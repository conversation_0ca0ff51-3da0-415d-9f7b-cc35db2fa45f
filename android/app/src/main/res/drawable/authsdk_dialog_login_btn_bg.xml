<?xml version="1.0" encoding="utf-8"?><!-- 按钮正常的时候的背景 -->
<!-- shape的默认形状是rectangle，还有oval(椭圆),line（线）,ring（圆环），我就用过rectangle,其他的大家可以试一试 -->

<shape xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 矩形的圆角弧度 -->
    <corners android:radius="25dp" />
    <!-- 矩形的填充色 -->
    <solid android:color="#1890FF" />

    <!-- 矩形的边框的宽度,每段虚线的长度，和两段虚线之间的颜色和颜色 -->
    <stroke
        android:width="0.5dp"
        android:dashWidth="0dp"
        android:dashGap="0dp"
        android:color="#3EB0FF" />
</shape>