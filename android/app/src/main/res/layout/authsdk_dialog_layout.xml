<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/authsdk_title_rl"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:gravity="center_vertical"
        android:background="#026ED2">

        <ImageButton
            android:id="@+id/authsdk_back_btn"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginLeft="12dp"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:src="@drawable/authsdk_return_bg" />

        <TextView
            android:id="@+id/authsdk_title_tv"
            android:layout_width="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginRight="30dp"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textSize="18dp"
            android:textColor="#ffffff" />

    </RelativeLayout>

    <ProgressBar
        android:id="@+id/authsdk_progressBar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="4dp"
        android:layout_gravity="top"
        android:visibility="gone" />

    <WebView
        android:id="@+id/authsdk_webview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fadingEdge="none"
        android:overScrollMode="never" />

</LinearLayout>
