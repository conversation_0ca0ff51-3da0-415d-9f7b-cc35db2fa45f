package com.sq.auth;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.webkit.WebSettings;
import com.sq.dlyz_flutter.R;

/**
 * 协议WebView Activity
 * 用于显示用户协议和隐私政策
 */
public class ProtocolWebActivity extends Activity {

    private static final String TAG = "ProtocolWebActivity";
    
    private WebView mWebView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        Log.d(TAG, "ProtocolWebActivity onCreate");
        
        // 设置布局
        setContentView(R.layout.activity_protocol_web);
        
        // 初始化视图
        initViews();
        
        // 配置WebView
        setupWebView();
        
        // 处理Intent数据
        handleIntent();
    }

    /**
     * 初始化视图
     */
    private void initViews() {
        mWebView = findViewById(R.id.webview);
    }

    /**
     * 配置WebView
     */
    @SuppressLint("SetJavaScriptEnabled")
    private void setupWebView() {
        // 设置WebView背景为透明
        mWebView.setBackgroundColor(0); // 透明背景
        mWebView.getBackground().setAlpha(0); // 设置背景透明度为0
        
        WebSettings settings = mWebView.getSettings();
        settings.setJavaScriptEnabled(true);
        settings.setDomStorageEnabled(true);
        settings.setLoadWithOverviewMode(true);
        settings.setUseWideViewPort(true);
        settings.setBuiltInZoomControls(false);
        settings.setDisplayZoomControls(false);
        settings.setSupportZoom(false);
        settings.setCacheMode(WebSettings.LOAD_DEFAULT);
        
        mWebView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                Log.d(TAG, "页面加载完成: " + url);
                // 页面加载完成后再次确保背景透明
                view.setBackgroundColor(0);
            }

            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
                Log.e(TAG, "页面加载错误: " + description);
            }
        });
    }

    /**
     * 处理Intent数据
     */
    private void handleIntent() {
        Intent intent = getIntent();
        if (intent != null) {
            String url = intent.getStringExtra("url");

            // 如果没有通过extra传递，尝试从Intent的data中获取
            if (TextUtils.isEmpty(url) && intent.getData() != null) {
                url = intent.getData().toString();
            }
            
            // 加载URL
            if (!TextUtils.isEmpty(url)) {
                Log.d(TAG, "加载协议页面: " + url);
                mWebView.loadUrl(url);
            }
        }
    }

    @Override
    public void onBackPressed() {
        if (mWebView != null && mWebView.canGoBack()) {
            mWebView.goBack();
        } else {
            super.onBackPressed();
        }
    }

    @Override
    protected void onDestroy() {
        if (mWebView != null) {
            mWebView.destroy();
        }
        super.onDestroy();
    }
}