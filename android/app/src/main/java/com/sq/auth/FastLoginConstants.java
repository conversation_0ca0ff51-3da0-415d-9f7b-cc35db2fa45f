package com.sq.auth;

/**
 * 闪验常量定义
 */
public class FastLoginConstants {
    
    public static class Code {
        public static final String FAILURE_REQUEST_CONFIG = "FAILURE_REQUEST_CONFIG";
        public static final String FAILURE_NOT_CONFIG = "FAILURE_NOT_CONFIG";
        public static final String FAILURE_PARSE_CONFIG = "FAILURE_PARSE_CONFIG";
        public static final String FAILURE_CONFIG_ERROR = "FAILURE_CONFIG_ERROR";
        public static final String FAILURE_NOT_SUPPORT = "FAILURE_NOT_SUPPORT";
        public static final String FAILURE_PARSE_ALI = "FAILURE_PARSE_ALI";
        public static final String FAILURE_PARSE_ALI_FAILURE = "FAILURE_PARSE_ALI_FAILURE";
        public static final String FAILURE_CLICK_OTHER_WAY = "FAILURE_CLICK_OTHER_WAY";
        public static final String FAILURE_CLICK_BACK = "FAILURE_CLICK_BACK";
        public static final String FAILURE_VERIFY_FAIL = "FAILURE_VERIFY_FAIL";
        public static final String CANCEL = "CANCEL";
    }
    
    public static class MESSAGE {
        public static final String FAILURE_REQUEST_CONFIG = "请求闪验配置失败";
        public static final String FAILURE_NOT_CONFIG = "闪验配置不可用";
        public static final String FAILURE_PARSE_CONFIG = "解析闪验配置失败";
        public static final String FAILURE_CONFIG_ERROR = "闪验配置异常";
        public static final String FAILURE_NOT_SUPPORT = "闪验不支持";
        public static final String FAILURE_PARSE_ALI = "解析阿里云结果失败";
        public static final String FAILURE_PARSE_ALI_FAILURE = "解析阿里云失败结果异常";
        public static final String FAILURE_CLICK_OTHER_WAY = "用户点击其他登录方式";
        public static final String FAILURE_CLICK_BACK = "用户点击返回";
        public static final String FAILURE_VERIFY_FAIL = "验证闪验token失败";
        public static final String CANCEL = "用户取消闪验";
    }
    
    public static class BundleKey {
        public static final String CODE = "CODE";
        public static final String MESSAGE = "MESSAGE";
    }
}