package com.sq.dlyz_flutter.push

import android.content.Context
import android.content.Intent
import android.text.TextUtils
import android.util.Log
import com.igexin.sdk.PushManager
import java.math.BigInteger
import java.nio.charset.StandardCharsets
import java.security.MessageDigest
import java.util.UUID

/**
 *
 * @author: zls
 * @date: 2025/8/12
 */
object SqPushHelper {

    private const val TAG = "SqPushHelper"

    fun sendFeedback(context: Context, intent: Intent?) {
        Log.d(TAG, "sendFeedback 开始发送回执")
        if (intent == null) {
            Log.d(TAG, "无效intent, 无法发送回执")
            return
        }
        pushClick(context, intent)
    }

    /**
     * 设置回执
     *
     * @param params task_id: 任务id(str), message_id: 信息id(str), type: 类型(1展示, 2点击)
     */
    fun sendFeedback(context: Context, params: Map<String?, String?>?) {
        if (params == null) {
            Log.w(TAG, "无效params, 无法发送回执")
            return
        }
        val taskId = params["task_id"] ?: ""
        val type = params["type"] ?: ""
        val action = if ("1" == type) {
            // 1: 展示
            "60001" // 60001表示个推渠道消息展示了
        } else if ("2" == type) {
            // 2: 点击
            "60002" // 60002表示个推渠道消息点击了
        } else {
            Log.w(TAG, "无指定回执类型, 无法发送回执, type=$type")
            return
        }
        var messageId = params["message_id"] ?: ""
        if (TextUtils.isEmpty(messageId)) {
            // 没有指定message id, 本地生成
            messageId = createMessageId(context, taskId) ?: ""
        }
        pushClick(context, taskId, action, messageId)
    }

    private fun pushClick(context: Context, intent: Intent) {
        // key是个推服务端下发的, 不能修改
        val taskId = intent.getStringExtra("gttask") ?: ""
        val action = intent.getStringExtra("gtaction") ?: ""
        if (TextUtils.isEmpty(taskId) || TextUtils.isEmpty(action)) {
            // 没有关键参数, 忽略
            Log.d(TAG, "pushClick 没有关键参数")
            return
        }

        // intent类型, 本地生成message id
        val messageId = createMessageId(context, taskId) ?: ""

        pushClick(context, taskId, action, messageId)
    }

    /**
     * https://docs.getui.com/getui/mobile/vendor/report/
     * action类型
     * 60020 华为点击
     * 60030 oppo点击
     * 60040 vivo点击
     * 60070 荣耀点击
     */
    private fun pushClick(
        context: Context,
        taskId: String,
        action: String,
        messageId: String
    ) {
        if (TextUtils.isEmpty(taskId)) {
            Log.w(TAG, "无效taskId, 无法发送回执")
            return
        }
        if (TextUtils.isEmpty(messageId)) {
            Log.w(TAG, "无效messageId, 无法发送回执")
            return
        }
        if (TextUtils.isEmpty(action)) {
            Log.w(TAG, "无效action, 无法发送回执")
            return
        }
        val actionId: Int
        try {
            actionId = action.toInt()
        } catch (e: Exception) {
            Log.w(TAG, "无效action, 无法发送回执: $action")
            return
        }

        Log.d(TAG, "发送回执: taskId=$taskId, action=$action, messageId=$messageId")
        val result =
            PushManager.getInstance().sendFeedbackMessage(context, taskId, messageId, actionId)
        Log.i(TAG, "发送回执结果: $result")
    }

    private fun createMessageId(context: Context, taskId: String): String? {
        try {
            val clientId = PushManager.getInstance().getClientid(context)
            val uuid = UUID.randomUUID().toString().replace("-".toRegex(), "")
            // 这里的message id需要自定义， 保证每条消息汇报的都不相同
            val contentToDigest = taskId + clientId + uuid
            val md5s = MessageDigest.getInstance("MD5").digest(
                contentToDigest.toByteArray(
                    StandardCharsets.UTF_8
                )
            )
            return BigInteger(1, md5s).toString(16)
        } catch (e: Exception) {
            Log.w(TAG, "生成message id失败", e)
            return null
        }
    }
}