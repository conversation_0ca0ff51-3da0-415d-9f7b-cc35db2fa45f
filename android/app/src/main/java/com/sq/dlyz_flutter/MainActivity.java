package com.sq.dlyz_flutter;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.sq.dlyz_flutter.flutterdownloader.FlutterDownloaderPlugin;
import com.sq.dlyz_flutter.flutterdownloader.FlutterDownloaderPluginKt;
import com.sq.dlyz_flutter.push.SqPushHelper;

import io.flutter.embedding.android.FlutterActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodChannel;

public class MainActivity extends FlutterActivity {

    private static final String TAG = "MainActivity";

    @Override
    public void configureFlutterEngine(FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);
        // 注册DouluoManagerChannel通信通道
        DouluoManagerChannel douluoManagerChannel = DouluoManagerChannel.getInstance(this);
        douluoManagerChannel.registerWith(flutterEngine.getDartExecutor().getBinaryMessenger(), this);
        douluoManagerChannel.setCurrentActivity(this);
        // 注册FlutterDownloaderPlugin通信通道
        FlutterDownloaderPlugin.registerWith(flutterEngine.getDartExecutor().getBinaryMessenger(), this);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 获取自定义透传参数值
        Intent intent = getIntent();
        // 上报华为、oppo、vivo、荣耀离线点击数据
        SqPushHelper.INSTANCE.sendFeedback(this, intent);
    }

    @Override
    protected void onNewIntent(@NonNull Intent intent) {
        super.onNewIntent(intent);
        handlerBindingCallback(intent);
        // 上报华为、oppo、vivo、荣耀离线点击数据
        SqPushHelper.INSTANCE.sendFeedback(this, intent);
    }

    /**
     * 处理游戏绑定回调
     * @param intent
     */
    private void handlerBindingCallback(Intent intent) {
        try {
            if (intent != null) {
                String bindingStatus = intent.getStringExtra("bindingStatus");
                if ("1".equals(bindingStatus)) {
                    String bindingRespData = intent.getStringExtra("bindingRespData");
                    Log.d(TAG, "Binding success, bindingRespData = " + bindingRespData);
                    DouluoManagerChannel.getInstance(this).onBindingSuccess(bindingRespData);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
