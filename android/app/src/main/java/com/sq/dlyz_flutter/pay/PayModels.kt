package com.sq.dlyz_flutter.pay

import org.json.JSONObject

/**
 * 通用支付请求参数
 * 支持JSON字符串和JSONObject两种形式，适应不同的服务端返回格式
 */
data class PayRequest(
    val paymentData: String = "",                // 支付数据（JSON字符串形式）
    val orderId: String = "",               // 订单ID（可选，用于日志和回调识别）
    val extraParams: Map<String, Any> = emptyMap() // 额外参数
) {

    /**
     * 获取支付数据的JSONObject形式
     */
    fun getPaymentDataAsJson(): JSONObject? {
        return try {
            JSONObject(paymentData)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 获取指定字段的值
     */
    fun getString(key: String, defaultValue: String = ""): String {
        return try {
            getPaymentDataAsJson()?.optString(key, defaultValue) ?: defaultValue
        } catch (e: Exception) {
            defaultValue
        }
    }
}

/**
 * 支付结果
 */
data class PayResult(
    val status: PayStatus,                  // 支付状态
    val orderId: String = "",               // 订单ID
    val message: String = "",               // 结果描述
    val extraData: Map<String, Any> = emptyMap() // 额外数据
)