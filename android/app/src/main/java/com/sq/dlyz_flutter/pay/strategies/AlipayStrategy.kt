package com.sq.dlyz_flutter.pay.strategies

import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.os.Handler
import android.os.Looper
import android.util.Log
import com.alipay.sdk.app.PayTask
import com.sq.dlyz_flutter.pay.*
import java.util.concurrent.Executors

/**
 * 支付宝支付实现
 */
class AlipayStrategy : IPayStrategy {
    
    companion object {
        private const val TAG = "AlipayStrategy"
        private const val ALIPAY_PACKAGE_NAME = "com.eg.android.AlipayGphone"
    }

    private val executor = Executors.newSingleThreadExecutor()
    private val mainHandler = Handler(Looper.getMainLooper())
    
    override fun getPaymentName(): String = "支付宝"
    
    override fun getPaymentType(): PaymentType = PaymentType.ALIPAY
    
    override fun pay(activity: Activity, payRequest: PayRequest, callback: PayCallback) {
        Log.d(TAG, "开始执行支付宝支付, 订单: ${payRequest.orderId}, paymentData = ${payRequest.paymentData}")
        // 在子线程中执行支付
        executor.execute {
            try {
                 val payResult = PayTask(activity).payV2(payRequest.paymentData, true)
                
                // 回到主线程执行回调
                mainHandler.post {
                    handlePayResult(payResult, payRequest, callback)
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "支付宝支付异常", e)
                mainHandler.post {
                    callback.onPayFailed(
                        PayResult(
                            status = PayStatus.FAILED,
                            orderId = payRequest.orderId,
                            message = "支付异常: ${e.message}"
                        )
                    )
                }
            }
        }
    }

    override fun isSupported(context: Context): Boolean {
        return true
    }
    
    override fun release() {
        executor.shutdown()
        Log.d(TAG, "支付宝策略资源已释放")
    }
    
    /**
     * 处理支付结果
     */
    private fun handlePayResult(
        resultMap: Map<String, String>,
        payRequest: PayRequest,
        callback: PayCallback
    ) {
        val resultStatus = resultMap["resultStatus"] ?: ""
        val memo = resultMap["memo"] ?: ""
        
        when (resultStatus) {
            "9000" -> {
                // 支付成功
                callback.onPaySuccess(
                    PayResult(
                        status = PayStatus.SUCCESS,
                        orderId = payRequest.orderId,
                        message = "支付成功"
                    )
                )
            }
            "6001" -> {
                // 用户取消
                callback.onPayCancelled(
                    PayResult(
                        status = PayStatus.CANCELLED,
                        orderId = payRequest.orderId,
                        message = "取消支付"
                    )
                )
            }
            else -> {
                // 支付失败
                callback.onPayFailed(
                    PayResult(
                        status = PayStatus.FAILED,
                        orderId = payRequest.orderId,
                        message = memo.ifEmpty { "支付失败" }
                    )
                )
            }
        }
    }
}
