package com.sq.dlyz_flutter.flutterdownloader

data class DownloadTask(
    var primaryId: Int,
    var taskId: String,
    var status: DownloadStatus,
    var progress: Int,
    var speed: Double = 0.0,
    var url: String,
    var filename: String?,
    var savedDir: String,
    var headers: String,
    var mimeType: String?,
    var resumable: Boolean,
    var showNotification: Boolean,
    var openFileFromNotification: Boolean,
    var timeCreated: Long,
    var saveInPublicStorage: <PERSON>olean,
    var allowCellular: Boolean
)
