package com.sq.dlyz_flutter.pay

/**
 * 支付回调接口
 */
interface PayCallback {
    /**
     * 支付成功
     */
    fun onPaySuccess(result: PayResult)
    
    /**
     * 支付失败
     */
    fun onPayFailed(result: PayResult)
    
    /**
     * 支付取消
     */
    fun onPayCancelled(result: PayResult)
}

/**
 * 简化的支付回调抽象类
 */
abstract class SimplePayCallback : PayCallback {
    override fun onPaySuccess(result: PayResult) {}
    override fun onPayFailed(result: PayResult) {}
    override fun onPayCancelled(result: PayResult) {}
}
