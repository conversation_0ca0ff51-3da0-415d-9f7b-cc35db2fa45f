# 登录路由架构优化修改方案

## 目标
简化架构层次和统一状态管理，提升代码可维护性和用户体验。

## 1. 新增文件

### 1.1 创建登录状态管理Provider
**文件路径**: `lib/providers/login_state_provider.dart`
**用途**: 统一管理登录流程状态机
**核心功能**:
- 登录状态枚举管理
- 登录流程状态转换
- 账号/手机/验证码登录逻辑
- 微信登录集成
- 验证码重发倒计时

```dart
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../services/user_storage_service.dart';
import '../services/wechat_login_service.dart';
import '../net/http_service.dart';
import 'user_provider.dart';

/// 登录状态枚举
enum LoginState {
  initial,           // 初始状态
  loading,           // 加载中
  accountLogin,      // 账号密码登录
  phoneLogin,        // 手机号登录
  verifyCode,        // 验证码输入
  wechatLogin,       // 微信登录中
  success,           // 登录成功
  error,             // 错误状态
}

/// 登录流程状态管理
class LoginStateProvider extends ChangeNotifier {
  LoginState _currentState = LoginState.initial;
  String? _errorMessage;
  String _currentPhoneNumber = '';
  bool _isLoading = false;
  int _resendCountdown = 0;

  // Getters
  LoginState get currentState => _currentState;
  String? get errorMessage => _errorMessage;
  String get currentPhoneNumber => _currentPhoneNumber;
  bool get isLoading => _isLoading;
  int get resendCountdown => _resendCountdown;

  /// 初始化登录流程
  Future<void> initializeLoginFlow() async {
    _setState(LoginState.loading);
    
    try {
      // 检查是否有历史账号来决定默认显示哪种登录方式
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      await userProvider.initialize();
      
      if (userProvider.userList.isNotEmpty) {
        _setState(LoginState.accountLogin);
      } else {
        _setState(LoginState.phoneLogin);
      }
    } catch (e) {
      _setError('初始化失败: ${e.toString()}');
    }
  }

  /// 切换到账号密码登录
  void switchToAccountLogin() {
    _clearError();
    _setState(LoginState.accountLogin);
  }

  /// 切换到手机号登录
  void switchToPhoneLogin() {
    _clearError();
    _setState(LoginState.phoneLogin);
  }

  /// 切换到验证码页面
  void switchToVerifyCode(String phoneNumber) {
    _currentPhoneNumber = phoneNumber;
    _setState(LoginState.verifyCode);
    _startResendCountdown();
  }

  /// 执行账号密码登录
  Future<bool> performAccountLogin({
    required String account,
    required String password,
    required UserProvider userProvider,
  }) async {
    _setLoading(true);
    
    try {
      // 这里调用实际的登录API
      final success = await _callAccountLoginAPI(account, password);
      
      if (success) {
        await userProvider.loginSuccess();
        _setState(LoginState.success);
        return true;
      } else {
        _setError('账号或密码错误');
        return false;
      }
    } catch (e) {
      _setError('登录失败: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 发送验证码
  Future<bool> sendVerificationCode(String phoneNumber) async {
    _setLoading(true);
    
    try {
      final success = await _callSendCodeAPI(phoneNumber);
      
      if (success) {
        _currentPhoneNumber = phoneNumber;
        _startResendCountdown();
        return true;
      } else {
        _setError('发送验证码失败');
        return false;
      }
    } catch (e) {
      _setError('发送验证码失败: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 验证码登录
  Future<bool> performCodeLogin({
    required String code,
    required UserProvider userProvider,
  }) async {
    _setLoading(true);
    
    try {
      final success = await _callCodeLoginAPI(_currentPhoneNumber, code);
      
      if (success) {
        await userProvider.loginSuccess();
        _setState(LoginState.success);
        return true;
      } else {
        _setError('验证码错误');
        return false;
      }
    } catch (e) {
      _setError('登录失败: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 微信登录
  Future<bool> performWechatLogin({
    required UserProvider userProvider,
  }) async {
    _setState(LoginState.wechatLogin);
    
    try {
      final success = await WechatLoginService.instance.login();
      
      if (success) {
        await userProvider.loginSuccess();
        _setState(LoginState.success);
        return true;
      } else {
        _setError('微信登录失败');
        return false;
      }
    } catch (e) {
      _setError('微信登录失败: ${e.toString()}');
      return false;
    }
  }

  /// 重新发送验证码
  Future<void> resendVerificationCode() async {
    if (_resendCountdown > 0) return;
    
    await sendVerificationCode(_currentPhoneNumber);
  }

  /// 重置登录流程
  void resetLoginFlow() {
    _currentState = LoginState.initial;
    _errorMessage = null;
    _currentPhoneNumber = '';
    _isLoading = false;
    _resendCountdown = 0;
    notifyListeners();
  }

  // Private methods
  void _setState(LoginState newState) {
    _currentState = newState;
    _errorMessage = null;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _currentState = LoginState.error;
    _isLoading = false;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _startResendCountdown() {
    _resendCountdown = 60;
    _countdownTimer();
  }

  void _countdownTimer() {
    if (_resendCountdown > 0) {
      Future.delayed(const Duration(seconds: 1), () {
        _resendCountdown--;
        notifyListeners();
        _countdownTimer();
      });
    }
  }

  // Mock API calls - 实际实现时替换为真实API
  Future<bool> _callAccountLoginAPI(String account, String password) async {
    await Future.delayed(const Duration(seconds: 1)); // 模拟网络延迟
    // 实际API调用逻辑
    return account.isNotEmpty && password.isNotEmpty;
  }

  Future<bool> _callSendCodeAPI(String phoneNumber) async {
    await Future.delayed(const Duration(seconds: 1));
    // 实际API调用逻辑
    return phoneNumber.length == 11;
  }

  Future<bool> _callCodeLoginAPI(String phoneNumber, String code) async {
    await Future.delayed(const Duration(seconds: 1));
    // 实际API调用逻辑
    return code.length == 6;
  }
}
```

### 1.2 创建统一登录页面
**文件路径**: `lib/pages/login/unified_login_page.dart`
**用途**: 合并原LoginPage和LoginManager功能
**核心功能**:
- 单页面管理所有登录方式
- 基于状态显示不同登录表单
- 统一的错误处理和加载状态
- 优化的用户交互体验

```dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../providers/login_state_provider.dart';
import '../../services/route_manager.dart';
import 'widgets/account_login_form.dart';
import 'widgets/phone_login_form.dart';
import 'widgets/verify_code_form.dart';

/// 统一登录页面 - 合并原LoginPage和LoginManager功能
class UnifiedLoginPage extends StatefulWidget {
  const UnifiedLoginPage({Key? key}) : super(key: key);

  @override
  State<UnifiedLoginPage> createState() => _UnifiedLoginPageState();
}

class _UnifiedLoginPageState extends State<UnifiedLoginPage> {
  @override
  void initState() {
    super.initState();
    
    // 设置状态栏样式
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
      ),
    );
    
    // 初始化登录状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final loginProvider = Provider.of<LoginStateProvider>(context, listen: false);
      loginProvider.initializeLoginFlow();
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, dynamic result) {
        if (!didPop) {
          _handleBackPressed();
        }
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        body: Consumer<LoginStateProvider>(
          builder: (context, loginProvider, child) {
            // 根据登录状态显示不同内容
            switch (loginProvider.currentState) {
              case LoginState.loading:
                return _buildLoadingView();
              
              case LoginState.accountLogin:
                return AccountLoginForm(
                  onSwitchToPhone: () => loginProvider.switchToPhoneLogin(),
                  onLoginSuccess: () => _handleLoginSuccess(),
                  onForgetPassword: () => loginProvider.switchToPhoneLogin(),
                );
              
              case LoginState.phoneLogin:
                return PhoneLoginForm(
                  onSwitchToAccount: () => loginProvider.switchToAccountLogin(),
                  onSendCode: (phone) => loginProvider.switchToVerifyCode(phone),
                  onLoginSuccess: () => _handleLoginSuccess(),
                );
              
              case LoginState.verifyCode:
                return VerifyCodeForm(
                  phoneNumber: loginProvider.currentPhoneNumber,
                  onBack: () => loginProvider.switchToPhoneLogin(),
                  onLoginSuccess: () => _handleLoginSuccess(),
                  onResendCode: () => loginProvider.resendVerificationCode(),
                );
              
              case LoginState.error:
                return _buildErrorView(loginProvider.errorMessage);
              
              default:
                return _buildLoadingView();
            }
          },
        ),
      ),
    );
  }

  Widget _buildLoadingView() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 20),
          Text(
            '正在初始化登录...',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(String? errorMessage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.red),
          SizedBox(height: 20),
          Text(
            errorMessage ?? '登录过程中出现错误',
            style: TextStyle(fontSize: 16, color: Colors.red),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 20),
          ElevatedButton(
            onPressed: () {
              final loginProvider = Provider.of<LoginStateProvider>(context, listen: false);
              loginProvider.initializeLoginFlow();
            },
            child: Text('重试'),
          ),
        ],
      ),
    );
  }

  void _handleLoginSuccess() {
    // 延迟跳转，确保UI状态更新完成
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        RouteManager.navigateAfterLogin(context);
      }
    });
  }

  void _handleBackPressed() {
    final loginProvider = Provider.of<LoginStateProvider>(context, listen: false);
    
    // 如果在验证码页面，返回到手机登录
    if (loginProvider.currentState == LoginState.verifyCode) {
      loginProvider.switchToPhoneLogin();
      return;
    }
    
    // 其他情况显示退出确认
    _showExitConfirmation();
  }

  void _showExitConfirmation() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('提示'),
          content: const Text('您还未登录，请选择继续登录或退出应用'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('继续登录'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                SystemNavigator.pop();
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('退出应用'),
            ),
          ],
        );
      },
    );
  }
}
```

### 1.3 创建登录表单组件
**文件路径**: `lib/pages/login/widgets/account_login_form.dart`
**用途**: 账号密码登录表单组件

```dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../providers/login_state_provider.dart';
import '../../../providers/user_provider.dart';

class AccountLoginForm extends StatefulWidget {
  final VoidCallback? onSwitchToPhone;
  final VoidCallback? onLoginSuccess;
  final VoidCallback? onForgetPassword;

  const AccountLoginForm({
    Key? key,
    this.onSwitchToPhone,
    this.onLoginSuccess,
    this.onForgetPassword,
  }) : super(key: key);

  @override
  State<AccountLoginForm> createState() => _AccountLoginFormState();
}

class _AccountLoginFormState extends State<AccountLoginForm> {
  final _formKey = GlobalKey<FormState>();
  final _accountController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '账号登录',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 40),
            TextFormField(
              controller: _accountController,
              decoration: const InputDecoration(
                labelText: '账号',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入账号';
                }
                return null;
              },
            ),
            const SizedBox(height: 20),
            TextFormField(
              controller: _passwordController,
              obscureText: _obscurePassword,
              decoration: InputDecoration(
                labelText: '密码',
                border: const OutlineInputBorder(),
                suffixIcon: IconButton(
                  icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                  onPressed: () {
                    setState(() {
                      _obscurePassword = !_obscurePassword;
                    });
                  },
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入密码';
                }
                return null;
              },
            ),
            const SizedBox(height: 30),
            Consumer<LoginStateProvider>(
              builder: (context, loginProvider, child) {
                return SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: loginProvider.isLoading ? null : _handleLogin,
                    child: loginProvider.isLoading
                        ? const CircularProgressIndicator()
                        : const Text('登录'),
                  ),
                );
              },
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton(
                  onPressed: widget.onForgetPassword,
                  child: const Text('忘记密码？'),
                ),
                TextButton(
                  onPressed: widget.onSwitchToPhone,
                  child: const Text('手机号登录'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;

    final loginProvider = Provider.of<LoginStateProvider>(context, listen: false);
    final userProvider = Provider.of<UserProvider>(context, listen: false);

    final success = await loginProvider.performAccountLogin(
      account: _accountController.text,
      password: _passwordController.text,
      userProvider: userProvider,
    );

    if (success) {
      widget.onLoginSuccess?.call();
    }
  }

  @override
  void dispose() {
    _accountController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
}
```

**文件路径**: `lib/pages/login/widgets/phone_login_form.dart`
**用途**: 手机号登录表单组件

```dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../providers/login_state_provider.dart';

class PhoneLoginForm extends StatefulWidget {
  final VoidCallback? onSwitchToAccount;
  final Function(String)? onSendCode;
  final VoidCallback? onLoginSuccess;

  const PhoneLoginForm({
    Key? key,
    this.onSwitchToAccount,
    this.onSendCode,
    this.onLoginSuccess,
  }) : super(key: key);

  @override
  State<PhoneLoginForm> createState() => _PhoneLoginFormState();
}

class _PhoneLoginFormState extends State<PhoneLoginForm> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '手机号登录',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 40),
            TextFormField(
              controller: _phoneController,
              keyboardType: TextInputType.phone,
              decoration: const InputDecoration(
                labelText: '手机号',
                border: OutlineInputBorder(),
                prefixText: '+86 ',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入手机号';
                }
                if (value.length != 11) {
                  return '请输入11位手机号';
                }
                return null;
              },
            ),
            const SizedBox(height: 30),
            Consumer<LoginStateProvider>(
              builder: (context, loginProvider, child) {
                return SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: loginProvider.isLoading ? null : _handleSendCode,
                    child: loginProvider.isLoading
                        ? const CircularProgressIndicator()
                        : const Text('发送验证码'),
                  ),
                );
              },
            ),
            const SizedBox(height: 20),
            TextButton(
              onPressed: widget.onSwitchToAccount,
              child: const Text('账号密码登录'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleSendCode() async {
    if (!_formKey.currentState!.validate()) return;

    final loginProvider = Provider.of<LoginStateProvider>(context, listen: false);
    final success = await loginProvider.sendVerificationCode(_phoneController.text);

    if (success) {
      widget.onSendCode?.call(_phoneController.text);
    }
  }

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }
}
```

**文件路径**: `lib/pages/login/widgets/verify_code_form.dart`
**用途**: 验证码输入表单组件

```dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../providers/login_state_provider.dart';
import '../../../providers/user_provider.dart';

class VerifyCodeForm extends StatefulWidget {
  final String phoneNumber;
  final VoidCallback? onBack;
  final VoidCallback? onLoginSuccess;
  final VoidCallback? onResendCode;

  const VerifyCodeForm({
    Key? key,
    required this.phoneNumber,
    this.onBack,
    this.onLoginSuccess,
    this.onResendCode,
  }) : super(key: key);

  @override
  State<VerifyCodeForm> createState() => _VerifyCodeFormState();
}

class _VerifyCodeFormState extends State<VerifyCodeForm> {
  final _formKey = GlobalKey<FormState>();
  final _codeController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '验证码登录',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 20),
            Text(
              '验证码已发送至 ${widget.phoneNumber}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 40),
            TextFormField(
              controller: _codeController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: '验证码',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入验证码';
                }
                if (value.length != 6) {
                  return '请输入6位验证码';
                }
                return null;
              },
            ),
            const SizedBox(height: 30),
            Consumer<LoginStateProvider>(
              builder: (context, loginProvider, child) {
                return SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: loginProvider.isLoading ? null : _handleLogin,
                    child: loginProvider.isLoading
                        ? const CircularProgressIndicator()
                        : const Text('登录'),
                  ),
                );
              },
            ),
            const SizedBox(height: 20),
            Consumer<LoginStateProvider>(
              builder: (context, loginProvider, child) {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextButton(
                      onPressed: widget.onBack,
                      child: const Text('返回'),
                    ),
                    TextButton(
                      onPressed: loginProvider.resendCountdown > 0
                          ? null
                          : widget.onResendCode,
                      child: Text(
                        loginProvider.resendCountdown > 0
                            ? '重发(${loginProvider.resendCountdown}s)'
                            : '重新发送',
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;

    final loginProvider = Provider.of<LoginStateProvider>(context, listen: false);
    final userProvider = Provider.of<UserProvider>(context, listen: false);

    final success = await loginProvider.performCodeLogin(
      code: _codeController.text,
      userProvider: userProvider,
    );

    if (success) {
      widget.onLoginSuccess?.call();
    }
  }

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }
}
```

### 1.4 增强用户状态管理
**文件路径**: `lib/providers/enhanced_user_provider.dart`
**用途**: 增强现有UserProvider功能
**核心功能**:
- 用户会话管理
- 历史账号管理
- 用户切换功能
- 本地存储集成

```dart
import 'package:flutter/foundation.dart';
import '../services/user_storage_service.dart';
import '../models/user_model.dart';

/// 增强的用户状态管理
class EnhancedUserProvider extends ChangeNotifier {
  UserModel? _currentUser;
  List<UserModel> _userList = [];
  bool _isLoggedIn = false;
  bool _isInitialized = false;

  // Getters
  UserModel? get currentUser => _currentUser;
  List<UserModel> get userList => _userList;
  bool get isLoggedIn => _isLoggedIn;
  bool get isInitialized => _isInitialized;

  /// 初始化用户数据
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // 从本地存储加载用户数据
      _currentUser = await UserStorageService.getCurrentUser();
      _userList = await UserStorageService.getUserList();
      _isLoggedIn = _currentUser != null && _currentUser!.isValidSession();
      
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('用户数据初始化失败: $e');
      _isInitialized = true;
      notifyListeners();
    }
  }

  /// 登录成功处理
  Future<void> loginSuccess({UserModel? user}) async {
    if (user != null) {
      _currentUser = user;
      await UserStorageService.saveCurrentUser(user);
      await _updateUserList(user);
    }
    
    _isLoggedIn = true;
    notifyListeners();
  }

  /// 退出登录
  Future<void> logout() async {
    _currentUser = null;
    _isLoggedIn = false;
    
    await UserStorageService.clearCurrentUser();
    notifyListeners();
  }

  /// 更新用户信息
  Future<void> updateUserInfo(UserModel user) async {
    _currentUser = user;
    await UserStorageService.saveCurrentUser(user);
    await _updateUserList(user);
    notifyListeners();
  }

  /// 切换用户账号
  Future<void> switchUser(UserModel user) async {
    _currentUser = user;
    _isLoggedIn = user.isValidSession();
    
    await UserStorageService.saveCurrentUser(user);
    notifyListeners();
  }

  /// 删除历史账号
  Future<void> removeUser(String userId) async {
    _userList.removeWhere((user) => user.id == userId);
    await UserStorageService.saveUserList(_userList);
    notifyListeners();
  }

  // Private methods
  Future<void> _updateUserList(UserModel user) async {
    // 移除已存在的相同用户
    _userList.removeWhere((u) => u.id == user.id);
    // 添加到列表开头
    _userList.insert(0, user);
    // 限制历史账号数量
    if (_userList.length > 5) {
      _userList = _userList.take(5).toList();
    }
    
    await UserStorageService.saveUserList(_userList);
  }
}
```

### 1.5 增强路由管理器
**文件路径**: `lib/services/enhanced_route_manager.dart`
**用途**: 替代现有RouteManager
**核心功能**:
- 基于登录状态的智能路由
- 模态登录页面支持
- 优化的页面转场

```dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../main.dart';
import '../pages/login/unified_login_page.dart';
import '../providers/login_state_provider.dart';
import '../providers/enhanced_user_provider.dart';

/// 增强的路由管理器
class EnhancedRouteManager {
  /// 检查登录状态并跳转到相应页面
  static Future<void> navigateBasedOnLoginState(BuildContext context) async {
    final userProvider = Provider.of<EnhancedUserProvider>(context, listen: false);
    
    if (!userProvider.isInitialized) {
      await userProvider.initialize();
    }
    
    if (userProvider.isLoggedIn) {
      navigateToHome(context);
    } else {
      navigateToLogin(context);
    }
  }

  /// 跳转到登录页
  static void navigateToLogin(BuildContext context) {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider(
          create: (context) => LoginStateProvider(),
          child: const UnifiedLoginPage(),
        ),
      ),
    );
  }

  /// 跳转到主页
  static void navigateToHome(BuildContext context) {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (context) => const MyHomePage()),
    );
  }

  /// 登录成功后的跳转
  static void navigateAfterLogin(BuildContext context) {
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const MyHomePage()),
      (route) => false,
    );
  }

  /// 退出登录后的跳转
  static Future<void> navigateAfterLogout(BuildContext context) async {
    final userProvider = Provider.of<EnhancedUserProvider>(context, listen: false);
    await userProvider.logout();
    
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider(
          create: (context) => LoginStateProvider(),
          child: const UnifiedLoginPage(),
        ),
      ),
      (route) => false,
    );
  }

  /// 显示登录页面（用于需要登录验证的场景）
  static Future<bool?> showLoginModal(
    BuildContext context, {
    VoidCallback? onLoginSuccess,
  }) async {
    return showModalBottomSheet<bool>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ChangeNotifierProvider(
        create: (context) => LoginStateProvider(),
        child: Container(
          height: MediaQuery.of(context).size.height * 0.9,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: const UnifiedLoginPage(),
        ),
      ),
    );
  }
}
```

## 2. 修改文件

### 2.1 更新应用入口
**文件路径**: `lib/main.dart`
**修改内容**:
```dart
// 在MaterialApp上方添加Provider
MultiProvider(
  providers: [
    ChangeNotifierProvider(create: (_) => EnhancedUserProvider()),
    // 其他existing providers...
  ],
  child: MaterialApp(...)
)
```

### 2.2 更新启动页面
**文件路径**: `lib/pages/splash/SplashPage.dart`
**修改内容**:
```dart
// 在_navigateWithAnimation方法中替换路由逻辑
void _navigateWithAnimation() async {
  // 延迟后使用增强路由管理器
  await Future.delayed(const Duration(seconds: 3));
  if (mounted) {
    EnhancedRouteManager.navigateBasedOnLoginState(context);
  }
}
```

### 2.3 更新设置页面退出登录
**文件路径**: `lib/pages/profile/ProfilePage.dart`
**修改内容**:
```dart
// 在退出登录按钮点击事件中
onTap: () async {
  await EnhancedRouteManager.navigateAfterLogout(context);
}
```

## 3. 重构文件

### 3.1 简化现有登录页面
**文件路径**: `lib/pages/login/account_login_page.dart`
**重构为**: `lib/pages/login/widgets/account_login_form.dart`
**变更**: 从独立页面改为可复用表单组件

**文件路径**: `lib/pages/login/phone_login_page.dart`
**重构为**: `lib/pages/login/widgets/phone_login_form.dart`
**变更**: 从独立页面改为可复用表单组件

**文件路径**: `lib/pages/login/verify_code_page.dart`
**重构为**: `lib/pages/login/widgets/verify_code_form.dart`
**变更**: 从独立页面改为可复用表单组件

## 4. 废弃文件

### 4.1 标记为废弃（暂不删除）
**文件路径**: `lib/pages/auth/login_page.dart`
**原因**: 被unified_login_page.dart替代

**文件路径**: `lib/pages/login/login_manager.dart`
**原因**: 功能合并到login_state_provider.dart

**文件路径**: `lib/services/route_manager.dart`
**原因**: 被enhanced_route_manager.dart替代

**文件路径**: `lib/pages/login/login_dialog_account.dart`
**原因**: 不再需要对话框形式，改为全屏页面

## 5. 依赖更新

### 5.1 pubspec.yaml
确保以下依赖版本：
```yaml
dependencies:
  provider: ^6.0.5
  # 其他existing dependencies...
```

## 6. 实施步骤

### 阶段1: 创建新组件 (1-2天)
1. 创建`login_state_provider.dart`
2. 创建`enhanced_user_provider.dart`
3. 创建`enhanced_route_manager.dart`
4. 单元测试新Provider功能

### 阶段2: 创建新UI组件 (2-3天)
1. 创建登录表单组件(account_login_form, phone_login_form, verify_code_form)
2. 创建`unified_login_page.dart`
3. 集成测试登录流程

### 阶段3: 集成现有系统 (1-2天)
1. 更新`main.dart`添加新Provider
2. 更新`SplashPage.dart`使用新路由逻辑
3. 更新`ProfilePage.dart`退出登录逻辑
4. 端到端测试完整流程

### 阶段4: 清理和优化 (1天)
1. 移除或重构旧文件
2. 代码审查和优化
3. 文档更新

## 7. 测试计划

### 7.1 单元测试
- LoginStateProvider状态转换测试
- EnhancedUserProvider用户管理测试
- 登录表单组件功能测试

### 7.2 集成测试
- 完整登录流程测试
- 页面跳转逻辑测试
- 错误处理场景测试

### 7.3 UI测试
- 各种登录方式UI测试
- 响应式布局测试
- 用户交互体验测试

## 8. 风险评估

### 8.1 低风险
- 新组件创建不影响现有功能
- 渐进式替换，可随时回滚

### 8.2 中风险
- Provider状态管理变更需要仔细测试
- 路由逻辑变更需要全面测试

### 8.3 缓解措施
- 保留旧文件作为备份
- 分阶段实施，每阶段充分测试
- 准备快速回滚方案

## 9. 预期收益

### 9.1 代码质量
- 减少50%的登录相关代码复杂度
- 提升代码可维护性和可测试性
- 统一的错误处理和状态管理

### 9.2 用户体验
- 更流畅的登录流程转换
- 更好的错误提示和加载状态
- 减少页面跳转次数

### 9.3 开发效率
- 更容易添加新的登录方式
- 更容易调试和修复问题
- 更清晰的代码结构

## 10. 注意事项

1. **向后兼容**: 确保新系统与现有微信登录等功能兼容
2. **数据迁移**: 用户历史登录信息需要平滑迁移
3. **错误处理**: 网络异常、API错误等场景需要完善处理
4. **性能考虑**: 状态管理器不要过度notify，避免不必要的重建
5. **安全性**: 登录状态和用户信息的本地存储安全性