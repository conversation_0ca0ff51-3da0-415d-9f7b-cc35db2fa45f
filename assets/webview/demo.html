<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebView JS通信演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        .card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            backdrop-filter: blur(10px);
        }
        button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 5px;
            transition: background 0.3s;
        }
        button:hover {
            background: #0056cc;
        }
        .log {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
        }
        input {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 5px;
            margin: 5px 0;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🚀 WebView JS通信演示</h1>
            <p>这是一个演示Flutter WebView与JavaScript通信的页面</p>
        </div>

        <div class="card">
            <h3>📱 Flutter通信测试</h3>
            <button onclick="sendMessageToFlutter('hello')">发送Hello消息</button>
            <button onclick="sendMessageToFlutter('getData')">请求数据</button>
            <button onclick="getDeviceInfo()">获取设备信息</button>
            <button onclick="changeTitle()">更改页面标题</button>
        </div>

        <div class="card">
            <h3>📋 自定义消息</h3>
            <input type="text" id="customMessage" placeholder="输入自定义消息..." />
            <button onclick="sendCustomMessage()">发送自定义消息</button>
        </div>

        <div class="card">
            <h3>📊 消息日志</h3>
            <div id="log" class="log">等待消息...</div>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="card">
            <h3>🎮 页面操作</h3>
            <button onclick="openUrl()">打开浏览器</button>
            <button onclick="showToast('这是来自WebView的提示!')">显示提示</button>
            <button onclick="navigateToNewPage()">打开新页面</button>
            <button onclick="closePage()">关闭页面</button>
            <button onclick="isHasPermission()">是否有通知权限</button>
            <button onclick="requestPermission()">申请通知权限</button>
            <button onclick="showGameBindingDialog()">展示游戏绑定dialog</button>
            <button onclick="getBindingRoleData()">获取游戏绑定角色数据</button>
            <button onclick="pay()">支付</button>
            <button onclick="jumpGameBindingPage()">跳转游戏绑定页</button>
        </div>
    </div>

    <script>
        let messageCount = 0;

        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '日志已清空<br>';
        }

        function sendMessageToFlutter(type) {
            messageCount++;
            const message = {
                type: 'userAction',
                method: type,
                data: {
                    message: `来自WebView的消息 #${messageCount}`,
                    timestamp: Date.now(),
                    userAgent: navigator.userAgent
                },
                id: `msg_${messageCount}`
            };

            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage(message);
                log(`发送消息: ${type}`);
            } else {
                log('错误: FlutterJSBridge 未找到');
            }
        }

        function sendCustomMessage() {
            const input = document.getElementById('customMessage');
            const message = input.value.trim();
            if (!message) {
                alert('请输入消息内容');
                return;
            }

            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage({
                    type: 'custom',
                    method: 'customMessage',
                    data: { message: message },
                    id: `custom_${Date.now()}`
                });
                log(`发送自定义消息: ${message}`);
                input.value = '';
            } else {
                log('错误: FlutterJSBridge 未找到');
            }
        }

        function getDeviceInfo() {
            log("jho web调用了getDeviceInfo");
            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage(JSON.stringify({
                    method: 'getDeviceInfo',
                    data: {  }
                }));
                log('请求设备信息...');
            } else {
                log('错误: FlutterJSBridge 未找到');
            }
        }

        function changeTitle() {
            const newTitle = `新标题 ${Date.now()}`;
            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage(JSON.stringify({
                    method: 'setTitle',
                    data: {
                        "title": "newTitle"
                    }
                }));
                log(`更改标题: ${newTitle}`);
            }
        }

        function showToast(message) {
            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage(JSON.stringify({
                    method: 'showToast',
                    data: { 
                        "message": "123456",
                        "duration": 2000
                    }
                }));
                log(`显示提示: ${message}`);
            }
        }

        function navigateToNewPage() {
            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage(JSON.stringify({
                    method: 'navigate',
                    data: { url: 'https://flutter.dev' }
                }));
                log('请求导航到新页面');
            }
        }

        function closePage() {
            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage(JSON.stringify({
                    method: 'close',
                    data: { }
                }));
                log('请求关闭页面');
            }
        }

        function isHasPermission() {
            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage(JSON.stringify({
                    method: 'isHasPermission',
                    data: {
                        "permissionName": "notification",
                        "callback": "callback"
                    }
                }));
                log('查询权限');
            }
        }

        function requestPermission() {
            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage(JSON.stringify({
                    method: 'requestPermission',
                    data: {
                        "permissionName": "notification",
                        "callback": "callback"
                    }
                }));
                log('申请权限');
            }
        }

        function showGameBindingDialog() {
            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage(JSON.stringify({
                    method: 'showGameBindingDialog',
                    data: {
                        "callback": "callback"
                    }
                }));
                log('展示游戏绑定dialog');
            }
        }

        function getBindingRoleData() {
            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage(JSON.stringify({
                    method: 'getBindingRoleData',
                    data: {
                        'callback': 'callback'
                    }
                }));
                log('获取角色绑定列表');
            }
        }

        function pay(result) {
            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage(JSON.stringify({
                    method: 'pay',
                    data: {
                        'payType': 'alipay',
                        'uuid': '123456',
                        'trade': 'alipay_sdk=alipay-sdk-java-4.40.133.ALL&app_id=2021004136690593&biz_content=%7B%22extend_params%22%3A%7B%22royalty_freeze%22%3A%22true%22%2C%22sys_service_provider_id%22%3A%222088441791061045%22%7D%2C%22out_trade_no%22%3A%2220250820190455210883%22%2C%22settle_info%22%3A%7B%22settle_detail_infos%22%3A%5B%7B%22amount%22%3A0.01%2C%22trans_in_type%22%3A%22defaultSettle%22%7D%5D%7D%2C%22sub_merchant%22%3A%7B%22merchant_id%22%3A%222088450898951700%22%7D%2C%22subject%22%3A%22%E6%B8%B8%E6%88%8F%E5%85%85%E5%80%BC%22%2C%22total_amount%22%3A%220.01%22%7D&charset=UTF-8&format=json&method=alipay.trade.app.pay&notify_url=new-trade.51fubei.com%2FalipayYzt%2FpayCallback&sign=pMqqurvbSmPYg%2BOy2kac7LbngXQD7xSp12bHDVlLJmjKcz%2BFDXdKLqZutApExURMWFZKeErENLZl%2BRuIuBp8y4F5x59qk8pG3wV1gTRXriYUNkkyTW6XKMJ9EiFb1YtOzqxBXV5LBj7DOfoXEgVuV8eqinyRefi1LgyrkjwptluxSYq5FJ6h2JsU8%2BQYYOLluNDWdpeqzd8xFDMHs8vh3eK6c%2F6UOfgTBBsEJ%2FNEwi26DQ0gi3%2F8A7loBd%2B04wIdNmu2bMvpXkbD%2F8ENHWFWPrXCWAIME6L6uxKQqgOP8%2FCdYKqwbiL%2BPvmFoFC53ddR8xMU6f%2FjCZ6BFojNLXz3oA%3D%3D&sign_type=RSA2&timestamp=2025-08-20+19%3A04%3A55&version=1.0',
                        'callback': 'callback'
                    }
                }));
                log('调用支付');
            }
        }

        function callback(result) {
            alert('回调结果: ' + JSON.stringify(result));
        }

        function openUrl() {
            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage(JSON.stringify({
                    method: 'openActionBrowser',
                    data: { url: 'https://flutter.dev' }
                }));
                log('请求打开浏览器');
            }
        }

        function jumpGameBindingPage() {
            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage(JSON.stringify({
                    method: 'jumpGameBindingPage',
                    data: {
                        'callback': 'callback'
                    }
                }));
                log('跳转游戏绑定页');
            }
        }

        // 监听来自Flutter的消息
        window.onFlutterMessage = function(message) {
            log(`收到Flutter消息: ${JSON.stringify(message)}`);
        };

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，等待Flutter桥接...');
        });

        // 监听桥接就绪
        window.addEventListener('flutterReady', function() {
            log('Flutter桥接已就绪!');
        });
    </script>
</body>
</html> 