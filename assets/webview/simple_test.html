<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单WebView测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #a8e6cf 0%, #dcedc1 100%);
            color: #2d3436;
            text-align: center;
            min-height: 100vh;
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
        }
        h1 {
            color: #00b894;
            margin-bottom: 10px;
        }
        p {
            color: #636e72;
            margin-bottom: 30px;
        }
        button {
            background: #00b894;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
        }
        button:hover {
            background: #00a085;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 184, 148, 0.4);
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #74b9ff;
            color: white;
            border-radius: 10px;
            display: none;
        }
        .info {
            background: #e17055;
            color: white;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 简单测试</h1>
        <p>这是一个轻量级的WebView测试页面</p>
        
        <button onclick="testBasic()">基础测试</button>
        <button onclick="testBridge()">JS桥接测试</button>
        <button onclick="testAlert()">原生弹窗</button>
        
        <div id="result" class="result"></div>
        
        <div class="info">
            💡 提示：点击按钮测试不同功能
        </div>
    </div>

    <script>
        let testCount = 0;

        function showResult(message, isSuccess = true) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.style.backgroundColor = isSuccess ? '#00b894' : '#e17055';
            resultDiv.style.display = 'block';
            
            setTimeout(() => {
                resultDiv.style.display = 'none';
            }, 3000);
        }

        function testBasic() {
            testCount++;
            const timestamp = new Date().toLocaleTimeString();
            showResult(`基础测试 #${testCount} 完成 (${timestamp})`);
            console.log('基础测试执行完成');
        }

        function testBridge() {
            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage({
                    type: 'userAction',
                    method: 'simpleTest',
                    data: {
                        message: '简单桥接测试',
                        testNumber: ++testCount,
                        timestamp: Date.now()
                    }
                });
                showResult('JS桥接消息已发送');
            } else {
                showResult('FlutterJSBridge 未找到', false);
            }
        }

        function testAlert() {
            alert('这是一个原生JavaScript弹窗');
            showResult('原生弹窗测试完成');
        }

        // 监听来自Flutter的消息
        window.onFlutterMessage = function(message) {
            console.log('收到Flutter消息:', message);
            showResult(`收到Flutter响应: ${JSON.stringify(message)}`);
        };

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('简单测试页面加载完成');
        });

        // 自动检测桥接状态
        setTimeout(() => {
            if (window.FlutterJSBridge) {
                console.log('Flutter桥接可用');
            } else {
                console.log('Flutter桥接不可用');
            }
        }, 1000);
    </script>
</body>
</html> 