group 'io.flutter.plugins.videoplayer'
version '1.0-SNAPSHOT'

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.5.0'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'

android {
    namespace 'io.flutter.plugins.videoplayer'
    compileSdk = flutter.compileSdkVersion

    defaultConfig {
        minSdkVersion 21
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    lintOptions {
        checkAllWarnings true
        warningsAsErrors true
        disable 'AndroidGradlePluginVersion', 'InvalidPackage', 'GradleDependency', 'NewerVersionAvailable'
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    dependencies {
        def exoplayer_version = "1.5.1"
        implementation "androidx.media3:media3-exoplayer:${exoplayer_version}"
        implementation "androidx.media3:media3-exoplayer-hls:${exoplayer_version}"
        implementation "androidx.media3:media3-exoplayer-dash:${exoplayer_version}"
        implementation "androidx.media3:media3-exoplayer-rtsp:${exoplayer_version}"
        implementation "androidx.media3:media3-exoplayer-smoothstreaming:${exoplayer_version}"
        testImplementation 'junit:junit:4.13.2'
        testImplementation 'androidx.test:core:1.7.0'
        testImplementation 'org.mockito:mockito-inline:5.2.0'
        testImplementation 'org.robolectric:robolectric:4.15.1'
        testImplementation "androidx.media3:media3-test-utils:${exoplayer_version}"
    }

    testOptions {
        unitTests.includeAndroidResources = true
        unitTests.returnDefaultValues = true
        unitTests.all {
            // The org.gradle.jvmargs property that may be set in gradle.properties does not impact
            // the Java heap size when running the Android unit tests. The following property here
            // sets the heap size to a size large enough to run the robolectric tests across
            // multiple SDK levels.
            jvmArgs "-Xmx4G"
            testLogging {
               events "passed", "skipped", "failed", "standardOut", "standardError"
               outputs.upToDateWhen {false}
               showStandardStreams = true
            }
        }
    }
}
