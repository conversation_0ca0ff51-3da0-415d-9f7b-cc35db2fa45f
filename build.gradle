group 'cn.thinkingdata.flutter'
version '1.0'

buildscript {
    repositories {
        google()
        jcenter()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.3.0'
    }
}

rootProject.allprojects {
    repositories {
        google()
        jcenter()
    }
}

apply plugin: 'com.android.library'

android {
    if (project.android.hasProperty("namespace")) {
        namespace 'cn.thinkingdata.flutter'
    }
    compileSdkVersion 32

    defaultConfig {
        minSdkVersion 19
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    lintOptions {
        disable 'InvalidPackage'
    }
}

dependencies {
    implementation 'cn.thinkingdata.android:ThinkingAnalyticsSDK:3.1.1'
    implementation 'cn.thinkingdata.android:TAThirdParty:2.0.2'
}


allprojects {
    gradle.projectsEvaluated {
        tasks.withType(JavaCompile) {
//            options.compilerArgs << "-Xlint:unchecked" << "-Xlint:deprecation"
            options.compilerArgs << "-Xlint:deprecation"
        }
    }
}
