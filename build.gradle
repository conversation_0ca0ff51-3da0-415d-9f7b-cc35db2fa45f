import org.yaml.snakeyaml.Yaml

group 'com.jarvan.fluwx'
version '1.0-SNAPSHOT'

Map projectYaml = loadPubspec()

buildscript {
    ext.kotlin_version = '1.8.22'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath "com.android.tools.build:gradle:8.3.2"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "org.yaml:snakeyaml:2.0"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
android {
    if (project.android.hasProperty("namespace")) {
        namespace 'com.jarvan.fluwx'
    }

    compileSdk 34

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = '11'
    }

    sourceSets {
        main.java.srcDirs += ['src/main/kotlin', "${buildDir}/generated/src/kotlin"]
        test.java.srcDirs += 'src/test/kotlin'
    }

    defaultConfig {
        minSdkVersion 19
        consumerProguardFiles 'consumer-proguard-rules.txt'
    }

    dependencies {
        api 'com.tencent.mm.opensdk:wechat-sdk-android:6.8.34'
        implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0'
        implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0'
        implementation 'id.zelory:compressor:3.0.1'
        implementation 'com.squareup.okhttp3:okhttp:4.12.0'
        testImplementation 'org.jetbrains.kotlin:kotlin-test'
        testImplementation 'org.mockito:mockito-core:5.0.0'
    }

    testOptions {
        unitTests.all {
            useJUnitPlatform()

            testLogging {
                events "passed", "skipped", "failed", "standardOut", "standardError"
                outputs.upToDateWhen { false }
                showStandardStreams = true
            }
        }
    }
}

Map loadPubspec() {
    File pubspecFile
    def yamlDir = rootProject.hasProperty('yamlDir') ? rootProject.ext.yamlDir : ''
    def pubspecPath = rootProject.projectDir.parent + File.separator +yamlDir +"pubspec.yaml"
    if(file(pubspecPath).exists()) {
        pubspecFile = new File(pubspecPath)
    } else {
        def currentGradleFileDir = file(".").absolutePath // 当前 build.gradle 所在目录
        def parentDir = new File(currentGradleFileDir).getParentFile() // 获取父目录
        pubspecFile = new File(parentDir, "pubspec.yaml") // 构建 pubspec.yaml 路径
    }


    InputStream input = new FileInputStream(pubspecFile)
    Yaml yaml = new Yaml()
    Map projectConfig = yaml.load(input)

    return projectConfig
}



tasks.register("generateFluwxHelperFile") {
   doFirst {
       Map config = loadPubspec()
       Map fluwx = (Map) config.get("fluwx")
       String enableLogging = "false"
       String interruptWeChatRequestByFluwx = "true"
       String flutterActivity = ""
       if (fluwx) {
           Map android = (Map) fluwx.get("android")
           if (android) {
               def iwr = android.get("interrupt_wx_request")
               if (iwr && iwr == "true" || iwr == "false") {
                   interruptWeChatRequestByFluwx = (String)
               }

               def activity = android.get("flutter_activity")
               if (activity) {
                   flutterActivity = (String) activity
               }
           }

           def logging = fluwx.get("debug_logging")
           if ("${logging}" == "true" || "${logging}" == "false") {
               enableLogging = "${logging}"
           }
       }

       generateFluwxConfigurations(interruptWeChatRequestByFluwx, flutterActivity, enableLogging)
   }
}

def generateFluwxConfigurations(String interruptWeChatRequestByFluwx, String flutterActivity, String enableLogging) {
    File generateFolder = new File("${buildDir}/generated/src/kotlin/com/jarvan/fluwx")

    String template = "package com.jarvan.fluwx\n" +
            "\n" +
            "// auto generated\n" +
            "internal object FluwxConfigurations {\n" +
            "    val flutterActivity: String = \"&&flutterActivity&&\"\n" +
            "    val enableLogging: Boolean = &&enableLogging&&\n" +
            "    val interruptWeChatRequestByFluwx: Boolean = &&interruptWeChatRequestByFluwx&&\n" +
            "}"
    if (!generateFolder.exists()) {
        generateFolder.mkdirs()
    }

    String source = template.replace("&&interruptWeChatRequestByFluwx&&", interruptWeChatRequestByFluwx)
            .replace("&&flutterActivity&&", flutterActivity)
            .replace("&&enableLogging&&", enableLogging)
    file("${generateFolder.absolutePath}/FluwxConfigurations.kt").text = source
}

//tasks.withType(JavaCompile) { javaCompile ->
//    javaCompile.configure {
//        dependsOn("generateFluwxHelperFile")
//    }
//}
android.libraryVariants.configureEach {
    it.registerGeneratedResFolders(project.files(new File("${buildDir}/generated/src/kotlin/com/jarvan/fluwx")).builtBy(generateFluwxHelperFile))
}
