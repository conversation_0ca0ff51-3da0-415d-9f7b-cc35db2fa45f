import Flutter
import UIKit

@main
@objc class AppDelegate: FlutterAppDelegate {
    var flutterEngine : FlutterEngine?
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        GeneratedPluginRegistrant.register(with: self)
        
        // 注册DouluoManagerChannel
        if let messenger = window?.rootViewController as? FlutterViewController {
            DouluoManagerChannel.shared.setup(with: messenger.binaryMessenger)
        }
        self.flutterEngine = FlutterEngine(name: "my flutter engine")
        self.flutterEngine?.run()
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }

    override func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable : Any]) {
        //warning: 需要重写当前方法，gtsdk的接管系统方法就会生效，否则会影响回执
        //保持空实现
    }
}
