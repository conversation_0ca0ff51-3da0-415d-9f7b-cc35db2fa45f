//
//  PNSBuildModelUtils.swift
//  RedCrystal
//
//  Created by <PERSON><PERSON> on 17/09/2021.
//  Copyright © 2021 37sy. All rights reserved.
//
import UIKit
import ATAuthSDK
import CoreGraphics
import QuartzCore

class PNSBuildModelUtils: NSObject {
    
    // 正常的闪验弹窗
    class func buildAlertMode(with wechatLoginTarget: Any?, wechatLoginSelector: Selector, accountLoginTarget: Any?, accountLoginSelector: Selector, otherLoginTarget: Any?, otherLoginSelector: Selector, topBannerImage: UIImage?, agreement: String, policy: String) -> TXCustomModel? {

        var model: TXCustomModel?
        model = buildAlertAutorotateMode(with: wechatLoginTarget, wechatLoginSelector: wechatLoginSelector, accountLoginTarget: accountLoginTarget, accountLoginSelector: accountLoginSelector, otherLoginTarget: otherLoginTarget, otherLoginSelector: otherLoginSelector, topBannerImage: topBannerImage, agreement: agreement, policy: policy)
        return model
    }
    
    
    class func buildAlertAutorotateMode(with wechatLoginTarget: Any?, wechatLoginSelector: Selector, accountLoginTarget: Any?, accountLoginSelector: Selector, otherLoginTarget: Any?, otherLoginSelector: Selector, topBannerImage: UIImage?, agreement: String, policy: String) -> TXCustomModel {
        
        let model = TXCustomModel()
        model.supportedInterfaceOrientations = UIInterfaceOrientationMask.allButUpsideDown
        model.animationDuration = 0.0
        model.alertCornerRadiusArray = [NSNumber(value: 3), NSNumber(value: 3), NSNumber(value: 3), NSNumber(value: 3)]
        // 配置背景 - 添加蓝色渐变背景
        // 默认白色背景
        model.alertContentViewColor = UIColor.white
        model.alertCornerRadiusArray = [NSNumber(10), NSNumber(10), NSNumber(10), NSNumber(10)]
        
        model.alertTitleBarColor = UIColor.clear
        let attributes = [
            NSAttributedString.Key.foregroundColor: UIColor.white,
            NSAttributedString.Key.font: UIFont.systemFont(ofSize: 17.0)
        ]
        model.loginBtnText = NSAttributedString(string: "本机号码一键登录", attributes: attributes)
        
        // 创建头部视图 - 蓝色渐变背景
        let headerView = UIImageView()
        headerView.image = topBannerImage
        headerView.layer.cornerRadius = 10
        headerView.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        
        // title
        let title = UILabel()
        title.textAlignment = .center
        
        model.numberColor = UIColor.black
        model.numberFont = UIFont.boldSystemFont(ofSize: 35)

        model.sloganIsHidden = true
        model.changeBtnIsHidden = true
        model.privacyAlignment = NSTextAlignment.center
        model.privacyPreText = "已阅读并同意"
        model.privacyOne = ["《用户协议》", agreement]
        model.privacyConectTexts = ["及", "、"]
        model.privacyTwo = ["《隐私政策》", policy]
        model.privacyOperatorPreText = "《"
        model.privacyOperatorSufText = "》"
        model.privacyOperatorIndex = 2
        model.privacyFont = UIFont.systemFont(ofSize: 14.0)
        model.privacyColors = [
            UIColor(red: 153.0 / 255, green: 153.0 / 255, blue: 153.0 / 255, alpha: 1),
            UIColor(red: 69.0 / 255, green: 113.0 / 255, blue: 251.0 / 255, alpha: 1)]
        model.checkBoxWH = 16
        model.checkBoxImageEdgeInsets = UIEdgeInsets(top: 1, left: 1, bottom: 0, right: 0)
        model.checkBoxImages = [
            UIImage(
                named: "unchecked_icon.png",
                in: Bundle(for: TXCustomModel.self),
                compatibleWith: nil)!,
            UIImage(
                named: "checked_icon.png",
                in: Bundle(for: TXCustomModel.self),
                compatibleWith: nil)!
        ]
        
        model.alertBarIsHidden = true
        model.navTitleFrameBlock = { _, _, _ in
            return .zero
            }
        model.alertTitleBarFrameBlock = { _, _, frame in
            var frame = frame
            frame.size.height = 45
            return frame
        }
        model.contentViewFrameBlock = { _, _, _ in
            return CGRect.zero
        }
        model.logoFrameBlock = { _, _, frame in
            var frame = frame
            frame.origin.y = 130
            frame.origin.x -= 10
            frame.size.width = 100
            frame.size.height = 23
            return frame
        }

        model.sloganFrameBlock = { _, _, _ in
            return CGRect.zero
        }
        model.numberFrameBlock = { _, _, frame in
            var frame = frame
            frame.origin.y = CGFloat(285)
            frame.size.height = 45
            return frame
        }
        model.loginBtnFrameBlock = { _, _, frame in
            var frame = frame
            frame.origin.y = CGFloat(380)
            frame.size.height = 51
            return frame
        }
        // 其他手机号登录按钮
        let otherPhoneButton = UIButton(type: .system)
        otherPhoneButton.setTitle("其他手机号登录", for: .normal)
        otherPhoneButton.setTitleColor(UIColor(red: 48/255, green: 49/255, blue: 51/255, alpha: 1), for: .normal)
        otherPhoneButton.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        otherPhoneButton.backgroundColor = UIColor.white
        otherPhoneButton.layer.borderWidth = 1
        otherPhoneButton.layer.borderColor = UIColor(red: 48/255, green: 49/255, blue: 51/255, alpha: 1).cgColor
        otherPhoneButton.layer.cornerRadius = 5
        otherPhoneButton.addTarget(otherLoginTarget, action: otherLoginSelector, for: .touchUpInside)
        
        // 微信登录按钮
        let wechatButton = UIButton(type: .custom)
        wechatButton.setImage(UIImage(named: "sysq_ic_wechat_login"), for: .normal)
        wechatButton.backgroundColor = UIColor(red: 7/255, green: 193/255, blue: 96/255, alpha: 1)
        wechatButton.layer.cornerRadius = 30
        wechatButton.addTarget(wechatLoginTarget, action: wechatLoginSelector, for: .touchUpInside)
        
        // 账号登录按钮
        let accountButton = UIButton(type: .custom)
        accountButton.setImage(UIImage(named: "login_account"), for: .normal)
        accountButton.backgroundColor = UIColor(red: 66/255, green: 133/255, blue: 244/255, alpha: 1)
        accountButton.layer.cornerRadius = 30
        accountButton.addTarget(accountLoginTarget, action: accountLoginSelector, for: .touchUpInside)
        
        // 微信登录标签
        let wechatLabel = UILabel()
        wechatLabel.text = "微信登录"
        wechatLabel.textColor = UIColor.gray
        wechatLabel.font = UIFont.systemFont(ofSize: 14)
        wechatLabel.textAlignment = .center
        
        // 账号密码登录标签
        let accountLabel = UILabel()
        accountLabel.text = "账号密码登录"
        accountLabel.textColor = UIColor.gray
        accountLabel.font = UIFont.systemFont(ofSize: 14)
        accountLabel.textAlignment = .center
        
        let contentView = UIView()
        contentView.backgroundColor = .white // 示例背景色
        contentView.layer.cornerRadius = 10.0
        contentView.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        contentView.layer.masksToBounds = true
        
        
        model.customViewBlock = { superCustomView in
            superCustomView.addSubview(headerView)
            superCustomView.addSubview(contentView)
            
            superCustomView.addSubview(otherPhoneButton)
            superCustomView.addSubview(wechatButton)
            superCustomView.addSubview(accountButton)
            superCustomView.addSubview(wechatLabel)
            superCustomView.addSubview(accountLabel)
            superCustomView.addSubview(title)
        }
        
        model.customViewLayoutBlock = { _, _, _, _, logoFrame, _, _, loginFrame, _, privacyFrame in
            
            let imageHeight = UIScreen.main.bounds.width/((topBannerImage?.size.width)!/(topBannerImage?.size.height)!)
            // 头部视图布局
            headerView.frame = CGRect(
                x: 0,
                y: 0,
                width: UIScreen.main.bounds.width,
                height: imageHeight)
            
            contentView.frame = CGRect(
                x: 0,
                y: imageHeight - 10,
                width: UIScreen.main.bounds.width,
                height: UIScreen.main.bounds.height - imageHeight - 10
            )
            
            // 其他手机号登录按钮
            otherPhoneButton.frame = CGRect(
                x: loginFrame.origin.x,
                y: loginFrame.maxY + 10,
                width: loginFrame.width,
                height: 49)
            
            // 微信登录按钮
            wechatButton.frame = CGRect(
                x: loginFrame.midX - 90,
                y: privacyFrame.minY - 160,
                width: 60,
                height: 60)
            
            // 账号登录按钮
            accountButton.frame = CGRect(
                x: loginFrame.midX + 30,
                y: privacyFrame.minY - 160,
                width: 60,
                height: 60)
            
            // 微信登录标签
            wechatLabel.frame = CGRect(
                x: wechatButton.frame.midX - 30,
                y: wechatButton.frame.maxY + 5,
                width: 60,
                height: 15)
            wechatLabel.center.x = wechatButton.center.x
            
            // 账号登录标签
            accountLabel.frame = CGRect(
                x: accountButton.frame.midX - 35,
                y: accountButton.frame.maxY + 5,
                width: 90,
                height: 15)
            accountLabel.center.x = accountButton.center.x
            
            title.frame = CGRect(
                x: logoFrame.maxX - 25 ,
                y: logoFrame.midY - 25,
                width: 180,
                height: 20)
            title.center = CGPoint(x: loginFrame.midX, y: logoFrame.midY - 15)
        }

        model.privacyFrameBlock = { _, contentViewFrame, frame in
            var frame = frame
            frame.origin.y = contentViewFrame.height - 70
            frame.size.height = 38
            return frame
        }
        
        // 登录按钮颜色
        let loginBtn = UIButton(frame: CGRect(x: 0, y: 0, width: 300, height: 39))
        // 设置背景颜色为#4571FB
        loginBtn.backgroundColor = UIColor(red: 69/255, green: 113/255, blue: 251/255, alpha: 1)
        loginBtn.layer.cornerRadius = 5
//        loginBtn.gradient(colors: theme.loginButtonBackgroundColors)
        loginBtn.setTitleColor(UIColor.white, for: .normal)
        UIGraphicsBeginImageContext(loginBtn.frame.size)
        let ctx: CGContext = UIGraphicsGetCurrentContext()!
        loginBtn.layer.render(in: ctx)
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        guard let loginBtnBgColorImg = image, let loginBtnBgColorImg = loginBtnBgColorImg.image(roundedCornersSize: 5) else {
            return model
        }
        // 登录按钮颜色高亮
        guard let loginBtnBgHighlightedColorImg = UIImage.image(with: UIColor(red: 69/255, green: 113/255, blue: 251/255, alpha: 0.8),
                                                                rect: CGRect(x: 0, y: 0, width: 300, height: 39),
                                                                rounded: 5) else {
            return model
        }

        model.loginBtnBgImgs = [loginBtnBgColorImg, loginBtnBgColorImg, loginBtnBgHighlightedColorImg]

        return model
    }
    
}

enum PNSBuildModelStyle {
    case PNSBuildModelStyleAutorotate
    case PNSBuildModelStyleAlertAutorotate
}

public extension UIImage {
    // 生成圆角UIIamge 的方法
    func image(roundedCornersSize cornerRadius: CGFloat) -> UIImage? {
        let original = self
        let frame = CGRect(x: 0, y: 0, width: original.size.width, height: original.size.height)
        // 开始一个Image的上下文
        UIGraphicsBeginImageContextWithOptions(original.size, false, 2.0)
        // 添加圆角
        UIBezierPath(roundedRect: frame, cornerRadius: cornerRadius).addClip()
        // 绘制图片
        original.draw(in: frame)
        // 接收绘制成功的图片
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image
    }

    class func image(with color: UIColor?, rect: CGRect, rounded radius: CGFloat) -> UIImage? {
        UIGraphicsBeginImageContext(rect.size)
        let context = UIGraphicsGetCurrentContext()
        if let cgColor = color?.cgColor {
            context?.setFillColor(cgColor)
        }
        context?.fill(rect)

        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()

        return image?.image(roundedCornersSize: radius)
    }
    class func image(color: UIColor?) -> UIImage? {
        let rect = CGRect(x: 0, y: 0, width: 1, height: 1)
        UIGraphicsBeginImageContext(rect.size)
        let context = UIGraphicsGetCurrentContext()
        if let cgColor = color?.cgColor {
            context?.setFillColor(cgColor)
        }
        context?.fill(rect)

        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image
    }
}
