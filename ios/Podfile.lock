PODS:
  - device_info_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - fluttertoast (0.0.2):
    - Flutter
  - fluwx (0.0.1):
    - Flutter
    - fluwx/pay (= 0.0.1)
  - fluwx/pay (0.0.1):
    - Flutter
    - WechatOpenSDK-XCFramework (~> 2.0.5)
  - getuiflut (0.0.1):
    - Flutter
    - GTSDK
  - GTCommonSDK (3.1.10.0):
    - ZXSDK
  - GTSDK (3.0.10.0):
    - GTCommonSDK (> 3.0.9.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - TAThirdParty (0.3.5)
  - thinking_analytics (3.2.1):
    - Flutter
    - TAThirdParty (= 0.3.5)
    - ThinkingSDK (= 3.1.2)
  - ThinkingDataCore (1.2.2):
    - ThinkingDataCore/Main (= 1.2.2)
  - ThinkingDataCore/iOS (1.2.2)
  - ThinkingDataCore/Main (1.2.2):
    - ThinkingDataCore/iOS
    - ThinkingDataCore/OSX
    - ThinkingDataCore/tvOS
    - ThinkingDataCore/versionOS
    - ThinkingDataCore/watchOS
  - ThinkingSDK (3.1.2):
    - ThinkingSDK/Main (= 3.1.2)
  - ThinkingSDK/iOS (3.1.2):
    - ThinkingDataCore (= 1.2.2)
  - ThinkingSDK/Main (3.1.2):
    - ThinkingSDK/iOS
    - ThinkingSDK/OSX
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS
  - WechatOpenSDK-XCFramework (2.0.5)
  - ZXSDK (3.3.2)

DEPENDENCIES:
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Flutter (from `Flutter`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - fluwx (from `.symlinks/plugins/fluwx/ios`)
  - getuiflut (from `.symlinks/plugins/getuiflut/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - thinking_analytics (from `.symlinks/plugins/thinking_analytics/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - GTCommonSDK
    - GTSDK
    - TAThirdParty
    - ThinkingDataCore
    - ThinkingSDK
    - WechatOpenSDK-XCFramework
    - ZXSDK

EXTERNAL SOURCES:
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  fluwx:
    :path: ".symlinks/plugins/fluwx/ios"
  getuiflut:
    :path: ".symlinks/plugins/getuiflut/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  thinking_analytics:
    :path: ".symlinks/plugins/thinking_analytics/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  device_info_plus: 335f3ce08d2e174b9fdc3db3db0f4e3b1f66bd89
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  fluttertoast: 2c67e14dce98bbdb200df9e1acf610d7a6264ea1
  fluwx: 2ef787502fccb3f3596b380509001a8ea71cbbff
  getuiflut: ea9b200062f474810671171f9f36985ff5d431d0
  GTCommonSDK: aa0cdaada5e31c2dc173c8170127d50f16a439c7
  GTSDK: 1a2e62b50cf265046c2e48cdb88cd3d8e62bd7ea
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  TAThirdParty: 65db0235cd209237781535f19f9d0eef706156e5
  thinking_analytics: 93619fcfa7c05f7f33f90abf3f1b966b4d731116
  ThinkingDataCore: 696567a978c47e918da8a62e64e7dffa49584af5
  ThinkingSDK: 1e331743eae0e30dfd5148cbfe96366c915e6ac7
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2
  WechatOpenSDK-XCFramework: b072030c9eeee91dfff1856a7846f70f7b9a88ed
  ZXSDK: 786338c0a18e98e03eda00699c3bfd2700b97117

PODFILE CHECKSUM: 59aa9215904c5cd9be1114c307564da7cb7ba5bd

COCOAPODS: 1.16.2
