import 'package:flutter_test/flutter_test.dart';
import 'package:dlyz_flutter/net/sign_v5_interceptor.dart';

void main() {
  group('SignV5Interceptor Tests', () {
    const testAppKey = 'test_app_key_123';
    
    test('should generate correct signature with all parameters', () {
      final params = <String, String>{
        'pid': '12345',
        'gid': '67890',
        'refer': 'test_refer',
        'version': '1.0.0',
        'time': '1640995200',
        'trans_info': 'test_trans',
      };

      final sign = SignV5Interceptor.signWithKey(testAppKey, params);
      
      // 验证签名不为空
      expect(sign.isNotEmpty, true);
      expect(sign.length, 32); // MD5 签名长度为32
      
      // 验证签名为小写
      expect(sign, equals(sign.toLowerCase()));
    });

    test('should generate correct signature with empty trans_info', () {
      final params = <String, String>{
        'pid': '12345',
        'gid': '67890',
        'refer': 'test_refer',
        'version': '1.0.0',
        'time': '1640995200',
        // trans_info 为空
      };

      final sign = SignV5Interceptor.signWithKey(testAppKey, params);
      
      expect(sign.isNotEmpty, true);
      expect(sign.length, 32);
    });

    test('should return empty string when missing required parameters', () {
      final params = <String, String>{
        'pid': '12345',
        // 缺少其他必需参数
      };

      final sign = SignV5Interceptor.signWithKey(testAppKey, params);
      
      expect(sign, '');
    });

    test('should verify signature correctly', () {
      final params = <String, String>{
        'pid': '12345',
        'gid': '67890',
        'refer': 'test_refer',
        'version': '1.0.0',
        'time': '1640995200',
        'trans_info': 'test_trans',
      };

      // 生成签名
      final sign = SignV5Interceptor.signWithKey(testAppKey, params);
      params['sign'] = sign;

      // 验证签名
      final isValid = SignV5Interceptor.verifySign(params, appKey: testAppKey);
      
      expect(isValid, true);
    });

    test('should fail verification with wrong signature', () {
      final params = <String, String>{
        'pid': '12345',
        'gid': '67890',
        'refer': 'test_refer',
        'version': '1.0.0',
        'time': '1640995200',
        'trans_info': 'test_trans',
        'sign': 'wrong_signature',
      };

      final isValid = SignV5Interceptor.verifySign(params, appKey: testAppKey);
      
      expect(isValid, false);
    });

    test('should generate consistent signatures for same parameters', () {
      final params = <String, String>{
        'pid': '12345',
        'gid': '67890',
        'refer': 'test_refer',
        'version': '1.0.0',
        'time': '1640995200',
        'trans_info': 'test_trans',
      };

      final sign1 = SignV5Interceptor.signWithKey(testAppKey, params);
      final sign2 = SignV5Interceptor.signWithKey(testAppKey, params);
      
      expect(sign1, equals(sign2));
    });

    test('should generate different signatures for different parameters', () {
      final params1 = <String, String>{
        'pid': '12345',
        'gid': '67890',
        'refer': 'test_refer',
        'version': '1.0.0',
        'time': '1640995200',
        'trans_info': 'test_trans',
      };

      final params2 = <String, String>{
        'pid': '54321', // 不同的 pid
        'gid': '67890',
        'refer': 'test_refer',
        'version': '1.0.0',
        'time': '1640995200',
        'trans_info': 'test_trans',
      };

      final sign1 = SignV5Interceptor.signWithKey(testAppKey, params1);
      final sign2 = SignV5Interceptor.signWithKey(testAppKey, params2);
      
      expect(sign1, isNot(equals(sign2)));
    });

    test('should handle special characters in parameters', () {
      final params = <String, String>{
        'pid': '12345',
        'gid': '67890',
        'refer': 'test_refer',
        'version': '1.0.0',
        'time': '1640995200',
        'trans_info': 'test_中文_特殊字符@#\$%',
      };

      final sign = SignV5Interceptor.signWithKey(testAppKey, params);
      
      expect(sign.isNotEmpty, true);
      expect(sign.length, 32);
    });

    test('should match Java implementation signature', () {
      // 这个测试用例模拟 Java 代码生成的签名结果
      // 确保 Dart 实现与 Java 实现一致
      final params = <String, String>{
        'pid': '123',
        'gid': '456',
        'refer': 'sy_00001',
        'version': '1.0.0',
        'time': '1640995200',
        'trans_info': '',
      };

      const appKey = 'test_key';
      
      // 预期的签名原串：123456sy_000011.0.01640995200test_key
      // 对应的 MD5 值（需要根据实际计算结果调整）
      final sign = SignV5Interceptor.signWithKey(appKey, params);
      
      // 验证签名格式正确
      expect(sign.isNotEmpty, true);
      expect(sign.length, 32);
      expect(RegExp(r'^[a-f0-9]{32}$').hasMatch(sign), true);
    });
  });
}