// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

package io.flutter.plugins.webviewflutter;

import android.webkit.WebChromeClient.CustomViewCallback;
import androidx.annotation.NonNull;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugins.webviewflutter.GeneratedAndroidWebView.CustomViewCallbackHostApi;
import java.util.Objects;

/**
 * Host API implementation for `CustomViewCallback`.
 *
 * <p>This class may handle instantiating and adding native object instances that are attached to a
 * Dart instance or handle method calls on the associated native class or an instance of the class.
 */
public class CustomViewCallbackHostApiImpl implements CustomViewCallbackHostApi {
  // To ease adding additional methods, this value is added prematurely.
  @SuppressWarnings({"unused", "FieldCanBeLocal"})
  private final BinaryMessenger binaryMessenger;

  private final InstanceManager instanceManager;

  /**
   * Constructs a {@link CustomViewCallbackHostApiImpl}.
   *
   * @param binaryMessenger used to communicate with <PERSON><PERSON> over asynchronous messages
   * @param instanceManager maintains instances stored to communicate with attached Dart objects
   */
  public CustomViewCallbackHostApiImpl(
      @NonNull BinaryMessenger binaryMessenger, @NonNull InstanceManager instanceManager) {
    this.binaryMessenger = binaryMessenger;
    this.instanceManager = instanceManager;
  }

  @Override
  public void onCustomViewHidden(@NonNull Long identifier) {
    getCustomViewCallbackInstance(identifier).onCustomViewHidden();
  }

  private CustomViewCallback getCustomViewCallbackInstance(@NonNull Long identifier) {
    return Objects.requireNonNull(instanceManager.getInstance(identifier));
  }
}
