// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.
// Autogenerated from Pig<PERSON> (v11.0.1), do not edit directly.
// See also: https://pub.dev/packages/pigeon

package io.flutter.plugins.webviewflutter;

import android.util.Log;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import io.flutter.plugin.common.BasicMessageChannel;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MessageCodec;
import io.flutter.plugin.common.StandardMessageCodec;
import java.io.ByteArrayOutputStream;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/** Generated class from Pigeon. */
@SuppressWarnings({"unused", "unchecked", "CodeBlock2Expr", "RedundantSuppression", "serial"})
public class GeneratedAndroidWebView {

  /** Error class for passing custom error details to Flutter via a thrown PlatformException. */
  public static class FlutterError extends RuntimeException {

    /** The error code. */
    public final String code;

    /** The error details. Must be a datatype supported by the api codec. */
    public final Object details;

    public FlutterError(@NonNull String code, @Nullable String message, @Nullable Object details) {
      super(message);
      this.code = code;
      this.details = details;
    }
  }

  @NonNull
  protected static ArrayList<Object> wrapError(@NonNull Throwable exception) {
    ArrayList<Object> errorList = new ArrayList<Object>(3);
    if (exception instanceof FlutterError) {
      FlutterError error = (FlutterError) exception;
      errorList.add(error.code);
      errorList.add(error.getMessage());
      errorList.add(error.details);
    } else {
      errorList.add(exception.toString());
      errorList.add(exception.getClass().getSimpleName());
      errorList.add(
          "Cause: " + exception.getCause() + ", Stacktrace: " + Log.getStackTraceString(exception));
    }
    return errorList;
  }

  /**
   * Mode of how to select files for a file chooser.
   *
   * <p>See
   * https://developer.android.com/reference/android/webkit/WebChromeClient.FileChooserParams.
   */
  public enum FileChooserMode {
    /**
     * Open single file and requires that the file exists before allowing the user to pick it.
     *
     * <p>See
     * https://developer.android.com/reference/android/webkit/WebChromeClient.FileChooserParams#MODE_OPEN.
     */
    OPEN(0),
    /**
     * Similar to [open] but allows multiple files to be selected.
     *
     * <p>See
     * https://developer.android.com/reference/android/webkit/WebChromeClient.FileChooserParams#MODE_OPEN_MULTIPLE.
     */
    OPEN_MULTIPLE(1),
    /**
     * Allows picking a nonexistent file and saving it.
     *
     * <p>See
     * https://developer.android.com/reference/android/webkit/WebChromeClient.FileChooserParams#MODE_SAVE.
     */
    SAVE(2);

    final int index;

    private FileChooserMode(final int index) {
      this.index = index;
    }
  }

  /**
   * Indicates the type of message logged to the console.
   *
   * <p>See https://developer.android.com/reference/android/webkit/ConsoleMessage.MessageLevel.
   */
  public enum ConsoleMessageLevel {
    /**
     * Indicates a message is logged for debugging.
     *
     * <p>See
     * https://developer.android.com/reference/android/webkit/ConsoleMessage.MessageLevel#DEBUG.
     */
    DEBUG(0),
    /**
     * Indicates a message is provided as an error.
     *
     * <p>See
     * https://developer.android.com/reference/android/webkit/ConsoleMessage.MessageLevel#ERROR.
     */
    ERROR(1),
    /**
     * Indicates a message is provided as a basic log message.
     *
     * <p>See
     * https://developer.android.com/reference/android/webkit/ConsoleMessage.MessageLevel#LOG.
     */
    LOG(2),
    /**
     * Indicates a message is provided as a tip.
     *
     * <p>See
     * https://developer.android.com/reference/android/webkit/ConsoleMessage.MessageLevel#TIP.
     */
    TIP(3),
    /**
     * Indicates a message is provided as a warning.
     *
     * <p>See
     * https://developer.android.com/reference/android/webkit/ConsoleMessage.MessageLevel#WARNING.
     */
    WARNING(4),
    /**
     * Indicates a message with an unknown level.
     *
     * <p>This does not represent an actual value provided by the platform and only indicates a
     * value was provided that isn't currently supported.
     */
    UNKNOWN(5);

    final int index;

    private ConsoleMessageLevel(final int index) {
      this.index = index;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static final class WebResourceRequestData {
    private @NonNull String url;

    public @NonNull String getUrl() {
      return url;
    }

    public void setUrl(@NonNull String setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"url\" is null.");
      }
      this.url = setterArg;
    }

    private @NonNull Boolean isForMainFrame;

    public @NonNull Boolean getIsForMainFrame() {
      return isForMainFrame;
    }

    public void setIsForMainFrame(@NonNull Boolean setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"isForMainFrame\" is null.");
      }
      this.isForMainFrame = setterArg;
    }

    private @Nullable Boolean isRedirect;

    public @Nullable Boolean getIsRedirect() {
      return isRedirect;
    }

    public void setIsRedirect(@Nullable Boolean setterArg) {
      this.isRedirect = setterArg;
    }

    private @NonNull Boolean hasGesture;

    public @NonNull Boolean getHasGesture() {
      return hasGesture;
    }

    public void setHasGesture(@NonNull Boolean setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"hasGesture\" is null.");
      }
      this.hasGesture = setterArg;
    }

    private @NonNull String method;

    public @NonNull String getMethod() {
      return method;
    }

    public void setMethod(@NonNull String setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"method\" is null.");
      }
      this.method = setterArg;
    }

    private @NonNull Map<String, String> requestHeaders;

    public @NonNull Map<String, String> getRequestHeaders() {
      return requestHeaders;
    }

    public void setRequestHeaders(@NonNull Map<String, String> setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"requestHeaders\" is null.");
      }
      this.requestHeaders = setterArg;
    }

    /** Constructor is non-public to enforce null safety; use Builder. */
    WebResourceRequestData() {}

    public static final class Builder {

      private @Nullable String url;

      public @NonNull Builder setUrl(@NonNull String setterArg) {
        this.url = setterArg;
        return this;
      }

      private @Nullable Boolean isForMainFrame;

      public @NonNull Builder setIsForMainFrame(@NonNull Boolean setterArg) {
        this.isForMainFrame = setterArg;
        return this;
      }

      private @Nullable Boolean isRedirect;

      public @NonNull Builder setIsRedirect(@Nullable Boolean setterArg) {
        this.isRedirect = setterArg;
        return this;
      }

      private @Nullable Boolean hasGesture;

      public @NonNull Builder setHasGesture(@NonNull Boolean setterArg) {
        this.hasGesture = setterArg;
        return this;
      }

      private @Nullable String method;

      public @NonNull Builder setMethod(@NonNull String setterArg) {
        this.method = setterArg;
        return this;
      }

      private @Nullable Map<String, String> requestHeaders;

      public @NonNull Builder setRequestHeaders(@NonNull Map<String, String> setterArg) {
        this.requestHeaders = setterArg;
        return this;
      }

      public @NonNull WebResourceRequestData build() {
        WebResourceRequestData pigeonReturn = new WebResourceRequestData();
        pigeonReturn.setUrl(url);
        pigeonReturn.setIsForMainFrame(isForMainFrame);
        pigeonReturn.setIsRedirect(isRedirect);
        pigeonReturn.setHasGesture(hasGesture);
        pigeonReturn.setMethod(method);
        pigeonReturn.setRequestHeaders(requestHeaders);
        return pigeonReturn;
      }
    }

    @NonNull
    ArrayList<Object> toList() {
      ArrayList<Object> toListResult = new ArrayList<Object>(6);
      toListResult.add(url);
      toListResult.add(isForMainFrame);
      toListResult.add(isRedirect);
      toListResult.add(hasGesture);
      toListResult.add(method);
      toListResult.add(requestHeaders);
      return toListResult;
    }

    static @NonNull WebResourceRequestData fromList(@NonNull ArrayList<Object> list) {
      WebResourceRequestData pigeonResult = new WebResourceRequestData();
      Object url = list.get(0);
      pigeonResult.setUrl((String) url);
      Object isForMainFrame = list.get(1);
      pigeonResult.setIsForMainFrame((Boolean) isForMainFrame);
      Object isRedirect = list.get(2);
      pigeonResult.setIsRedirect((Boolean) isRedirect);
      Object hasGesture = list.get(3);
      pigeonResult.setHasGesture((Boolean) hasGesture);
      Object method = list.get(4);
      pigeonResult.setMethod((String) method);
      Object requestHeaders = list.get(5);
      pigeonResult.setRequestHeaders((Map<String, String>) requestHeaders);
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static final class WebResourceResponseData {
    private @NonNull Long statusCode;

    public @NonNull Long getStatusCode() {
      return statusCode;
    }

    public void setStatusCode(@NonNull Long setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"statusCode\" is null.");
      }
      this.statusCode = setterArg;
    }

    /** Constructor is non-public to enforce null safety; use Builder. */
    WebResourceResponseData() {}

    public static final class Builder {

      private @Nullable Long statusCode;

      public @NonNull Builder setStatusCode(@NonNull Long setterArg) {
        this.statusCode = setterArg;
        return this;
      }

      public @NonNull WebResourceResponseData build() {
        WebResourceResponseData pigeonReturn = new WebResourceResponseData();
        pigeonReturn.setStatusCode(statusCode);
        return pigeonReturn;
      }
    }

    @NonNull
    ArrayList<Object> toList() {
      ArrayList<Object> toListResult = new ArrayList<Object>(1);
      toListResult.add(statusCode);
      return toListResult;
    }

    static @NonNull WebResourceResponseData fromList(@NonNull ArrayList<Object> list) {
      WebResourceResponseData pigeonResult = new WebResourceResponseData();
      Object statusCode = list.get(0);
      pigeonResult.setStatusCode(
          (statusCode == null)
              ? null
              : ((statusCode instanceof Integer) ? (Integer) statusCode : (Long) statusCode));
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static final class WebResourceErrorData {
    private @NonNull Long errorCode;

    public @NonNull Long getErrorCode() {
      return errorCode;
    }

    public void setErrorCode(@NonNull Long setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"errorCode\" is null.");
      }
      this.errorCode = setterArg;
    }

    private @NonNull String description;

    public @NonNull String getDescription() {
      return description;
    }

    public void setDescription(@NonNull String setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"description\" is null.");
      }
      this.description = setterArg;
    }

    /** Constructor is non-public to enforce null safety; use Builder. */
    WebResourceErrorData() {}

    public static final class Builder {

      private @Nullable Long errorCode;

      public @NonNull Builder setErrorCode(@NonNull Long setterArg) {
        this.errorCode = setterArg;
        return this;
      }

      private @Nullable String description;

      public @NonNull Builder setDescription(@NonNull String setterArg) {
        this.description = setterArg;
        return this;
      }

      public @NonNull WebResourceErrorData build() {
        WebResourceErrorData pigeonReturn = new WebResourceErrorData();
        pigeonReturn.setErrorCode(errorCode);
        pigeonReturn.setDescription(description);
        return pigeonReturn;
      }
    }

    @NonNull
    ArrayList<Object> toList() {
      ArrayList<Object> toListResult = new ArrayList<Object>(2);
      toListResult.add(errorCode);
      toListResult.add(description);
      return toListResult;
    }

    static @NonNull WebResourceErrorData fromList(@NonNull ArrayList<Object> list) {
      WebResourceErrorData pigeonResult = new WebResourceErrorData();
      Object errorCode = list.get(0);
      pigeonResult.setErrorCode(
          (errorCode == null)
              ? null
              : ((errorCode instanceof Integer) ? (Integer) errorCode : (Long) errorCode));
      Object description = list.get(1);
      pigeonResult.setDescription((String) description);
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static final class WebViewPoint {
    private @NonNull Long x;

    public @NonNull Long getX() {
      return x;
    }

    public void setX(@NonNull Long setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"x\" is null.");
      }
      this.x = setterArg;
    }

    private @NonNull Long y;

    public @NonNull Long getY() {
      return y;
    }

    public void setY(@NonNull Long setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"y\" is null.");
      }
      this.y = setterArg;
    }

    /** Constructor is non-public to enforce null safety; use Builder. */
    WebViewPoint() {}

    public static final class Builder {

      private @Nullable Long x;

      public @NonNull Builder setX(@NonNull Long setterArg) {
        this.x = setterArg;
        return this;
      }

      private @Nullable Long y;

      public @NonNull Builder setY(@NonNull Long setterArg) {
        this.y = setterArg;
        return this;
      }

      public @NonNull WebViewPoint build() {
        WebViewPoint pigeonReturn = new WebViewPoint();
        pigeonReturn.setX(x);
        pigeonReturn.setY(y);
        return pigeonReturn;
      }
    }

    @NonNull
    ArrayList<Object> toList() {
      ArrayList<Object> toListResult = new ArrayList<Object>(2);
      toListResult.add(x);
      toListResult.add(y);
      return toListResult;
    }

    static @NonNull WebViewPoint fromList(@NonNull ArrayList<Object> list) {
      WebViewPoint pigeonResult = new WebViewPoint();
      Object x = list.get(0);
      pigeonResult.setX((x == null) ? null : ((x instanceof Integer) ? (Integer) x : (Long) x));
      Object y = list.get(1);
      pigeonResult.setY((y == null) ? null : ((y instanceof Integer) ? (Integer) y : (Long) y));
      return pigeonResult;
    }
  }

  /**
   * Represents a JavaScript console message from WebCore.
   *
   * <p>See https://developer.android.com/reference/android/webkit/ConsoleMessage
   *
   * <p>Generated class from Pigeon that represents data sent in messages.
   */
  public static final class ConsoleMessage {
    private @NonNull Long lineNumber;

    public @NonNull Long getLineNumber() {
      return lineNumber;
    }

    public void setLineNumber(@NonNull Long setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"lineNumber\" is null.");
      }
      this.lineNumber = setterArg;
    }

    private @NonNull String message;

    public @NonNull String getMessage() {
      return message;
    }

    public void setMessage(@NonNull String setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"message\" is null.");
      }
      this.message = setterArg;
    }

    private @NonNull ConsoleMessageLevel level;

    public @NonNull ConsoleMessageLevel getLevel() {
      return level;
    }

    public void setLevel(@NonNull ConsoleMessageLevel setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"level\" is null.");
      }
      this.level = setterArg;
    }

    private @NonNull String sourceId;

    public @NonNull String getSourceId() {
      return sourceId;
    }

    public void setSourceId(@NonNull String setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"sourceId\" is null.");
      }
      this.sourceId = setterArg;
    }

    /** Constructor is non-public to enforce null safety; use Builder. */
    ConsoleMessage() {}

    public static final class Builder {

      private @Nullable Long lineNumber;

      public @NonNull Builder setLineNumber(@NonNull Long setterArg) {
        this.lineNumber = setterArg;
        return this;
      }

      private @Nullable String message;

      public @NonNull Builder setMessage(@NonNull String setterArg) {
        this.message = setterArg;
        return this;
      }

      private @Nullable ConsoleMessageLevel level;

      public @NonNull Builder setLevel(@NonNull ConsoleMessageLevel setterArg) {
        this.level = setterArg;
        return this;
      }

      private @Nullable String sourceId;

      public @NonNull Builder setSourceId(@NonNull String setterArg) {
        this.sourceId = setterArg;
        return this;
      }

      public @NonNull ConsoleMessage build() {
        ConsoleMessage pigeonReturn = new ConsoleMessage();
        pigeonReturn.setLineNumber(lineNumber);
        pigeonReturn.setMessage(message);
        pigeonReturn.setLevel(level);
        pigeonReturn.setSourceId(sourceId);
        return pigeonReturn;
      }
    }

    @NonNull
    ArrayList<Object> toList() {
      ArrayList<Object> toListResult = new ArrayList<Object>(4);
      toListResult.add(lineNumber);
      toListResult.add(message);
      toListResult.add(level == null ? null : level.index);
      toListResult.add(sourceId);
      return toListResult;
    }

    static @NonNull ConsoleMessage fromList(@NonNull ArrayList<Object> list) {
      ConsoleMessage pigeonResult = new ConsoleMessage();
      Object lineNumber = list.get(0);
      pigeonResult.setLineNumber(
          (lineNumber == null)
              ? null
              : ((lineNumber instanceof Integer) ? (Integer) lineNumber : (Long) lineNumber));
      Object message = list.get(1);
      pigeonResult.setMessage((String) message);
      Object level = list.get(2);
      pigeonResult.setLevel(ConsoleMessageLevel.values()[(int) level]);
      Object sourceId = list.get(3);
      pigeonResult.setSourceId((String) sourceId);
      return pigeonResult;
    }
  }

  public interface Result<T> {
    @SuppressWarnings("UnknownNullness")
    void success(T result);

    void error(@NonNull Throwable error);
  }
  /**
   * Host API for managing the native `InstanceManager`.
   *
   * <p>Generated interface from Pigeon that represents a handler of messages from Flutter.
   */
  public interface InstanceManagerHostApi {
    /**
     * Clear the native `InstanceManager`.
     *
     * <p>This is typically only used after a hot restart.
     */
    void clear();

    /** The codec used by InstanceManagerHostApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }
    /**
     * Sets up an instance of `InstanceManagerHostApi` to handle messages through the
     * `binaryMessenger`.
     */
    static void setup(
        @NonNull BinaryMessenger binaryMessenger, @Nullable InstanceManagerHostApi api) {
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.InstanceManagerHostApi.clear",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                try {
                  api.clear();
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
    }
  }
  /**
   * Handles methods calls to the native Java Object class.
   *
   * <p>Also handles calls to remove the reference to an instance with `dispose`.
   *
   * <p>See https://docs.oracle.com/javase/7/docs/api/java/lang/Object.html.
   *
   * <p>Generated interface from Pigeon that represents a handler of messages from Flutter.
   */
  public interface JavaObjectHostApi {

    void dispose(@NonNull Long identifier);

    /** The codec used by JavaObjectHostApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }
    /**
     * Sets up an instance of `JavaObjectHostApi` to handle messages through the `binaryMessenger`.
     */
    static void setup(@NonNull BinaryMessenger binaryMessenger, @Nullable JavaObjectHostApi api) {
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.JavaObjectHostApi.dispose",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number identifierArg = (Number) args.get(0);
                try {
                  api.dispose((identifierArg == null) ? null : identifierArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
    }
  }
  /**
   * Handles callbacks methods for the native Java Object class.
   *
   * <p>See https://docs.oracle.com/javase/7/docs/api/java/lang/Object.html.
   *
   * <p>Generated class from Pigeon that represents Flutter messages that can be called from Java.
   */
  public static class JavaObjectFlutterApi {
    private final @NonNull BinaryMessenger binaryMessenger;

    public JavaObjectFlutterApi(@NonNull BinaryMessenger argBinaryMessenger) {
      this.binaryMessenger = argBinaryMessenger;
    }

    /** Public interface for sending reply. */
    @SuppressWarnings("UnknownNullness")
    public interface Reply<T> {
      void reply(T reply);
    }
    /** The codec used by JavaObjectFlutterApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }

    public void dispose(@NonNull Long identifierArg, @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.JavaObjectFlutterApi.dispose",
              getCodec());
      channel.send(
          new ArrayList<Object>(Collections.singletonList(identifierArg)),
          channelReply -> callback.reply(null));
    }
  }
  /**
   * Host API for `CookieManager`.
   *
   * <p>This class may handle instantiating and adding native object instances that are attached to
   * a Dart instance or handle method calls on the associated native class or an instance of the
   * class.
   *
   * <p>Generated interface from Pigeon that represents a handler of messages from Flutter.
   */
  public interface CookieManagerHostApi {
    /** Handles attaching `CookieManager.instance` to a native instance. */
    void attachInstance(@NonNull Long instanceIdentifier);
    /** Handles Dart method `CookieManager.setCookie`. */
    void setCookie(@NonNull Long identifier, @NonNull String url, @NonNull String value);
    /** Handles Dart method `CookieManager.removeAllCookies`. */
    void removeAllCookies(@NonNull Long identifier, @NonNull Result<Boolean> result);
    /** Handles Dart method `CookieManager.setAcceptThirdPartyCookies`. */
    void setAcceptThirdPartyCookies(
        @NonNull Long identifier, @NonNull Long webViewIdentifier, @NonNull Boolean accept);

    /** The codec used by CookieManagerHostApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }
    /**
     * Sets up an instance of `CookieManagerHostApi` to handle messages through the
     * `binaryMessenger`.
     */
    static void setup(
        @NonNull BinaryMessenger binaryMessenger, @Nullable CookieManagerHostApi api) {
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.CookieManagerHostApi.attachInstance",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdentifierArg = (Number) args.get(0);
                try {
                  api.attachInstance(
                      (instanceIdentifierArg == null) ? null : instanceIdentifierArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.CookieManagerHostApi.setCookie",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number identifierArg = (Number) args.get(0);
                String urlArg = (String) args.get(1);
                String valueArg = (String) args.get(2);
                try {
                  api.setCookie(
                      (identifierArg == null) ? null : identifierArg.longValue(), urlArg, valueArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.CookieManagerHostApi.removeAllCookies",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number identifierArg = (Number) args.get(0);
                Result<Boolean> resultCallback =
                    new Result<Boolean>() {
                      public void success(Boolean result) {
                        wrapped.add(0, result);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.removeAllCookies(
                    (identifierArg == null) ? null : identifierArg.longValue(), resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.CookieManagerHostApi.setAcceptThirdPartyCookies",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number identifierArg = (Number) args.get(0);
                Number webViewIdentifierArg = (Number) args.get(1);
                Boolean acceptArg = (Boolean) args.get(2);
                try {
                  api.setAcceptThirdPartyCookies(
                      (identifierArg == null) ? null : identifierArg.longValue(),
                      (webViewIdentifierArg == null) ? null : webViewIdentifierArg.longValue(),
                      acceptArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
    }
  }

  private static class WebViewHostApiCodec extends StandardMessageCodec {
    public static final WebViewHostApiCodec INSTANCE = new WebViewHostApiCodec();

    private WebViewHostApiCodec() {}

    @Override
    protected Object readValueOfType(byte type, @NonNull ByteBuffer buffer) {
      switch (type) {
        case (byte) 128:
          return WebViewPoint.fromList((ArrayList<Object>) readValue(buffer));
        default:
          return super.readValueOfType(type, buffer);
      }
    }

    @Override
    protected void writeValue(@NonNull ByteArrayOutputStream stream, Object value) {
      if (value instanceof WebViewPoint) {
        stream.write(128);
        writeValue(stream, ((WebViewPoint) value).toList());
      } else {
        super.writeValue(stream, value);
      }
    }
  }

  /** Generated interface from Pigeon that represents a handler of messages from Flutter. */
  public interface WebViewHostApi {

    void create(@NonNull Long instanceId);

    void loadData(
        @NonNull Long instanceId,
        @NonNull String data,
        @Nullable String mimeType,
        @Nullable String encoding);

    void loadDataWithBaseUrl(
        @NonNull Long instanceId,
        @Nullable String baseUrl,
        @NonNull String data,
        @Nullable String mimeType,
        @Nullable String encoding,
        @Nullable String historyUrl);

    void loadUrl(
        @NonNull Long instanceId, @NonNull String url, @NonNull Map<String, String> headers);

    void postUrl(@NonNull Long instanceId, @NonNull String url, @NonNull byte[] data);

    @Nullable
    String getUrl(@NonNull Long instanceId);

    @NonNull
    Boolean canGoBack(@NonNull Long instanceId);

    @NonNull
    Boolean canGoForward(@NonNull Long instanceId);

    void goBack(@NonNull Long instanceId);

    void goForward(@NonNull Long instanceId);

    void reload(@NonNull Long instanceId);

    void clearCache(@NonNull Long instanceId, @NonNull Boolean includeDiskFiles);

    void evaluateJavascript(
        @NonNull Long instanceId, @NonNull String javascriptString, @NonNull Result<String> result);

    @Nullable
    String getTitle(@NonNull Long instanceId);

    void scrollTo(@NonNull Long instanceId, @NonNull Long x, @NonNull Long y);

    void scrollBy(@NonNull Long instanceId, @NonNull Long x, @NonNull Long y);

    @NonNull
    Long getScrollX(@NonNull Long instanceId);

    @NonNull
    Long getScrollY(@NonNull Long instanceId);

    @NonNull
    WebViewPoint getScrollPosition(@NonNull Long instanceId);

    void setWebContentsDebuggingEnabled(@NonNull Boolean enabled);

    void setWebViewClient(@NonNull Long instanceId, @NonNull Long webViewClientInstanceId);

    void addJavaScriptChannel(@NonNull Long instanceId, @NonNull Long javaScriptChannelInstanceId);

    void removeJavaScriptChannel(
        @NonNull Long instanceId, @NonNull Long javaScriptChannelInstanceId);

    void setDownloadListener(@NonNull Long instanceId, @Nullable Long listenerInstanceId);

    void setWebChromeClient(@NonNull Long instanceId, @Nullable Long clientInstanceId);

    void setBackgroundColor(@NonNull Long instanceId, @NonNull Long color);

    /** The codec used by WebViewHostApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return WebViewHostApiCodec.INSTANCE;
    }
    /** Sets up an instance of `WebViewHostApi` to handle messages through the `binaryMessenger`. */
    static void setup(@NonNull BinaryMessenger binaryMessenger, @Nullable WebViewHostApi api) {
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.create",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                try {
                  api.create((instanceIdArg == null) ? null : instanceIdArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.loadData",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                String dataArg = (String) args.get(1);
                String mimeTypeArg = (String) args.get(2);
                String encodingArg = (String) args.get(3);
                try {
                  api.loadData(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(),
                      dataArg,
                      mimeTypeArg,
                      encodingArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.loadDataWithBaseUrl",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                String baseUrlArg = (String) args.get(1);
                String dataArg = (String) args.get(2);
                String mimeTypeArg = (String) args.get(3);
                String encodingArg = (String) args.get(4);
                String historyUrlArg = (String) args.get(5);
                try {
                  api.loadDataWithBaseUrl(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(),
                      baseUrlArg,
                      dataArg,
                      mimeTypeArg,
                      encodingArg,
                      historyUrlArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.loadUrl",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                String urlArg = (String) args.get(1);
                Map<String, String> headersArg = (Map<String, String>) args.get(2);
                try {
                  api.loadUrl(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(),
                      urlArg,
                      headersArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.postUrl",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                String urlArg = (String) args.get(1);
                byte[] dataArg = (byte[]) args.get(2);
                try {
                  api.postUrl(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(), urlArg, dataArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.getUrl",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                try {
                  String output =
                      api.getUrl((instanceIdArg == null) ? null : instanceIdArg.longValue());
                  wrapped.add(0, output);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.canGoBack",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                try {
                  Boolean output =
                      api.canGoBack((instanceIdArg == null) ? null : instanceIdArg.longValue());
                  wrapped.add(0, output);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.canGoForward",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                try {
                  Boolean output =
                      api.canGoForward((instanceIdArg == null) ? null : instanceIdArg.longValue());
                  wrapped.add(0, output);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.goBack",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                try {
                  api.goBack((instanceIdArg == null) ? null : instanceIdArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.goForward",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                try {
                  api.goForward((instanceIdArg == null) ? null : instanceIdArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.reload",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                try {
                  api.reload((instanceIdArg == null) ? null : instanceIdArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.clearCache",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Boolean includeDiskFilesArg = (Boolean) args.get(1);
                try {
                  api.clearCache(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(),
                      includeDiskFilesArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.evaluateJavascript",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                String javascriptStringArg = (String) args.get(1);
                Result<String> resultCallback =
                    new Result<String>() {
                      public void success(String result) {
                        wrapped.add(0, result);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.evaluateJavascript(
                    (instanceIdArg == null) ? null : instanceIdArg.longValue(),
                    javascriptStringArg,
                    resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.getTitle",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                try {
                  String output =
                      api.getTitle((instanceIdArg == null) ? null : instanceIdArg.longValue());
                  wrapped.add(0, output);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.scrollTo",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Number xArg = (Number) args.get(1);
                Number yArg = (Number) args.get(2);
                try {
                  api.scrollTo(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(),
                      (xArg == null) ? null : xArg.longValue(),
                      (yArg == null) ? null : yArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.scrollBy",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Number xArg = (Number) args.get(1);
                Number yArg = (Number) args.get(2);
                try {
                  api.scrollBy(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(),
                      (xArg == null) ? null : xArg.longValue(),
                      (yArg == null) ? null : yArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.getScrollX",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                try {
                  Long output =
                      api.getScrollX((instanceIdArg == null) ? null : instanceIdArg.longValue());
                  wrapped.add(0, output);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.getScrollY",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                try {
                  Long output =
                      api.getScrollY((instanceIdArg == null) ? null : instanceIdArg.longValue());
                  wrapped.add(0, output);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.getScrollPosition",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                try {
                  WebViewPoint output =
                      api.getScrollPosition(
                          (instanceIdArg == null) ? null : instanceIdArg.longValue());
                  wrapped.add(0, output);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.setWebContentsDebuggingEnabled",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Boolean enabledArg = (Boolean) args.get(0);
                try {
                  api.setWebContentsDebuggingEnabled(enabledArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.setWebViewClient",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Number webViewClientInstanceIdArg = (Number) args.get(1);
                try {
                  api.setWebViewClient(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(),
                      (webViewClientInstanceIdArg == null)
                          ? null
                          : webViewClientInstanceIdArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.addJavaScriptChannel",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Number javaScriptChannelInstanceIdArg = (Number) args.get(1);
                try {
                  api.addJavaScriptChannel(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(),
                      (javaScriptChannelInstanceIdArg == null)
                          ? null
                          : javaScriptChannelInstanceIdArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.removeJavaScriptChannel",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Number javaScriptChannelInstanceIdArg = (Number) args.get(1);
                try {
                  api.removeJavaScriptChannel(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(),
                      (javaScriptChannelInstanceIdArg == null)
                          ? null
                          : javaScriptChannelInstanceIdArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.setDownloadListener",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Number listenerInstanceIdArg = (Number) args.get(1);
                try {
                  api.setDownloadListener(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(),
                      (listenerInstanceIdArg == null) ? null : listenerInstanceIdArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.setWebChromeClient",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Number clientInstanceIdArg = (Number) args.get(1);
                try {
                  api.setWebChromeClient(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(),
                      (clientInstanceIdArg == null) ? null : clientInstanceIdArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.setBackgroundColor",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Number colorArg = (Number) args.get(1);
                try {
                  api.setBackgroundColor(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(),
                      (colorArg == null) ? null : colorArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
    }
  }
  /**
   * Flutter API for `WebView`.
   *
   * <p>This class may handle instantiating and adding Dart instances that are attached to a native
   * instance or receiving callback methods from an overridden native class.
   *
   * <p>See https://developer.android.com/reference/android/webkit/WebView.
   *
   * <p>Generated class from Pigeon that represents Flutter messages that can be called from Java.
   */
  public static class WebViewFlutterApi {
    private final @NonNull BinaryMessenger binaryMessenger;

    public WebViewFlutterApi(@NonNull BinaryMessenger argBinaryMessenger) {
      this.binaryMessenger = argBinaryMessenger;
    }

    /** Public interface for sending reply. */
    @SuppressWarnings("UnknownNullness")
    public interface Reply<T> {
      void reply(T reply);
    }
    /** The codec used by WebViewFlutterApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }
    /** Create a new Dart instance and add it to the `InstanceManager`. */
    public void create(@NonNull Long identifierArg, @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.WebViewFlutterApi.create",
              getCodec());
      channel.send(
          new ArrayList<Object>(Collections.singletonList(identifierArg)),
          channelReply -> callback.reply(null));
    }

    public void onScrollChanged(
        @NonNull Long webViewInstanceIdArg,
        @NonNull Long leftArg,
        @NonNull Long topArg,
        @NonNull Long oldLeftArg,
        @NonNull Long oldTopArg,
        @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.WebViewFlutterApi.onScrollChanged",
              getCodec());
      channel.send(
          new ArrayList<Object>(
              Arrays.asList(webViewInstanceIdArg, leftArg, topArg, oldLeftArg, oldTopArg)),
          channelReply -> callback.reply(null));
    }
  }
  /** Generated interface from Pigeon that represents a handler of messages from Flutter. */
  public interface WebSettingsHostApi {

    void create(@NonNull Long instanceId, @NonNull Long webViewInstanceId);

    void setDomStorageEnabled(@NonNull Long instanceId, @NonNull Boolean flag);

    void setJavaScriptCanOpenWindowsAutomatically(@NonNull Long instanceId, @NonNull Boolean flag);

    void setSupportMultipleWindows(@NonNull Long instanceId, @NonNull Boolean support);

    void setJavaScriptEnabled(@NonNull Long instanceId, @NonNull Boolean flag);

    void setUserAgentString(@NonNull Long instanceId, @Nullable String userAgentString);

    void setMediaPlaybackRequiresUserGesture(@NonNull Long instanceId, @NonNull Boolean require);

    void setSupportZoom(@NonNull Long instanceId, @NonNull Boolean support);

    void setLoadWithOverviewMode(@NonNull Long instanceId, @NonNull Boolean overview);

    void setUseWideViewPort(@NonNull Long instanceId, @NonNull Boolean use);

    void setDisplayZoomControls(@NonNull Long instanceId, @NonNull Boolean enabled);

    void setBuiltInZoomControls(@NonNull Long instanceId, @NonNull Boolean enabled);

    void setAllowFileAccess(@NonNull Long instanceId, @NonNull Boolean enabled);

    void setTextZoom(@NonNull Long instanceId, @NonNull Long textZoom);

    @NonNull
    String getUserAgentString(@NonNull Long instanceId);

    /** The codec used by WebSettingsHostApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }
    /**
     * Sets up an instance of `WebSettingsHostApi` to handle messages through the `binaryMessenger`.
     */
    static void setup(@NonNull BinaryMessenger binaryMessenger, @Nullable WebSettingsHostApi api) {
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.create",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Number webViewInstanceIdArg = (Number) args.get(1);
                try {
                  api.create(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(),
                      (webViewInstanceIdArg == null) ? null : webViewInstanceIdArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.setDomStorageEnabled",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Boolean flagArg = (Boolean) args.get(1);
                try {
                  api.setDomStorageEnabled(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(), flagArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.setJavaScriptCanOpenWindowsAutomatically",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Boolean flagArg = (Boolean) args.get(1);
                try {
                  api.setJavaScriptCanOpenWindowsAutomatically(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(), flagArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.setSupportMultipleWindows",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Boolean supportArg = (Boolean) args.get(1);
                try {
                  api.setSupportMultipleWindows(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(), supportArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.setJavaScriptEnabled",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Boolean flagArg = (Boolean) args.get(1);
                try {
                  api.setJavaScriptEnabled(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(), flagArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.setUserAgentString",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                String userAgentStringArg = (String) args.get(1);
                try {
                  api.setUserAgentString(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(),
                      userAgentStringArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.setMediaPlaybackRequiresUserGesture",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Boolean requireArg = (Boolean) args.get(1);
                try {
                  api.setMediaPlaybackRequiresUserGesture(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(), requireArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.setSupportZoom",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Boolean supportArg = (Boolean) args.get(1);
                try {
                  api.setSupportZoom(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(), supportArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.setLoadWithOverviewMode",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Boolean overviewArg = (Boolean) args.get(1);
                try {
                  api.setLoadWithOverviewMode(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(), overviewArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.setUseWideViewPort",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Boolean useArg = (Boolean) args.get(1);
                try {
                  api.setUseWideViewPort(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(), useArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.setDisplayZoomControls",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Boolean enabledArg = (Boolean) args.get(1);
                try {
                  api.setDisplayZoomControls(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(), enabledArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.setBuiltInZoomControls",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Boolean enabledArg = (Boolean) args.get(1);
                try {
                  api.setBuiltInZoomControls(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(), enabledArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.setAllowFileAccess",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Boolean enabledArg = (Boolean) args.get(1);
                try {
                  api.setAllowFileAccess(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(), enabledArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.setTextZoom",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Number textZoomArg = (Number) args.get(1);
                try {
                  api.setTextZoom(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(),
                      (textZoomArg == null) ? null : textZoomArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.getUserAgentString",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                try {
                  String output =
                      api.getUserAgentString(
                          (instanceIdArg == null) ? null : instanceIdArg.longValue());
                  wrapped.add(0, output);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
    }
  }
  /** Generated interface from Pigeon that represents a handler of messages from Flutter. */
  public interface JavaScriptChannelHostApi {

    void create(@NonNull Long instanceId, @NonNull String channelName);

    /** The codec used by JavaScriptChannelHostApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }
    /**
     * Sets up an instance of `JavaScriptChannelHostApi` to handle messages through the
     * `binaryMessenger`.
     */
    static void setup(
        @NonNull BinaryMessenger binaryMessenger, @Nullable JavaScriptChannelHostApi api) {
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.JavaScriptChannelHostApi.create",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                String channelNameArg = (String) args.get(1);
                try {
                  api.create(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(), channelNameArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
    }
  }
  /** Generated class from Pigeon that represents Flutter messages that can be called from Java. */
  public static class JavaScriptChannelFlutterApi {
    private final @NonNull BinaryMessenger binaryMessenger;

    public JavaScriptChannelFlutterApi(@NonNull BinaryMessenger argBinaryMessenger) {
      this.binaryMessenger = argBinaryMessenger;
    }

    /** Public interface for sending reply. */
    @SuppressWarnings("UnknownNullness")
    public interface Reply<T> {
      void reply(T reply);
    }
    /** The codec used by JavaScriptChannelFlutterApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }

    public void postMessage(
        @NonNull Long instanceIdArg, @NonNull String messageArg, @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.JavaScriptChannelFlutterApi.postMessage",
              getCodec());
      channel.send(
          new ArrayList<Object>(Arrays.asList(instanceIdArg, messageArg)),
          channelReply -> callback.reply(null));
    }
  }
  /** Generated interface from Pigeon that represents a handler of messages from Flutter. */
  public interface WebViewClientHostApi {

    void create(@NonNull Long instanceId);

    void setSynchronousReturnValueForShouldOverrideUrlLoading(
        @NonNull Long instanceId, @NonNull Boolean value);

    /** The codec used by WebViewClientHostApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }
    /**
     * Sets up an instance of `WebViewClientHostApi` to handle messages through the
     * `binaryMessenger`.
     */
    static void setup(
        @NonNull BinaryMessenger binaryMessenger, @Nullable WebViewClientHostApi api) {
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewClientHostApi.create",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                try {
                  api.create((instanceIdArg == null) ? null : instanceIdArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebViewClientHostApi.setSynchronousReturnValueForShouldOverrideUrlLoading",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Boolean valueArg = (Boolean) args.get(1);
                try {
                  api.setSynchronousReturnValueForShouldOverrideUrlLoading(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(), valueArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
    }
  }

  private static class WebViewClientFlutterApiCodec extends StandardMessageCodec {
    public static final WebViewClientFlutterApiCodec INSTANCE = new WebViewClientFlutterApiCodec();

    private WebViewClientFlutterApiCodec() {}

    @Override
    protected Object readValueOfType(byte type, @NonNull ByteBuffer buffer) {
      switch (type) {
        case (byte) 128:
          return WebResourceErrorData.fromList((ArrayList<Object>) readValue(buffer));
        case (byte) 129:
          return WebResourceRequestData.fromList((ArrayList<Object>) readValue(buffer));
        case (byte) 130:
          return WebResourceResponseData.fromList((ArrayList<Object>) readValue(buffer));
        default:
          return super.readValueOfType(type, buffer);
      }
    }

    @Override
    protected void writeValue(@NonNull ByteArrayOutputStream stream, Object value) {
      if (value instanceof WebResourceErrorData) {
        stream.write(128);
        writeValue(stream, ((WebResourceErrorData) value).toList());
      } else if (value instanceof WebResourceRequestData) {
        stream.write(129);
        writeValue(stream, ((WebResourceRequestData) value).toList());
      } else if (value instanceof WebResourceResponseData) {
        stream.write(130);
        writeValue(stream, ((WebResourceResponseData) value).toList());
      } else {
        super.writeValue(stream, value);
      }
    }
  }

  /** Generated class from Pigeon that represents Flutter messages that can be called from Java. */
  public static class WebViewClientFlutterApi {
    private final @NonNull BinaryMessenger binaryMessenger;

    public WebViewClientFlutterApi(@NonNull BinaryMessenger argBinaryMessenger) {
      this.binaryMessenger = argBinaryMessenger;
    }

    /** Public interface for sending reply. */
    @SuppressWarnings("UnknownNullness")
    public interface Reply<T> {
      void reply(T reply);
    }
    /** The codec used by WebViewClientFlutterApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return WebViewClientFlutterApiCodec.INSTANCE;
    }

    public void onPageStarted(
        @NonNull Long instanceIdArg,
        @NonNull Long webViewInstanceIdArg,
        @NonNull String urlArg,
        @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.WebViewClientFlutterApi.onPageStarted",
              getCodec());
      channel.send(
          new ArrayList<Object>(Arrays.asList(instanceIdArg, webViewInstanceIdArg, urlArg)),
          channelReply -> callback.reply(null));
    }

    public void onPageFinished(
        @NonNull Long instanceIdArg,
        @NonNull Long webViewInstanceIdArg,
        @NonNull String urlArg,
        @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.WebViewClientFlutterApi.onPageFinished",
              getCodec());
      channel.send(
          new ArrayList<Object>(Arrays.asList(instanceIdArg, webViewInstanceIdArg, urlArg)),
          channelReply -> callback.reply(null));
    }

    public void onReceivedHttpError(
        @NonNull Long instanceIdArg,
        @NonNull Long webViewInstanceIdArg,
        @NonNull WebResourceRequestData requestArg,
        @NonNull WebResourceResponseData responseArg,
        @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.WebViewClientFlutterApi.onReceivedHttpError",
              getCodec());
      channel.send(
          new ArrayList<Object>(
              Arrays.asList(instanceIdArg, webViewInstanceIdArg, requestArg, responseArg)),
          channelReply -> callback.reply(null));
    }

    public void onReceivedRequestError(
        @NonNull Long instanceIdArg,
        @NonNull Long webViewInstanceIdArg,
        @NonNull WebResourceRequestData requestArg,
        @NonNull WebResourceErrorData errorArg,
        @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.WebViewClientFlutterApi.onReceivedRequestError",
              getCodec());
      channel.send(
          new ArrayList<Object>(
              Arrays.asList(instanceIdArg, webViewInstanceIdArg, requestArg, errorArg)),
          channelReply -> callback.reply(null));
    }

    public void onReceivedError(
        @NonNull Long instanceIdArg,
        @NonNull Long webViewInstanceIdArg,
        @NonNull Long errorCodeArg,
        @NonNull String descriptionArg,
        @NonNull String failingUrlArg,
        @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.WebViewClientFlutterApi.onReceivedError",
              getCodec());
      channel.send(
          new ArrayList<Object>(
              Arrays.asList(
                  instanceIdArg,
                  webViewInstanceIdArg,
                  errorCodeArg,
                  descriptionArg,
                  failingUrlArg)),
          channelReply -> callback.reply(null));
    }

    public void requestLoading(
        @NonNull Long instanceIdArg,
        @NonNull Long webViewInstanceIdArg,
        @NonNull WebResourceRequestData requestArg,
        @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.WebViewClientFlutterApi.requestLoading",
              getCodec());
      channel.send(
          new ArrayList<Object>(Arrays.asList(instanceIdArg, webViewInstanceIdArg, requestArg)),
          channelReply -> callback.reply(null));
    }

    public void urlLoading(
        @NonNull Long instanceIdArg,
        @NonNull Long webViewInstanceIdArg,
        @NonNull String urlArg,
        @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.WebViewClientFlutterApi.urlLoading",
              getCodec());
      channel.send(
          new ArrayList<Object>(Arrays.asList(instanceIdArg, webViewInstanceIdArg, urlArg)),
          channelReply -> callback.reply(null));
    }

    public void doUpdateVisitedHistory(
        @NonNull Long instanceIdArg,
        @NonNull Long webViewInstanceIdArg,
        @NonNull String urlArg,
        @NonNull Boolean isReloadArg,
        @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.WebViewClientFlutterApi.doUpdateVisitedHistory",
              getCodec());
      channel.send(
          new ArrayList<Object>(
              Arrays.asList(instanceIdArg, webViewInstanceIdArg, urlArg, isReloadArg)),
          channelReply -> callback.reply(null));
    }

    public void onReceivedHttpAuthRequest(
        @NonNull Long instanceIdArg,
        @NonNull Long webViewInstanceIdArg,
        @NonNull Long httpAuthHandlerInstanceIdArg,
        @NonNull String hostArg,
        @NonNull String realmArg,
        @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.WebViewClientFlutterApi.onReceivedHttpAuthRequest",
              getCodec());
      channel.send(
          new ArrayList<Object>(
              Arrays.asList(
                  instanceIdArg,
                  webViewInstanceIdArg,
                  httpAuthHandlerInstanceIdArg,
                  hostArg,
                  realmArg)),
          channelReply -> callback.reply(null));
    }
  }
  /** Generated interface from Pigeon that represents a handler of messages from Flutter. */
  public interface DownloadListenerHostApi {

    void create(@NonNull Long instanceId);

    /** The codec used by DownloadListenerHostApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }
    /**
     * Sets up an instance of `DownloadListenerHostApi` to handle messages through the
     * `binaryMessenger`.
     */
    static void setup(
        @NonNull BinaryMessenger binaryMessenger, @Nullable DownloadListenerHostApi api) {
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.DownloadListenerHostApi.create",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                try {
                  api.create((instanceIdArg == null) ? null : instanceIdArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
    }
  }
  /** Generated class from Pigeon that represents Flutter messages that can be called from Java. */
  public static class DownloadListenerFlutterApi {
    private final @NonNull BinaryMessenger binaryMessenger;

    public DownloadListenerFlutterApi(@NonNull BinaryMessenger argBinaryMessenger) {
      this.binaryMessenger = argBinaryMessenger;
    }

    /** Public interface for sending reply. */
    @SuppressWarnings("UnknownNullness")
    public interface Reply<T> {
      void reply(T reply);
    }
    /** The codec used by DownloadListenerFlutterApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }

    public void onDownloadStart(
        @NonNull Long instanceIdArg,
        @NonNull String urlArg,
        @NonNull String userAgentArg,
        @NonNull String contentDispositionArg,
        @NonNull String mimetypeArg,
        @NonNull Long contentLengthArg,
        @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.DownloadListenerFlutterApi.onDownloadStart",
              getCodec());
      channel.send(
          new ArrayList<Object>(
              Arrays.asList(
                  instanceIdArg,
                  urlArg,
                  userAgentArg,
                  contentDispositionArg,
                  mimetypeArg,
                  contentLengthArg)),
          channelReply -> callback.reply(null));
    }
  }
  /** Generated interface from Pigeon that represents a handler of messages from Flutter. */
  public interface WebChromeClientHostApi {

    void create(@NonNull Long instanceId);

    void setSynchronousReturnValueForOnShowFileChooser(
        @NonNull Long instanceId, @NonNull Boolean value);

    void setSynchronousReturnValueForOnConsoleMessage(
        @NonNull Long instanceId, @NonNull Boolean value);

    void setSynchronousReturnValueForOnJsAlert(@NonNull Long instanceId, @NonNull Boolean value);

    void setSynchronousReturnValueForOnJsConfirm(@NonNull Long instanceId, @NonNull Boolean value);

    void setSynchronousReturnValueForOnJsPrompt(@NonNull Long instanceId, @NonNull Boolean value);

    /** The codec used by WebChromeClientHostApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }
    /**
     * Sets up an instance of `WebChromeClientHostApi` to handle messages through the
     * `binaryMessenger`.
     */
    static void setup(
        @NonNull BinaryMessenger binaryMessenger, @Nullable WebChromeClientHostApi api) {
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebChromeClientHostApi.create",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                try {
                  api.create((instanceIdArg == null) ? null : instanceIdArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebChromeClientHostApi.setSynchronousReturnValueForOnShowFileChooser",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Boolean valueArg = (Boolean) args.get(1);
                try {
                  api.setSynchronousReturnValueForOnShowFileChooser(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(), valueArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebChromeClientHostApi.setSynchronousReturnValueForOnConsoleMessage",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Boolean valueArg = (Boolean) args.get(1);
                try {
                  api.setSynchronousReturnValueForOnConsoleMessage(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(), valueArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebChromeClientHostApi.setSynchronousReturnValueForOnJsAlert",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Boolean valueArg = (Boolean) args.get(1);
                try {
                  api.setSynchronousReturnValueForOnJsAlert(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(), valueArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebChromeClientHostApi.setSynchronousReturnValueForOnJsConfirm",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Boolean valueArg = (Boolean) args.get(1);
                try {
                  api.setSynchronousReturnValueForOnJsConfirm(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(), valueArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebChromeClientHostApi.setSynchronousReturnValueForOnJsPrompt",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                Boolean valueArg = (Boolean) args.get(1);
                try {
                  api.setSynchronousReturnValueForOnJsPrompt(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(), valueArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
    }
  }
  /** Generated interface from Pigeon that represents a handler of messages from Flutter. */
  public interface FlutterAssetManagerHostApi {

    @NonNull
    List<String> list(@NonNull String path);

    @NonNull
    String getAssetFilePathByName(@NonNull String name);

    /** The codec used by FlutterAssetManagerHostApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }
    /**
     * Sets up an instance of `FlutterAssetManagerHostApi` to handle messages through the
     * `binaryMessenger`.
     */
    static void setup(
        @NonNull BinaryMessenger binaryMessenger, @Nullable FlutterAssetManagerHostApi api) {
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.FlutterAssetManagerHostApi.list",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                String pathArg = (String) args.get(0);
                try {
                  List<String> output = api.list(pathArg);
                  wrapped.add(0, output);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.FlutterAssetManagerHostApi.getAssetFilePathByName",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                String nameArg = (String) args.get(0);
                try {
                  String output = api.getAssetFilePathByName(nameArg);
                  wrapped.add(0, output);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
    }
  }

  private static class WebChromeClientFlutterApiCodec extends StandardMessageCodec {
    public static final WebChromeClientFlutterApiCodec INSTANCE =
        new WebChromeClientFlutterApiCodec();

    private WebChromeClientFlutterApiCodec() {}

    @Override
    protected Object readValueOfType(byte type, @NonNull ByteBuffer buffer) {
      switch (type) {
        case (byte) 128:
          return ConsoleMessage.fromList((ArrayList<Object>) readValue(buffer));
        default:
          return super.readValueOfType(type, buffer);
      }
    }

    @Override
    protected void writeValue(@NonNull ByteArrayOutputStream stream, Object value) {
      if (value instanceof ConsoleMessage) {
        stream.write(128);
        writeValue(stream, ((ConsoleMessage) value).toList());
      } else {
        super.writeValue(stream, value);
      }
    }
  }

  /** Generated class from Pigeon that represents Flutter messages that can be called from Java. */
  public static class WebChromeClientFlutterApi {
    private final @NonNull BinaryMessenger binaryMessenger;

    public WebChromeClientFlutterApi(@NonNull BinaryMessenger argBinaryMessenger) {
      this.binaryMessenger = argBinaryMessenger;
    }

    /** Public interface for sending reply. */
    @SuppressWarnings("UnknownNullness")
    public interface Reply<T> {
      void reply(T reply);
    }
    /** The codec used by WebChromeClientFlutterApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return WebChromeClientFlutterApiCodec.INSTANCE;
    }

    public void onProgressChanged(
        @NonNull Long instanceIdArg,
        @NonNull Long webViewInstanceIdArg,
        @NonNull Long progressArg,
        @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.WebChromeClientFlutterApi.onProgressChanged",
              getCodec());
      channel.send(
          new ArrayList<Object>(Arrays.asList(instanceIdArg, webViewInstanceIdArg, progressArg)),
          channelReply -> callback.reply(null));
    }

    public void onShowFileChooser(
        @NonNull Long instanceIdArg,
        @NonNull Long webViewInstanceIdArg,
        @NonNull Long paramsInstanceIdArg,
        @NonNull Reply<List<String>> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.WebChromeClientFlutterApi.onShowFileChooser",
              getCodec());
      channel.send(
          new ArrayList<Object>(
              Arrays.asList(instanceIdArg, webViewInstanceIdArg, paramsInstanceIdArg)),
          channelReply -> {
            @SuppressWarnings("ConstantConditions")
            List<String> output = (List<String>) channelReply;
            callback.reply(output);
          });
    }
    /** Callback to Dart function `WebChromeClient.onPermissionRequest`. */
    public void onPermissionRequest(
        @NonNull Long instanceIdArg,
        @NonNull Long requestInstanceIdArg,
        @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.WebChromeClientFlutterApi.onPermissionRequest",
              getCodec());
      channel.send(
          new ArrayList<Object>(Arrays.asList(instanceIdArg, requestInstanceIdArg)),
          channelReply -> callback.reply(null));
    }
    /** Callback to Dart function `WebChromeClient.onShowCustomView`. */
    public void onShowCustomView(
        @NonNull Long instanceIdArg,
        @NonNull Long viewIdentifierArg,
        @NonNull Long callbackIdentifierArg,
        @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.WebChromeClientFlutterApi.onShowCustomView",
              getCodec());
      channel.send(
          new ArrayList<Object>(
              Arrays.asList(instanceIdArg, viewIdentifierArg, callbackIdentifierArg)),
          channelReply -> callback.reply(null));
    }
    /** Callback to Dart function `WebChromeClient.onHideCustomView`. */
    public void onHideCustomView(@NonNull Long instanceIdArg, @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.WebChromeClientFlutterApi.onHideCustomView",
              getCodec());
      channel.send(
          new ArrayList<Object>(Collections.singletonList(instanceIdArg)),
          channelReply -> callback.reply(null));
    }
    /** Callback to Dart function `WebChromeClient.onGeolocationPermissionsShowPrompt`. */
    public void onGeolocationPermissionsShowPrompt(
        @NonNull Long instanceIdArg,
        @NonNull Long paramsInstanceIdArg,
        @NonNull String originArg,
        @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.WebChromeClientFlutterApi.onGeolocationPermissionsShowPrompt",
              getCodec());
      channel.send(
          new ArrayList<Object>(Arrays.asList(instanceIdArg, paramsInstanceIdArg, originArg)),
          channelReply -> callback.reply(null));
    }
    /** Callback to Dart function `WebChromeClient.onGeolocationPermissionsHidePrompt`. */
    public void onGeolocationPermissionsHidePrompt(
        @NonNull Long identifierArg, @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.WebChromeClientFlutterApi.onGeolocationPermissionsHidePrompt",
              getCodec());
      channel.send(
          new ArrayList<Object>(Collections.singletonList(identifierArg)),
          channelReply -> callback.reply(null));
    }
    /** Callback to Dart function `WebChromeClient.onConsoleMessage`. */
    public void onConsoleMessage(
        @NonNull Long instanceIdArg,
        @NonNull ConsoleMessage messageArg,
        @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.WebChromeClientFlutterApi.onConsoleMessage",
              getCodec());
      channel.send(
          new ArrayList<Object>(Arrays.asList(instanceIdArg, messageArg)),
          channelReply -> callback.reply(null));
    }

    public void onJsAlert(
        @NonNull Long instanceIdArg,
        @NonNull String urlArg,
        @NonNull String messageArg,
        @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.WebChromeClientFlutterApi.onJsAlert",
              getCodec());
      channel.send(
          new ArrayList<Object>(Arrays.asList(instanceIdArg, urlArg, messageArg)),
          channelReply -> callback.reply(null));
    }

    public void onJsConfirm(
        @NonNull Long instanceIdArg,
        @NonNull String urlArg,
        @NonNull String messageArg,
        @NonNull Reply<Boolean> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.WebChromeClientFlutterApi.onJsConfirm",
              getCodec());
      channel.send(
          new ArrayList<Object>(Arrays.asList(instanceIdArg, urlArg, messageArg)),
          channelReply -> {
            @SuppressWarnings("ConstantConditions")
            Boolean output = (Boolean) channelReply;
            callback.reply(output);
          });
    }

    public void onJsPrompt(
        @NonNull Long instanceIdArg,
        @NonNull String urlArg,
        @NonNull String messageArg,
        @NonNull String defaultValueArg,
        @NonNull Reply<String> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.WebChromeClientFlutterApi.onJsPrompt",
              getCodec());
      channel.send(
          new ArrayList<Object>(Arrays.asList(instanceIdArg, urlArg, messageArg, defaultValueArg)),
          channelReply -> {
            @SuppressWarnings("ConstantConditions")
            String output = (String) channelReply;
            callback.reply(output);
          });
    }
  }
  /** Generated interface from Pigeon that represents a handler of messages from Flutter. */
  public interface WebStorageHostApi {

    void create(@NonNull Long instanceId);

    void deleteAllData(@NonNull Long instanceId);

    /** The codec used by WebStorageHostApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }
    /**
     * Sets up an instance of `WebStorageHostApi` to handle messages through the `binaryMessenger`.
     */
    static void setup(@NonNull BinaryMessenger binaryMessenger, @Nullable WebStorageHostApi api) {
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebStorageHostApi.create",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                try {
                  api.create((instanceIdArg == null) ? null : instanceIdArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.WebStorageHostApi.deleteAllData",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                try {
                  api.deleteAllData((instanceIdArg == null) ? null : instanceIdArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
    }
  }
  /**
   * Handles callbacks methods for the native Java FileChooserParams class.
   *
   * <p>See
   * https://developer.android.com/reference/android/webkit/WebChromeClient.FileChooserParams.
   *
   * <p>Generated class from Pigeon that represents Flutter messages that can be called from Java.
   */
  public static class FileChooserParamsFlutterApi {
    private final @NonNull BinaryMessenger binaryMessenger;

    public FileChooserParamsFlutterApi(@NonNull BinaryMessenger argBinaryMessenger) {
      this.binaryMessenger = argBinaryMessenger;
    }

    /** Public interface for sending reply. */
    @SuppressWarnings("UnknownNullness")
    public interface Reply<T> {
      void reply(T reply);
    }
    /** The codec used by FileChooserParamsFlutterApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }

    public void create(
        @NonNull Long instanceIdArg,
        @NonNull Boolean isCaptureEnabledArg,
        @NonNull List<String> acceptTypesArg,
        @NonNull FileChooserMode modeArg,
        @Nullable String filenameHintArg,
        @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.FileChooserParamsFlutterApi.create",
              getCodec());
      channel.send(
          new ArrayList<Object>(
              Arrays.asList(
                  instanceIdArg,
                  isCaptureEnabledArg,
                  acceptTypesArg,
                  modeArg.index,
                  filenameHintArg)),
          channelReply -> callback.reply(null));
    }
  }
  /**
   * Host API for `PermissionRequest`.
   *
   * <p>This class may handle instantiating and adding native object instances that are attached to
   * a Dart instance or handle method calls on the associated native class or an instance of the
   * class.
   *
   * <p>See https://developer.android.com/reference/android/webkit/PermissionRequest.
   *
   * <p>Generated interface from Pigeon that represents a handler of messages from Flutter.
   */
  public interface PermissionRequestHostApi {
    /** Handles Dart method `PermissionRequest.grant`. */
    void grant(@NonNull Long instanceId, @NonNull List<String> resources);
    /** Handles Dart method `PermissionRequest.deny`. */
    void deny(@NonNull Long instanceId);

    /** The codec used by PermissionRequestHostApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }
    /**
     * Sets up an instance of `PermissionRequestHostApi` to handle messages through the
     * `binaryMessenger`.
     */
    static void setup(
        @NonNull BinaryMessenger binaryMessenger, @Nullable PermissionRequestHostApi api) {
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.PermissionRequestHostApi.grant",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                List<String> resourcesArg = (List<String>) args.get(1);
                try {
                  api.grant(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(), resourcesArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.PermissionRequestHostApi.deny",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                try {
                  api.deny((instanceIdArg == null) ? null : instanceIdArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
    }
  }
  /**
   * Flutter API for `PermissionRequest`.
   *
   * <p>This class may handle instantiating and adding Dart instances that are attached to a native
   * instance or receiving callback methods from an overridden native class.
   *
   * <p>See https://developer.android.com/reference/android/webkit/PermissionRequest.
   *
   * <p>Generated class from Pigeon that represents Flutter messages that can be called from Java.
   */
  public static class PermissionRequestFlutterApi {
    private final @NonNull BinaryMessenger binaryMessenger;

    public PermissionRequestFlutterApi(@NonNull BinaryMessenger argBinaryMessenger) {
      this.binaryMessenger = argBinaryMessenger;
    }

    /** Public interface for sending reply. */
    @SuppressWarnings("UnknownNullness")
    public interface Reply<T> {
      void reply(T reply);
    }
    /** The codec used by PermissionRequestFlutterApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }
    /** Create a new Dart instance and add it to the `InstanceManager`. */
    public void create(
        @NonNull Long instanceIdArg,
        @NonNull List<String> resourcesArg,
        @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.PermissionRequestFlutterApi.create",
              getCodec());
      channel.send(
          new ArrayList<Object>(Arrays.asList(instanceIdArg, resourcesArg)),
          channelReply -> callback.reply(null));
    }
  }
  /**
   * Host API for `CustomViewCallback`.
   *
   * <p>This class may handle instantiating and adding native object instances that are attached to
   * a Dart instance or handle method calls on the associated native class or an instance of the
   * class.
   *
   * <p>See
   * https://developer.android.com/reference/android/webkit/WebChromeClient.CustomViewCallback.
   *
   * <p>Generated interface from Pigeon that represents a handler of messages from Flutter.
   */
  public interface CustomViewCallbackHostApi {
    /** Handles Dart method `CustomViewCallback.onCustomViewHidden`. */
    void onCustomViewHidden(@NonNull Long identifier);

    /** The codec used by CustomViewCallbackHostApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }
    /**
     * Sets up an instance of `CustomViewCallbackHostApi` to handle messages through the
     * `binaryMessenger`.
     */
    static void setup(
        @NonNull BinaryMessenger binaryMessenger, @Nullable CustomViewCallbackHostApi api) {
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.CustomViewCallbackHostApi.onCustomViewHidden",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number identifierArg = (Number) args.get(0);
                try {
                  api.onCustomViewHidden(
                      (identifierArg == null) ? null : identifierArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
    }
  }
  /**
   * Flutter API for `CustomViewCallback`.
   *
   * <p>This class may handle instantiating and adding Dart instances that are attached to a native
   * instance or receiving callback methods from an overridden native class.
   *
   * <p>See
   * https://developer.android.com/reference/android/webkit/WebChromeClient.CustomViewCallback.
   *
   * <p>Generated class from Pigeon that represents Flutter messages that can be called from Java.
   */
  public static class CustomViewCallbackFlutterApi {
    private final @NonNull BinaryMessenger binaryMessenger;

    public CustomViewCallbackFlutterApi(@NonNull BinaryMessenger argBinaryMessenger) {
      this.binaryMessenger = argBinaryMessenger;
    }

    /** Public interface for sending reply. */
    @SuppressWarnings("UnknownNullness")
    public interface Reply<T> {
      void reply(T reply);
    }
    /** The codec used by CustomViewCallbackFlutterApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }
    /** Create a new Dart instance and add it to the `InstanceManager`. */
    public void create(@NonNull Long identifierArg, @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.CustomViewCallbackFlutterApi.create",
              getCodec());
      channel.send(
          new ArrayList<Object>(Collections.singletonList(identifierArg)),
          channelReply -> callback.reply(null));
    }
  }
  /**
   * Flutter API for `View`.
   *
   * <p>This class may handle instantiating and adding Dart instances that are attached to a native
   * instance or receiving callback methods from an overridden native class.
   *
   * <p>See https://developer.android.com/reference/android/view/View.
   *
   * <p>Generated class from Pigeon that represents Flutter messages that can be called from Java.
   */
  public static class ViewFlutterApi {
    private final @NonNull BinaryMessenger binaryMessenger;

    public ViewFlutterApi(@NonNull BinaryMessenger argBinaryMessenger) {
      this.binaryMessenger = argBinaryMessenger;
    }

    /** Public interface for sending reply. */
    @SuppressWarnings("UnknownNullness")
    public interface Reply<T> {
      void reply(T reply);
    }
    /** The codec used by ViewFlutterApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }
    /** Create a new Dart instance and add it to the `InstanceManager`. */
    public void create(@NonNull Long identifierArg, @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.ViewFlutterApi.create",
              getCodec());
      channel.send(
          new ArrayList<Object>(Collections.singletonList(identifierArg)),
          channelReply -> callback.reply(null));
    }
  }
  /**
   * Host API for `GeolocationPermissionsCallback`.
   *
   * <p>This class may handle instantiating and adding native object instances that are attached to
   * a Dart instance or handle method calls on the associated native class or an instance of the
   * class.
   *
   * <p>See https://developer.android.com/reference/android/webkit/GeolocationPermissions.Callback.
   *
   * <p>Generated interface from Pigeon that represents a handler of messages from Flutter.
   */
  public interface GeolocationPermissionsCallbackHostApi {
    /** Handles Dart method `GeolocationPermissionsCallback.invoke`. */
    void invoke(
        @NonNull Long instanceId,
        @NonNull String origin,
        @NonNull Boolean allow,
        @NonNull Boolean retain);

    /** The codec used by GeolocationPermissionsCallbackHostApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }
    /**
     * Sets up an instance of `GeolocationPermissionsCallbackHostApi` to handle messages through the
     * `binaryMessenger`.
     */
    static void setup(
        @NonNull BinaryMessenger binaryMessenger,
        @Nullable GeolocationPermissionsCallbackHostApi api) {
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.GeolocationPermissionsCallbackHostApi.invoke",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                String originArg = (String) args.get(1);
                Boolean allowArg = (Boolean) args.get(2);
                Boolean retainArg = (Boolean) args.get(3);
                try {
                  api.invoke(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(),
                      originArg,
                      allowArg,
                      retainArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
    }
  }
  /**
   * Flutter API for `GeolocationPermissionsCallback`.
   *
   * <p>This class may handle instantiating and adding Dart instances that are attached to a native
   * instance or receiving callback methods from an overridden native class.
   *
   * <p>See https://developer.android.com/reference/android/webkit/GeolocationPermissions.Callback.
   *
   * <p>Generated class from Pigeon that represents Flutter messages that can be called from Java.
   */
  public static class GeolocationPermissionsCallbackFlutterApi {
    private final @NonNull BinaryMessenger binaryMessenger;

    public GeolocationPermissionsCallbackFlutterApi(@NonNull BinaryMessenger argBinaryMessenger) {
      this.binaryMessenger = argBinaryMessenger;
    }

    /** Public interface for sending reply. */
    @SuppressWarnings("UnknownNullness")
    public interface Reply<T> {
      void reply(T reply);
    }
    /** The codec used by GeolocationPermissionsCallbackFlutterApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }
    /** Create a new Dart instance and add it to the `InstanceManager`. */
    public void create(@NonNull Long instanceIdArg, @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.GeolocationPermissionsCallbackFlutterApi.create",
              getCodec());
      channel.send(
          new ArrayList<Object>(Collections.singletonList(instanceIdArg)),
          channelReply -> callback.reply(null));
    }
  }
  /**
   * Host API for `HttpAuthHandler`.
   *
   * <p>This class may handle instantiating and adding native object instances that are attached to
   * a Dart instance or handle method calls on the associated native class or an instance of the
   * class.
   *
   * <p>See https://developer.android.com/reference/android/webkit/HttpAuthHandler.
   *
   * <p>Generated interface from Pigeon that represents a handler of messages from Flutter.
   */
  public interface HttpAuthHandlerHostApi {
    /** Handles Dart method `HttpAuthHandler.useHttpAuthUsernamePassword`. */
    @NonNull
    Boolean useHttpAuthUsernamePassword(@NonNull Long instanceId);
    /** Handles Dart method `HttpAuthHandler.cancel`. */
    void cancel(@NonNull Long instanceId);
    /** Handles Dart method `HttpAuthHandler.proceed`. */
    void proceed(@NonNull Long instanceId, @NonNull String username, @NonNull String password);

    /** The codec used by HttpAuthHandlerHostApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }
    /**
     * Sets up an instance of `HttpAuthHandlerHostApi` to handle messages through the
     * `binaryMessenger`.
     */
    static void setup(
        @NonNull BinaryMessenger binaryMessenger, @Nullable HttpAuthHandlerHostApi api) {
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.HttpAuthHandlerHostApi.useHttpAuthUsernamePassword",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                try {
                  Boolean output =
                      api.useHttpAuthUsernamePassword(
                          (instanceIdArg == null) ? null : instanceIdArg.longValue());
                  wrapped.add(0, output);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.HttpAuthHandlerHostApi.cancel",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                try {
                  api.cancel((instanceIdArg == null) ? null : instanceIdArg.longValue());
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.webview_flutter_android.HttpAuthHandlerHostApi.proceed",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Number instanceIdArg = (Number) args.get(0);
                String usernameArg = (String) args.get(1);
                String passwordArg = (String) args.get(2);
                try {
                  api.proceed(
                      (instanceIdArg == null) ? null : instanceIdArg.longValue(),
                      usernameArg,
                      passwordArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
    }
  }
  /**
   * Flutter API for `HttpAuthHandler`.
   *
   * <p>This class may handle instantiating and adding Dart instances that are attached to a native
   * instance or receiving callback methods from an overridden native class.
   *
   * <p>See https://developer.android.com/reference/android/webkit/HttpAuthHandler.
   *
   * <p>Generated class from Pigeon that represents Flutter messages that can be called from Java.
   */
  public static class HttpAuthHandlerFlutterApi {
    private final @NonNull BinaryMessenger binaryMessenger;

    public HttpAuthHandlerFlutterApi(@NonNull BinaryMessenger argBinaryMessenger) {
      this.binaryMessenger = argBinaryMessenger;
    }

    /** Public interface for sending reply. */
    @SuppressWarnings("UnknownNullness")
    public interface Reply<T> {
      void reply(T reply);
    }
    /** The codec used by HttpAuthHandlerFlutterApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }
    /** Create a new Dart instance and add it to the `InstanceManager`. */
    public void create(@NonNull Long instanceIdArg, @NonNull Reply<Void> callback) {
      BasicMessageChannel<Object> channel =
          new BasicMessageChannel<>(
              binaryMessenger,
              "dev.flutter.pigeon.webview_flutter_android.HttpAuthHandlerFlutterApi.create",
              getCodec());
      channel.send(
          new ArrayList<Object>(Collections.singletonList(instanceIdArg)),
          channelReply -> callback.reply(null));
    }
  }
}
