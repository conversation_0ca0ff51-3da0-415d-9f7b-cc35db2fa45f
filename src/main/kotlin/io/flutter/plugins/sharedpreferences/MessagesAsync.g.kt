// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.
// Autogenerated from <PERSON><PERSON> (v24.2.0), do not edit directly.
// See also: https://pub.dev/packages/pigeon
@file:Suppress("UNCHECKED_CAST", "ArrayInDataClass")

package io.flutter.plugins.sharedpreferences

import android.util.Log
import io.flutter.plugin.common.BasicMessageChannel
import io.flutter.plugin.common.BinaryMessenger
import io.flutter.plugin.common.MessageCodec
import io.flutter.plugin.common.StandardMessageCodec
import java.io.ByteArrayOutputStream
import java.nio.ByteBuffer

private fun wrapResult(result: Any?): List<Any?> {
  return listOf(result)
}

private fun wrapError(exception: Throwable): List<Any?> {
  return if (exception is SharedPreferencesError) {
    listOf(exception.code, exception.message, exception.details)
  } else {
    listOf(
        exception.javaClass.simpleName,
        exception.toString(),
        "Cause: " + exception.cause + ", Stacktrace: " + Log.getStackTraceString(exception))
  }
}

/**
 * Error class for passing custom error details to Flutter via a thrown PlatformException.
 *
 * @property code The error code.
 * @property message The error message.
 * @property details The error details. Must be a datatype supported by the api codec.
 */
class SharedPreferencesError(
    val code: String,
    override val message: String? = null,
    val details: Any? = null
) : Throwable()

/** Possible types found during a getStringList call. */
enum class StringListLookupResultType(val raw: Int) {
  /** A deprecated platform-side encoding string list. */
  PLATFORM_ENCODED(0),
  /** A JSON-encoded string list. */
  JSON_ENCODED(1),
  /** A string that doesn't have the expected encoding prefix. */
  UNEXPECTED_STRING(2);

  companion object {
    fun ofRaw(raw: Int): StringListLookupResultType? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

/** Generated class from Pigeon that represents data sent in messages. */
data class SharedPreferencesPigeonOptions(val fileName: String? = null, val useDataStore: Boolean) {
  companion object {
    fun fromList(pigeonVar_list: List<Any?>): SharedPreferencesPigeonOptions {
      val fileName = pigeonVar_list[0] as String?
      val useDataStore = pigeonVar_list[1] as Boolean
      return SharedPreferencesPigeonOptions(fileName, useDataStore)
    }
  }

  fun toList(): List<Any?> {
    return listOf(
        fileName,
        useDataStore,
    )
  }
}

/** Generated class from Pigeon that represents data sent in messages. */
data class StringListResult(
    /** The JSON-encoded stored value, or null if something else was found. */
    val jsonEncodedValue: String? = null,
    /** The type of value found. */
    val type: StringListLookupResultType
) {
  companion object {
    fun fromList(pigeonVar_list: List<Any?>): StringListResult {
      val jsonEncodedValue = pigeonVar_list[0] as String?
      val type = pigeonVar_list[1] as StringListLookupResultType
      return StringListResult(jsonEncodedValue, type)
    }
  }

  fun toList(): List<Any?> {
    return listOf(
        jsonEncodedValue,
        type,
    )
  }
}

private open class MessagesAsyncPigeonCodec : StandardMessageCodec() {
  override fun readValueOfType(type: Byte, buffer: ByteBuffer): Any? {
    return when (type) {
      129.toByte() -> {
        return (readValue(buffer) as Long?)?.let { StringListLookupResultType.ofRaw(it.toInt()) }
      }
      130.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          SharedPreferencesPigeonOptions.fromList(it)
        }
      }
      131.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let { StringListResult.fromList(it) }
      }
      else -> super.readValueOfType(type, buffer)
    }
  }

  override fun writeValue(stream: ByteArrayOutputStream, value: Any?) {
    when (value) {
      is StringListLookupResultType -> {
        stream.write(129)
        writeValue(stream, value.raw)
      }
      is SharedPreferencesPigeonOptions -> {
        stream.write(130)
        writeValue(stream, value.toList())
      }
      is StringListResult -> {
        stream.write(131)
        writeValue(stream, value.toList())
      }
      else -> super.writeValue(stream, value)
    }
  }
}

/** Generated interface from Pigeon that represents a handler of messages from Flutter. */
interface SharedPreferencesAsyncApi {
  /** Adds property to shared preferences data set of type bool. */
  fun setBool(key: String, value: Boolean, options: SharedPreferencesPigeonOptions)
  /** Adds property to shared preferences data set of type String. */
  fun setString(key: String, value: String, options: SharedPreferencesPigeonOptions)
  /** Adds property to shared preferences data set of type int. */
  fun setInt(key: String, value: Long, options: SharedPreferencesPigeonOptions)
  /** Adds property to shared preferences data set of type double. */
  fun setDouble(key: String, value: Double, options: SharedPreferencesPigeonOptions)
  /** Adds property to shared preferences data set of type List<String>. */
  fun setEncodedStringList(key: String, value: String, options: SharedPreferencesPigeonOptions)
  /**
   * Adds property to shared preferences data set of type List<String>.
   *
   * Deprecated, this is only here for testing purposes.
   */
  fun setDeprecatedStringList(
      key: String,
      value: List<String>,
      options: SharedPreferencesPigeonOptions
  )
  /** Gets individual String value stored with [key], if any. */
  fun getString(key: String, options: SharedPreferencesPigeonOptions): String?
  /** Gets individual void value stored with [key], if any. */
  fun getBool(key: String, options: SharedPreferencesPigeonOptions): Boolean?
  /** Gets individual double value stored with [key], if any. */
  fun getDouble(key: String, options: SharedPreferencesPigeonOptions): Double?
  /** Gets individual int value stored with [key], if any. */
  fun getInt(key: String, options: SharedPreferencesPigeonOptions): Long?
  /** Gets individual List<String> value stored with [key], if any. */
  fun getPlatformEncodedStringList(
      key: String,
      options: SharedPreferencesPigeonOptions
  ): List<String>?
  /** Gets the JSON-encoded List<String> value stored with [key], if any. */
  fun getStringList(key: String, options: SharedPreferencesPigeonOptions): StringListResult?
  /** Removes all properties from shared preferences data set with matching prefix. */
  fun clear(allowList: List<String>?, options: SharedPreferencesPigeonOptions)
  /** Gets all properties from shared preferences data set with matching prefix. */
  fun getAll(allowList: List<String>?, options: SharedPreferencesPigeonOptions): Map<String, Any>
  /** Gets all properties from shared preferences data set with matching prefix. */
  fun getKeys(allowList: List<String>?, options: SharedPreferencesPigeonOptions): List<String>

  companion object {
    /** The codec used by SharedPreferencesAsyncApi. */
    val codec: MessageCodec<Any?> by lazy { MessagesAsyncPigeonCodec() }
    /**
     * Sets up an instance of `SharedPreferencesAsyncApi` to handle messages through the
     * `binaryMessenger`.
     */
    @JvmOverloads
    fun setUp(
        binaryMessenger: BinaryMessenger,
        api: SharedPreferencesAsyncApi?,
        messageChannelSuffix: String = ""
    ) {
      val separatedMessageChannelSuffix =
          if (messageChannelSuffix.isNotEmpty()) ".$messageChannelSuffix" else ""
      val taskQueue = binaryMessenger.makeBackgroundTaskQueue()
      run {
        val channel =
            BasicMessageChannel<Any?>(
                binaryMessenger,
                "dev.flutter.pigeon.shared_preferences_android.SharedPreferencesAsyncApi.setBool$separatedMessageChannelSuffix",
                codec,
                taskQueue)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val keyArg = args[0] as String
            val valueArg = args[1] as Boolean
            val optionsArg = args[2] as SharedPreferencesPigeonOptions
            val wrapped: List<Any?> =
                try {
                  api.setBool(keyArg, valueArg, optionsArg)
                  listOf(null)
                } catch (exception: Throwable) {
                  wrapError(exception)
                }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel =
            BasicMessageChannel<Any?>(
                binaryMessenger,
                "dev.flutter.pigeon.shared_preferences_android.SharedPreferencesAsyncApi.setString$separatedMessageChannelSuffix",
                codec,
                taskQueue)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val keyArg = args[0] as String
            val valueArg = args[1] as String
            val optionsArg = args[2] as SharedPreferencesPigeonOptions
            val wrapped: List<Any?> =
                try {
                  api.setString(keyArg, valueArg, optionsArg)
                  listOf(null)
                } catch (exception: Throwable) {
                  wrapError(exception)
                }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel =
            BasicMessageChannel<Any?>(
                binaryMessenger,
                "dev.flutter.pigeon.shared_preferences_android.SharedPreferencesAsyncApi.setInt$separatedMessageChannelSuffix",
                codec,
                taskQueue)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val keyArg = args[0] as String
            val valueArg = args[1] as Long
            val optionsArg = args[2] as SharedPreferencesPigeonOptions
            val wrapped: List<Any?> =
                try {
                  api.setInt(keyArg, valueArg, optionsArg)
                  listOf(null)
                } catch (exception: Throwable) {
                  wrapError(exception)
                }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel =
            BasicMessageChannel<Any?>(
                binaryMessenger,
                "dev.flutter.pigeon.shared_preferences_android.SharedPreferencesAsyncApi.setDouble$separatedMessageChannelSuffix",
                codec,
                taskQueue)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val keyArg = args[0] as String
            val valueArg = args[1] as Double
            val optionsArg = args[2] as SharedPreferencesPigeonOptions
            val wrapped: List<Any?> =
                try {
                  api.setDouble(keyArg, valueArg, optionsArg)
                  listOf(null)
                } catch (exception: Throwable) {
                  wrapError(exception)
                }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel =
            BasicMessageChannel<Any?>(
                binaryMessenger,
                "dev.flutter.pigeon.shared_preferences_android.SharedPreferencesAsyncApi.setEncodedStringList$separatedMessageChannelSuffix",
                codec,
                taskQueue)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val keyArg = args[0] as String
            val valueArg = args[1] as String
            val optionsArg = args[2] as SharedPreferencesPigeonOptions
            val wrapped: List<Any?> =
                try {
                  api.setEncodedStringList(keyArg, valueArg, optionsArg)
                  listOf(null)
                } catch (exception: Throwable) {
                  wrapError(exception)
                }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel =
            BasicMessageChannel<Any?>(
                binaryMessenger,
                "dev.flutter.pigeon.shared_preferences_android.SharedPreferencesAsyncApi.setDeprecatedStringList$separatedMessageChannelSuffix",
                codec,
                taskQueue)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val keyArg = args[0] as String
            val valueArg = args[1] as List<String>
            val optionsArg = args[2] as SharedPreferencesPigeonOptions
            val wrapped: List<Any?> =
                try {
                  api.setDeprecatedStringList(keyArg, valueArg, optionsArg)
                  listOf(null)
                } catch (exception: Throwable) {
                  wrapError(exception)
                }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel =
            BasicMessageChannel<Any?>(
                binaryMessenger,
                "dev.flutter.pigeon.shared_preferences_android.SharedPreferencesAsyncApi.getString$separatedMessageChannelSuffix",
                codec,
                taskQueue)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val keyArg = args[0] as String
            val optionsArg = args[1] as SharedPreferencesPigeonOptions
            val wrapped: List<Any?> =
                try {
                  listOf(api.getString(keyArg, optionsArg))
                } catch (exception: Throwable) {
                  wrapError(exception)
                }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel =
            BasicMessageChannel<Any?>(
                binaryMessenger,
                "dev.flutter.pigeon.shared_preferences_android.SharedPreferencesAsyncApi.getBool$separatedMessageChannelSuffix",
                codec,
                taskQueue)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val keyArg = args[0] as String
            val optionsArg = args[1] as SharedPreferencesPigeonOptions
            val wrapped: List<Any?> =
                try {
                  listOf(api.getBool(keyArg, optionsArg))
                } catch (exception: Throwable) {
                  wrapError(exception)
                }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel =
            BasicMessageChannel<Any?>(
                binaryMessenger,
                "dev.flutter.pigeon.shared_preferences_android.SharedPreferencesAsyncApi.getDouble$separatedMessageChannelSuffix",
                codec,
                taskQueue)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val keyArg = args[0] as String
            val optionsArg = args[1] as SharedPreferencesPigeonOptions
            val wrapped: List<Any?> =
                try {
                  listOf(api.getDouble(keyArg, optionsArg))
                } catch (exception: Throwable) {
                  wrapError(exception)
                }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel =
            BasicMessageChannel<Any?>(
                binaryMessenger,
                "dev.flutter.pigeon.shared_preferences_android.SharedPreferencesAsyncApi.getInt$separatedMessageChannelSuffix",
                codec,
                taskQueue)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val keyArg = args[0] as String
            val optionsArg = args[1] as SharedPreferencesPigeonOptions
            val wrapped: List<Any?> =
                try {
                  listOf(api.getInt(keyArg, optionsArg))
                } catch (exception: Throwable) {
                  wrapError(exception)
                }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel =
            BasicMessageChannel<Any?>(
                binaryMessenger,
                "dev.flutter.pigeon.shared_preferences_android.SharedPreferencesAsyncApi.getPlatformEncodedStringList$separatedMessageChannelSuffix",
                codec,
                taskQueue)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val keyArg = args[0] as String
            val optionsArg = args[1] as SharedPreferencesPigeonOptions
            val wrapped: List<Any?> =
                try {
                  listOf(api.getPlatformEncodedStringList(keyArg, optionsArg))
                } catch (exception: Throwable) {
                  wrapError(exception)
                }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel =
            BasicMessageChannel<Any?>(
                binaryMessenger,
                "dev.flutter.pigeon.shared_preferences_android.SharedPreferencesAsyncApi.getStringList$separatedMessageChannelSuffix",
                codec,
                taskQueue)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val keyArg = args[0] as String
            val optionsArg = args[1] as SharedPreferencesPigeonOptions
            val wrapped: List<Any?> =
                try {
                  listOf(api.getStringList(keyArg, optionsArg))
                } catch (exception: Throwable) {
                  wrapError(exception)
                }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel =
            BasicMessageChannel<Any?>(
                binaryMessenger,
                "dev.flutter.pigeon.shared_preferences_android.SharedPreferencesAsyncApi.clear$separatedMessageChannelSuffix",
                codec,
                taskQueue)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val allowListArg = args[0] as List<String>?
            val optionsArg = args[1] as SharedPreferencesPigeonOptions
            val wrapped: List<Any?> =
                try {
                  api.clear(allowListArg, optionsArg)
                  listOf(null)
                } catch (exception: Throwable) {
                  wrapError(exception)
                }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel =
            BasicMessageChannel<Any?>(
                binaryMessenger,
                "dev.flutter.pigeon.shared_preferences_android.SharedPreferencesAsyncApi.getAll$separatedMessageChannelSuffix",
                codec,
                taskQueue)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val allowListArg = args[0] as List<String>?
            val optionsArg = args[1] as SharedPreferencesPigeonOptions
            val wrapped: List<Any?> =
                try {
                  listOf(api.getAll(allowListArg, optionsArg))
                } catch (exception: Throwable) {
                  wrapError(exception)
                }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel =
            BasicMessageChannel<Any?>(
                binaryMessenger,
                "dev.flutter.pigeon.shared_preferences_android.SharedPreferencesAsyncApi.getKeys$separatedMessageChannelSuffix",
                codec,
                taskQueue)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val allowListArg = args[0] as List<String>?
            val optionsArg = args[1] as SharedPreferencesPigeonOptions
            val wrapped: List<Any?> =
                try {
                  listOf(api.getKeys(allowListArg, optionsArg))
                } catch (exception: Throwable) {
                  wrapError(exception)
                }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }
}
