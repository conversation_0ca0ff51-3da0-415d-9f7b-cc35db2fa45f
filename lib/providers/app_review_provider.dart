import 'package:flutter/foundation.dart';

/// 应用过审状态管理
/// 负责管理应用审核状态，提供全局访问
class AppReviewProvider extends ChangeNotifier {
  bool? _inReview;
  bool _isLoaded = false;

  // Getters
  bool? get inReview => _inReview;
  bool get isLoaded => _isLoaded;
  
  /// 是否处于审核状态
  bool get isInReview => _inReview ?? false;
  
  /// 是否为正常状态（非审核状态）
  bool get isNormal => !isInReview;

  /// 更新过审状态
  void updateReviewStatus(bool inReview) {
    if (_inReview != inReview) {
      _inReview = inReview;
      _isLoaded = true;
      debugPrint('过审状态更新: ${inReview ? "审核中" : "正常"}');
      notifyListeners();
    }
  }

  /// 重置状态
  void reset() {
    _inReview = null;
    _isLoaded = false;
    notifyListeners();
  }

  /// 获取状态描述
  String get statusDescription {
    if (!_isLoaded) return '状态未加载';
    return _inReview == true ? '审核中' : '正常';
  }
}