import 'package:flutter/services.dart';

/// HTML文件加载器
class HtmlLoader {
  /// 缓存已加载的HTML内容
  static final Map<String, String> _cache = {};

  /// 从assets加载HTML文件
  static Future<String> loadHtml(String assetPath) async {
    // 检查缓存
    if (_cache.containsKey(assetPath)) {
      return _cache[assetPath]!;
    }

    try {
      // 从assets加载文件
      final String htmlContent = await rootBundle.loadString(assetPath);
      
      // 缓存内容
      _cache[assetPath] = htmlContent;
      
      return htmlContent;
    } catch (e) {
      // 如果加载失败，返回默认的错误页面
      return _getErrorHtml('加载HTML文件失败: $e');
    }
  }

  /// 获取演示HTML
  static Future<String> getDemoHtml() async {
    return await loadHtml('assets/webview/demo.html');
  }

  /// 获取简单测试HTML（从文件加载）
  static Future<String> getSimpleTestFromFile() async {
    return await loadHtml('assets/webview/simple_test.html');
  }

  /// 清空缓存
  static void clearCache() {
    _cache.clear();
  }

  /// 预加载HTML文件
  static Future<void> preloadHtml(List<String> assetPaths) async {
    for (String path in assetPaths) {
      await loadHtml(path);
    }
  }

  /// 预加载所有WebView HTML文件
  static Future<void> preloadAllWebViewHtml() async {
    await preloadHtml([
      'assets/webview/demo.html',
      'assets/webview/simple_test.html',
    ]);
  }

  /// 获取错误页面HTML
  static String _getErrorHtml(String errorMessage) {
    return '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加载错误</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 40px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            text-align: center;
            min-height: 100vh;
            margin: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .error-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            max-width: 500px;
        }
        .error-icon {
            font-size: 64px;
            margin-bottom: 20px;
        }
        .error-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .error-message {
            font-size: 16px;
            line-height: 1.5;
            opacity: 0.9;
        }
        button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 20px;
            transition: all 0.3s;
        }
        button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">⚠️</div>
        <div class="error-title">页面加载失败</div>
        <div class="error-message">$errorMessage</div>
        <button onclick="location.reload()">重新加载</button>
    </div>
</body>
</html>
    ''';
  }

  /// 获取简单的测试HTML
  static String getSimpleTestHtml() {
    return '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            text-align: center;
        }
        .container {
            max-width: 400px;
            margin: 50px auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }
        button {
            background: #00b894;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: background 0.3s;
        }
        button:hover {
            background: #00a085;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 简单测试页面</h1>
        <p>这是一个简单的WebView测试页面</p>
        
        <button onclick="testMessage()">发送测试消息</button>
        <button onclick="testAlert()">显示提示</button>
        
        <div id="result" style="margin-top: 20px; padding: 10px; background: rgba(0,0,0,0.2); border-radius: 5px; display: none;">
            测试结果显示在这里
        </div>
    </div>

    <script>
        function testMessage() {
            if (window.FlutterJSBridge) {
                window.FlutterJSBridge.postMessage({
                    type: 'userAction',
                    method: 'simpleTest',
                    data: { message: '简单测试消息', timestamp: Date.now() }
                });
                showResult('消息已发送到Flutter');
            } else {
                showResult('FlutterJSBridge 未找到');
            }
        }
        
        function testAlert() {
            alert('这是一个简单的提示框');
        }
        
        function showResult(message) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.style.display = 'block';
            setTimeout(() => {
                resultDiv.style.display = 'none';
            }, 3000);
        }
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('简单测试页面加载完成');
        });
    </script>
</body>
</html>
    ''';
  }
} 