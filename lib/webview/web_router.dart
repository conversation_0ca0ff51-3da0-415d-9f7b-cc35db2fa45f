import 'package:dlyz_flutter/webview/webview.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class WebRouter {
  static void jumpToWebPage(
    BuildContext context,
    String url,
    String title,
    Map<String, dynamic> params,
  ) {
    // 将 params 拼接到 URL 后面
    String finalUrl = _buildUrlWithParams(url, params);

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewDialog(title: title, url: finalUrl),
      ),
    );
  }

  /// 将参数拼接到 URL 后面
  static String _buildUrlWithParams(String url, Map<String, dynamic> params) {
    // 检查 URL 是否为空或无效
    if (url.isEmpty) {
      return url;
    }

    // 检查参数是否为空
    if (params.isEmpty) {
      return url;
    }

    try {
      // 解析现有的 URI
      Uri uri = Uri.parse(url);

      // 将 params 转换为查询参数
      Map<String, String> queryParams = {};

      // 保留原有的查询参数
      queryParams.addAll(uri.queryParameters);

      // 添加新的参数
      params.forEach((key, value) {
        if (value != null && key.isNotEmpty) {
          queryParams[key] = value.toString();
        }
      });

      // 重新构建 URI
      Uri newUri = uri.replace(queryParameters: queryParams);

      return newUri.toString();
    } catch (e) {
      // URI 解析失败时，尝试简单拼接参数
      print('URI 解析失败: $e');
      return url;
    }
  }
}
