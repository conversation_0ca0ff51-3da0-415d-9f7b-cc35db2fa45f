import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'webview_wrapper.dart';
import 'js_bridge.dart';
import 'html_loader.dart';

/// WebView示例页面
class WebViewPage extends StatefulWidget {
  final String? title;
  final String? url;
  final String? html;

  const WebViewPage({
    Key? key,
    this.title = 'WebView',
    this.url,
    this.html,
  }) : super(key: key);

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {
  late JSBridge _jsBridge;
  String _currentTitle = '';
  final bool _canGoBack = false;
  final bool _canGoForward = false;
  late WebViewWrapperController _webViewController;
  String? _demoHtml;

  @override
  void initState() {
    super.initState();
    _currentTitle = widget.title ?? 'WebView';
    _webViewController = WebViewWrapperController();
    _setupJSBridge();
    _loadDemoHtml();
  }

  /// 预加载演示HTML内容
  Future<void> _loadDemoHtml() async {
    try {
      _demoHtml = await HtmlLoader.getDemoHtml();
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      debugPrint('加载演示HTML失败: $e');
      _demoHtml = HtmlLoader.getSimpleTestHtml();
      if (mounted) {
        setState(() {});
      }
    }
  }

  void _setupJSBridge() {
    _jsBridge = JSBridge(context, webViewController: _webViewController);

    _jsBridge.registerHandler("setTitle", (message) {
      setState(() {
        _currentTitle = message.data["title"];
      });
    });

    _jsBridge.registerHandler("navigate", (message) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => WebViewPage(
            title: 'New Page',
            url: message.data["url"],
          ),
        ),
      );
    });

    _jsBridge.registerHandler("getDeviceInfo", (message) {
      final deviceInfo = {
        'platform': Theme.of(context).platform.name,
        'screenWidth': MediaQuery.of(context).size.width,
        'screenHeight': MediaQuery.of(context).size.height,
        'devicePixelRatio': MediaQuery.of(context).devicePixelRatio,
      };

      Fluttertoast.showToast(
        msg: "$deviceInfo",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
      );
    });
  }

  Future<void> _sendMessageToWebView(Map<String, dynamic> message) async {
    await _webViewController.sendMessageToWebView(message);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_currentTitle),
        actions: [
                     IconButton(
             icon: const Icon(Icons.refresh),
             onPressed: () {
               _webViewController.reload();
             },
           ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'test_js',
                child: Text('测试JS通信'),
              ),
              const PopupMenuItem(
                value: 'inject_demo',
                child: Text('注入演示代码'),
              ),
              const PopupMenuItem(
                value: 'show_demo_html',
                child: Text('显示演示HTML'),
              ),
              const PopupMenuItem(
                value: 'simple_test',
                child: Text('简单测试页面'),
              ),
              const PopupMenuItem(
                value: 'simple_test_file',
                child: Text('简单测试(文件)'),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // 导航按钮栏
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              border: const Border(
                bottom: BorderSide(color: Colors.grey, width: 0.5),
              ),
            ),
            child: Row(
              children: [
                                 IconButton(
                   icon: const Icon(Icons.arrow_back),
                   onPressed: _canGoBack ? () {
                     _webViewController.goBack();
                   } : null,
                 ),
                 IconButton(
                   icon: const Icon(Icons.arrow_forward),
                   onPressed: _canGoForward ? () {
                     _webViewController.goForward();
                   } : null,
                 ),
                const Spacer(),
                TextButton(
                  onPressed: () => _handleMenuAction('test_js'),
                  child: const Text('测试JS'),
                ),
              ],
            ),
          ),
                     // WebView内容
           Expanded(
             child: _demoHtml != null
                 ? WebViewWrapper(
                     controller: _webViewController,
                     initialUrl: widget.url,
                     initialHtml: widget.html ?? _demoHtml!,
                     jsBridge: _jsBridge,
                     onPageFinished: () {
                       setState(() {
                         // 更新导航按钮状态
                       });
                     },
                   )
                 : const Center(
                     child: Column(
                       mainAxisAlignment: MainAxisAlignment.center,
                       children: [
                         CircularProgressIndicator(),
                         SizedBox(height: 16),
                         Text('正在加载HTML内容...'),
                       ],
                     ),
                   ),
           ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'test_js':
        _testJSCommunication();
        break;
      case 'inject_demo':
        _injectDemoCode();
        break;
      case 'show_demo_html':
        _showDemoHTML();
        break;
      case 'simple_test':
        _showSimpleTest();
        break;
      case 'simple_test_file':
        _showSimpleTestFromFile();
        break;
    }
  }

  void _testJSCommunication() {
    final message = {
      'type': 'greeting',
      'data': {
        'message': 'Hello from Flutter!',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      }
    };
    _sendMessageToWebView(message);
  }

  void _injectDemoCode() {
    // 注入一些演示用的JavaScript代码
    const script = '''
      const button = document.createElement('button');
      button.textContent = 'Send Message to Flutter';
      button.style.cssText = 'padding: 10px; margin: 10px; background: #007AFF; color: white; border: none; border-radius: 5px;';
      button.onclick = function() {
        if (window.FlutterJSBridge) {
          window.FlutterJSBridge.postMessage({
            type: 'userAction',
            method: 'buttonClick',
            data: {
              buttonText: 'Hello Flutter!',
              timestamp: Date.now()
            }
          });
        }
      };
      document.body.appendChild(button);
    ''';
    
         _webViewController.executeJavaScript(script);
  }

  void _showDemoHTML() async {
    final demoHtml = await HtmlLoader.getDemoHtml();
    if (mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => WebViewPage(
            title: 'Demo HTML',
            html: demoHtml,
          ),
        ),
      );
    }
  }

  void _showSimpleTest() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewPage(
          title: '简单测试',
          html: HtmlLoader.getSimpleTestHtml(),
        ),
      ),
    );
  }

  void _showSimpleTestFromFile() async {
    try {
      final simpleHtml = await HtmlLoader.getSimpleTestFromFile();
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => WebViewPage(
              title: '简单测试(文件)',
              html: simpleHtml,
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载测试文件失败: $e')),
        );
      }
    }
  }


} 