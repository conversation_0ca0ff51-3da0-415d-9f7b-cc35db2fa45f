import 'package:flutter/material.dart';
import 'webview_wrapper.dart';
import 'js_bridge.dart';

/// WebView弹窗组件
class WebViewDialog extends StatefulWidget {
  final String url;
  final String title;
  final bool showToolBar;
  final VoidCallback? onClose;
  final JSBridge? jsBridge;

  const WebViewDialog({
    super.key,
    required this.url,
    required this.title,
    this.showToolBar = false,
    this.onClose,
    this.jsBridge,
  });

  @override
  State<WebViewDialog> createState() => _WebViewDialogState();
}

class _WebViewDialogState extends State<WebViewDialog> {
  late WebViewWrapperController _webViewController;

  @override
  void initState() {
    super.initState();
    _webViewController = WebViewWrapperController();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Stack(
        children: [
          Positioned.fill(
            child: WebViewWrapper(
              controller: _webViewController,
              initialUrl: widget.url,
              jsBridge: widget.jsBridge ?? JSBridge(context, webViewController: _webViewController),
              backgroundColor: Colors.transparent,
              onPageFinished: () {},
            ),
          ),
          if (widget.showToolBar)
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                height: 49.0,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.5),
                  border: Border(top: BorderSide(color: Colors.grey[200]!, width: 0.5)),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.close),
                      iconSize: 14.0,
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(minWidth: 30.0, minHeight: 20.0),
                      onPressed: () {
                        if (widget.onClose != null) {
                          widget.onClose!();
                        }
                        Navigator.of(context).pop();
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.refresh),
                      iconSize: 14.0,
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(minWidth: 30.0, minHeight: 20.0),
                      onPressed: () {
                        _webViewController.reload();
                      },
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}