import 'dart:convert';
import 'dart:io';
import 'package:flutter/services.dart';

class AppConfig {
  static late String _appId;
  static late String _gid;
  static late String _pid;
  static late String _appKey;
  static late String _gwversion;
  static late String _sversion;
  static late String _refer;
  static late String _hostSdkVersion;

  static String get appId => _appId;
  static String get gid => _gid;
  static String get pid => _pid;
  static String get appKey => _appKey;
  static String get gwversion => _gwversion;
  static String get sversion => _sversion;
  static String get refer => _refer;
  static String get hostSdkVersion => _hostSdkVersion;

  static Future<void> initialize() async {
    try {
      String configFile = Platform.isAndroid ? 'app_config_android.json' : 'app_config_ios.json';
      final configString = await rootBundle.loadString('assets/config/$configFile');
      final configJson = json.decode(configString);
      
      _appId = configJson['appId'];
      _gid = configJson['gid'];
      _pid = configJson['pid'];
      _appKey = configJson['appKey'];
      _gwversion = configJson['gwversion'];
      _sversion = configJson['sversion'];
      _refer = configJson['refer'];
      _hostSdkVersion = configJson['host_sdk_version'];
    } catch (e) {
      throw Exception('Failed to load app config: $e');
    }
  }
}