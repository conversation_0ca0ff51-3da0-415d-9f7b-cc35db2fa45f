import 'package:dlyz_flutter/pay/pay_strategy_interface.dart';
import '../../manager/channel_manager.dart';
import '../pay_type.dart';

class AndroidWechatPayStrategy implements IPayStrategy {
  @override
  Future<Map<String, String>> pay(Map<String, dynamic> params) async {
    ///暂时和ios保持一致，后续迭代优化
    String tradeInfo = params['trade'] ?? '';
    await ChannelManager().openUrl(url: tradeInfo);
    return PayResult.buildReturnResult(PayResult.PAY_UNKNOWN, '未知');
  }
}
