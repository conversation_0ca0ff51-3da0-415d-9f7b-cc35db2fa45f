import '../../manager/channel_manager.dart';
import '../pay_strategy_interface.dart';
import '../pay_type.dart';

class IosWechatPayStrategy implements IPayStrategy {
  @override
  Future<Map<String, String>> pay(Map<String, dynamic> params) async {
    String tradeInfo = params['trade'] ?? '';
    await ChannelManager().openUrl(url: tradeInfo);
    return PayResult.buildReturnResult(PayResult.PAY_UNKNOWN, '未知');
  }

}
