import '../../manager/channel_manager.dart';
import '../pay_strategy_interface.dart';
import '../pay_type.dart';

class AndroidAlipayStrategy implements IPayStrategy {
  @override
  Future<Map<String, String>> pay(Map<String, dynamic> params) async {
    String orderId = params['uuid'] ?? '';
    String tradeInfo = params['trade'] ?? '';
    if (tradeInfo.isEmpty) {
      return PayResult.buildReturnResult(PayResult.PAY_FAILED, '支付信息不能为空');
    }
    PayResult payResult = await ChannelManager().androidPay(
      payType: PayType.alipay,
      orderId: orderId,
      tradeInfo: tradeInfo,
    );
    return PayResult.buildReturnResult(payResult.code, payResult.message);
  }
}
