import 'dart:io';
import 'package:dlyz_flutter/pay/pay_strategy_interface.dart';
import 'package:dlyz_flutter/pay/pay_type.dart';
import 'package:dlyz_flutter/pay/strategies/android_alipay_strategy.dart';
import 'package:dlyz_flutter/pay/strategies/android_wechat_pay_strategy.dart';
import 'package:dlyz_flutter/pay/strategies/ios_alipay_strategy.dart';
import 'package:dlyz_flutter/pay/strategies/ios_wechat_pay_strategy.dart';

class PayManager {
  static final PayManager _instance = PayManager._internal();

  factory PayManager() => _instance;

  PayManager._internal();

  static getInstance() {
    return _instance;
  }

  final Map<PayStrategyType, IPayStrategy> _payStrategyMap = {
    PayStrategyType.alipayAndroid: AndroidAlipayStrategy(),
    PayStrategyType.alipayIos: IosAlipayStrategy(),
    PayStrategyType.wxpayAndroid: AndroidWechatPayStrategy(),
    PayStrategyType.wxpayIos: IosWechatPayStrategy(),
  };

  Future<Map<String, String>> pay(Map<String, dynamic> params) async {
    String payType = params['payType'] ?? '';
    PayType? payTypeEnum;
    try {
      payTypeEnum = PayType.values.firstWhere((e) => e.name == payType);
    } catch (e) {
      payTypeEnum = null;
    }
    if (payTypeEnum == null) {
      return PayResult.buildReturnResult(PayResult.PAY_FAILED, '不支持的支付类型');
    }
    IPayStrategy? payStrategy = _getPayStrategy(payTypeEnum);
    if (payStrategy != null) {
      return await payStrategy.pay(params);
    }
    return PayResult.buildReturnResult(PayResult.PAY_FAILED, '不支持的支付类型');
  }

  ///根据类型获取对应支付策略
  IPayStrategy? _getPayStrategy(PayType payType) {
    if (Platform.isAndroid) {
      if (payType == PayType.alipay) {
        return _payStrategyMap[PayStrategyType.alipayAndroid];
      } else if (payType == PayType.wxpay) {
        return _payStrategyMap[PayStrategyType.wxpayAndroid];
      }
      return null;
    } else {
      if (payType == PayType.alipay) {
        return _payStrategyMap[PayStrategyType.alipayIos];
      } else if (payType == PayType.wxpay) {
        return _payStrategyMap[PayStrategyType.wxpayIos];
      }
      return null;
    }
  }
}
