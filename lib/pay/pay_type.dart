enum PayType { alipay, wxpay }

enum PayStrategyType { alipayAndroid, alipayIos, wxpayAndroid, wxpayIos }

class PayResult {

  static final String PAY_SUCCESS = 'PAY_SUCCESS';
  static final String PAY_FAILED = 'PAY_FAILED';
  static final String PAY_CANCELLED = 'PAY_CANCELLED';
  static final String PAY_UNKNOWN = 'PAY_UNKNOWN';

  final String code;
  final String message;

  PayResult({required this.code, required this.message});

  static Map<String, String> buildReturnResult(String code, String message) {
    return {'code': code, 'message': message};
  }
}
