/// 帖子评论列表数据模型
class PostsList {
  final List<Post> pageData;
  final int currentPage;
  final int perPage;
  final String firstPageUrl;
  final String? nextPageUrl;
  final String prePageUrl;
  final int pageLength;
  final int totalCount;
  final int totalPage;

  PostsList({
    required this.pageData,
    required this.currentPage,
    required this.perPage,
    required this.firstPageUrl,
    this.nextPageUrl,
    required this.prePageUrl,
    required this.pageLength,
    required this.totalCount,
    required this.totalPage,
  });

  factory PostsList.fromJson(Map<String, dynamic> json) {
    return PostsList(
      pageData: (json['pageData'] as List<dynamic>? ?? [])
          .map((item) => Post.fromJson(item))
          .toList(),
      currentPage: json['currentPage'] ?? 1,
      perPage: json['perPage'] ?? 20,
      firstPageUrl: json['firstPageUrl'] ?? '',
      nextPageUrl: json['nextPageUrl'],
      prePageUrl: json['prePageUrl'] ?? '',
      pageLength: json['pageLength'] ?? 20,
      totalCount: json['totalCount'] ?? 0,
      totalPage: json['totalPage'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pageData': pageData.map((post) => post.toJson()).toList(),
      'currentPage': currentPage,
      'perPage': perPage,
      'firstPageUrl': firstPageUrl,
      'nextPageUrl': nextPageUrl,
      'prePageUrl': prePageUrl,
      'pageLength': pageLength,
      'totalCount': totalCount,
      'totalPage': totalPage,
    };
  }
}

/// 帖子评论数据模型
class Post {
  final int id;
  final int userId;
  final int threadId;
  final int? replyPostId;
  final int? replyUserId;
  final int? commentPostId;
  final int? commentUserId;
  final int replyCount;
  final int likeCount;
  final String createdAt;
  final String updatedAt;
  final bool isFirst;
  final bool isComment;
  final int isApproved;
  final bool canApprove;
  final bool canDelete;
  final bool canHide;
  final bool canLike;
  final PostUser user;
  final String location;
  final List<PostImage> images;
  final dynamic likeState;
  final String summaryText;
  final int isTop;
  final String content;
  final bool isDeleted;
  final double redPacketAmount;
  final bool isLiked;
  final List<Post> lastThreeComments;
  final PostUser? replyUser;
  final PostUser? commentUser;

  Post({
    required this.id,
    required this.userId,
    required this.threadId,
    this.replyPostId,
    this.replyUserId,
    this.commentPostId,
    this.commentUserId,
    required this.replyCount,
    required this.likeCount,
    required this.createdAt,
    required this.updatedAt,
    required this.isFirst,
    required this.isComment,
    required this.isApproved,
    required this.canApprove,
    required this.canDelete,
    required this.canHide,
    required this.canLike,
    required this.user,
    required this.location,
    required this.images,
    this.likeState,
    required this.summaryText,
    required this.isTop,
    required this.content,
    required this.isDeleted,
    required this.redPacketAmount,
    required this.isLiked,
    required this.lastThreeComments,
    this.replyUser,
    this.commentUser,
  });

  factory Post.fromJson(Map<String, dynamic> json) {
    return Post(
      id: json['id'] ?? 0,
      userId: json['userId'] ?? 0,
      threadId: json['threadId'] ?? 0,
      replyPostId: json['replyPostId'],
      replyUserId: json['replyUserId'],
      commentPostId: json['commentPostId'],
      commentUserId: json['commentUserId'],
      replyCount: json['replyCount'] ?? 0,
      likeCount: json['likeCount'] ?? 0,
      createdAt: json['createdAt'] ?? '',
      updatedAt: json['updatedAt'] ?? '',
      isFirst: _parseBool(json['isFirst']),
      isComment: _parseBool(json['isComment']),
      isApproved: json['isApproved'] ?? 0,
      canApprove: _parseBool(json['canApprove']),
      canDelete: _parseBool(json['canDelete']),
      canHide: _parseBool(json['canHide']),
      canLike: _parseBool(json['canLike']),
      user: PostUser.fromJson(json['user'] ?? {}),
      location: json['location'] ?? '',
      images: (json['images'] as List<dynamic>? ?? [])
          .map((item) => PostImage.fromJson(item))
          .toList(),
      likeState: json['likeState'],
      summaryText: json['summaryText'] ?? '',
      isTop: json['isTop'] ?? 0,
      content: json['content'] ?? '',
      isDeleted: _parseBool(json['isDeleted']),
      redPacketAmount: (json['redPacketAmount'] ?? 0).toDouble(),
      isLiked: _parseBool(json['isLiked']),
      lastThreeComments: (json['lastThreeComments'] as List<dynamic>? ?? [])
          .map((item) => Post.fromJson(item))
          .toList(),
      replyUser: json['replyUser'] != null ? PostUser.fromJson(json['replyUser']) : null,
      commentUser: json['commentUser'] != null ? PostUser.fromJson(json['commentUser']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'threadId': threadId,
      'replyPostId': replyPostId,
      'replyUserId': replyUserId,
      'commentPostId': commentPostId,
      'commentUserId': commentUserId,
      'replyCount': replyCount,
      'likeCount': likeCount,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isFirst': isFirst,
      'isComment': isComment,
      'isApproved': isApproved,
      'canApprove': canApprove,
      'canDelete': canDelete,
      'canHide': canHide,
      'canLike': canLike,
      'user': user.toJson(),
      'location': location,
      'images': images.map((image) => image.toJson()).toList(),
      'likeState': likeState,
      'summaryText': summaryText,
      'isTop': isTop,
      'content': content,
      'isDeleted': isDeleted,
      'redPacketAmount': redPacketAmount,
      'isLiked': isLiked,
      'lastThreeComments': lastThreeComments.map((comment) => comment.toJson()).toList(),
      'replyUser': replyUser?.toJson(),
      'commentUser': commentUser?.toJson(),
    };
  }
}

/// 帖子用户数据模型
class PostUser {
  final int id;
  final String nickname;
  final String avatar;
  final String realname;
  final String? badge;
  final String? label;
  final String? color;
  final dynamic medal;
  final PostUserGroups? groups;
  final bool isReal;

  PostUser({
    required this.id,
    required this.nickname,
    required this.avatar,
    required this.realname,
    this.badge,
    this.label,
    this.color,
    this.medal,
    this.groups,
    required this.isReal,
  });

  factory PostUser.fromJson(Map<String, dynamic> json) {
    return PostUser(
      id: json['id'] ?? 0,
      nickname: json['nickname'] ?? '',
      avatar: json['avatar'] ?? '',
      realname: json['realname'] ?? '',
      badge: json['badge'],
      label: json['label'],
      color: json['color'],
      medal: json['medal'],
      groups: json['groups'] != null ? PostUserGroups.fromJson(json['groups']) : null,
      isReal: _parseBool(json['isReal']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nickname': nickname,
      'avatar': avatar,
      'realname': realname,
      'badge': badge,
      'label': label,
      'color': color,
      'medal': medal,
      'groups': groups?.toJson(),
      'isReal': isReal,
    };
  }
}

/// 用户组数据模型
class PostUserGroups {
  final int id;
  final String name;
  final bool isDisplay;
  final int level;

  PostUserGroups({
    required this.id,
    required this.name,
    required this.isDisplay,
    required this.level,
  });

  factory PostUserGroups.fromJson(Map<String, dynamic> json) {
    return PostUserGroups(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      isDisplay: _parseBool(json['isDisplay']),
      level: json['level'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'isDisplay': isDisplay,
      'level': level,
    };
  }
}

/// 帖子图片数据模型
class PostImage {
  final int id;
  final int order;
  final int type;
  final int typeId;
  final bool isRemote;
  final int isApproved;
  final String url;
  final String displayUrl;
  final String attachment;
  final String extension;
  final String fileName;
  final String filePath;
  final int fileSize;
  final String fileType;
  final int fileWidth;
  final int fileHeight;
  final String fileHash;
  final String thumbUrl;

  PostImage({
    required this.id,
    required this.order,
    required this.type,
    required this.typeId,
    required this.isRemote,
    required this.isApproved,
    required this.url,
    required this.displayUrl,
    required this.attachment,
    required this.extension,
    required this.fileName,
    required this.filePath,
    required this.fileSize,
    required this.fileType,
    required this.fileWidth,
    required this.fileHeight,
    required this.fileHash,
    required this.thumbUrl,
  });

  factory PostImage.fromJson(Map<String, dynamic> json) {
    return PostImage(
      id: json['id'] ?? 0,
      order: json['order'] ?? 0,
      type: json['type'] ?? 0,
      typeId: json['type_id'] ?? 0,
      isRemote: _parseBool(json['isRemote']),
      isApproved: json['isApproved'] ?? 0,
      url: json['url'] ?? '',
      displayUrl: json['display_url'] ?? '',
      attachment: json['attachment'] ?? '',
      extension: json['extension'] ?? '',
      fileName: json['fileName'] ?? '',
      filePath: json['filePath'] ?? '',
      fileSize: json['fileSize'] ?? 0,
      fileType: json['fileType'] ?? '',
      fileWidth: json['fileWidth'] ?? 0,
      fileHeight: json['fileHeight'] ?? 0,
      fileHash: json['fileHash'] ?? '',
      thumbUrl: json['thumbUrl'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order': order,
      'type': type,
      'type_id': typeId,
      'isRemote': isRemote,
      'isApproved': isApproved,
      'url': url,
      'display_url': displayUrl,
      'attachment': attachment,
      'extension': extension,
      'fileName': fileName,
      'filePath': filePath,
      'fileSize': fileSize,
      'fileType': fileType,
      'fileWidth': fileWidth,
      'fileHeight': fileHeight,
      'fileHash': fileHash,
      'thumbUrl': thumbUrl,
    };
  }
}

/// 辅助方法：安全地将动态类型转换为布尔值
bool _parseBool(dynamic value) {
  if (value == null) return false;
  if (value is bool) return value;
  if (value is int) return value != 0;
  if (value is String) {
    return value.toLowerCase() == 'true' || value == '1';
  }
  return false;
}