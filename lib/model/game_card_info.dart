
import 'package:dlyz_flutter/model/download_info.dart';
import 'package:dlyz_flutter/model/game_package_info.dart';

class GameCardInfo {
  final String title;
  final String type;
  final String subtitle;
  final String iconUrl;
  final String imageUrl;
  final String videoUrl;
  final String packageName;
  final String tag;
  final String officialWeb;
  final String miniProgram;
  final List<GamePackageInfo> channelPackages;
  final DownloadInfo downloadInfo;

  GameCardInfo({
    required this.title,
    required this.type,
    required this.subtitle,
    required this.iconUrl,
    required this.imageUrl,
    required this.videoUrl,
    required this.packageName,
    required this.tag,
    required this.officialWeb,
    required this.miniProgram,
    required this.channelPackages,
    required this.downloadInfo
  });
}