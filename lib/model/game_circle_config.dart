class GameCircleConfig {
  final String circleType;
  final String circleIcon;
  final String circleTopBanner;
  final String characterIcon;
  final String displayName;

  GameCircleConfig({
    required this.circleType,
    required this.circleIcon,
    required this.circleTopBanner,
    required this.characterIcon,
    required this.displayName,
  });

  factory GameCircleConfig.fromJson(Map<String, dynamic> json) {
    return GameCircleConfig(
      circleType: json['circle_type']?.toString() ?? '',
      circleIcon: json['circle_icon']?.toString() ?? '',
      circleTopBanner: json['circle_top_banner']?.toString() ?? '',
      characterIcon: json['character_icon']?.toString() ?? '',
      displayName: json['display_name']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'circle_type': circleType,
      'circle_icon': circleIcon,
      'circle_top_banner': circleTopBanner,
      'character_icon': characterIcon,
      'display_name': displayName,
    };
  }

  @override
  String toString() {
    return 'GameCircleConfig{circleType: $circleType, circleIcon: $circleIcon, circleTopBanner: $circleTopBanner, characterIcon: $characterIcon, displayName: $displayName}';
  }
}