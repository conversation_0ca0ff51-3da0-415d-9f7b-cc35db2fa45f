// 关联账号列表数据模型

/// 子用户信息项
class SubUserItem {
  /// 用户ID
  final String uid;
  
  /// 用户名
  final String uname;
  
  /// 是否当前登录的账号
  final bool isCurrent;

  const SubUserItem({
    required this.uid,
    required this.uname,
    required this.isCurrent,
  });

  factory SubUserItem.fromJson(Map<String, dynamic> json) {
    return SubUserItem(
      uid: json['uid']?.toString() ?? '',
      uname: json['uname']?.toString() ?? '',
      isCurrent: json['is_current'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'uname': uname,
      'is_current': isCurrent,
    };
  }
}

/// 子用户列表数据内容
class SubUserListData {
  /// 子用户列表
  final List<SubUserItem> list;

  const SubUserListData({
    required this.list,
  });

  factory SubUserListData.fromJson(Map<String, dynamic> json) {
    return SubUserListData(
      list: (json['list'] as List<dynamic>?)
          ?.map((item) {
            if (item is Map<String, dynamic>) {
              return SubUserItem.fromJson(item);
            } else if (item is Map) {
              return SubUserItem.fromJson(Map<String, dynamic>.from(item));
            } else {
              return const SubUserItem(
                uid: '',
                uname: '',
                isCurrent: false,
              );
            }
          })
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'list': list.map((item) => item.toJson()).toList(),
    };
  }
}

/// 子用户列表响应数据模型
class SubUserListResponse {
  /// 响应状态码
  final int state;
  
  /// 响应消息
  final String msg;
  
  /// 数据对象
  final SubUserListData? data;

  const SubUserListResponse({
    required this.state,
    required this.msg,
    this.data,
  });

  factory SubUserListResponse.fromJson(Map<String, dynamic> json) {
    return SubUserListResponse(
      state: json['state'] as int? ?? 0,
      msg: json['msg']?.toString() ?? '',
      data: json['data'] != null 
          ? SubUserListData.fromJson(json['data'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'state': state,
      'msg': msg,
      'data': data?.toJson(),
    };
  }
}