/// S层激活接口响应数据模型
/// 只处理API响应中data字段的内容
class SLayerActivateResponse {
  /// 用户相关URL配置
  final UserUrlConfig? u;
  
  /// 客户端配置
  final ClientConfig? c;
  
  /// 其他数据配置
  final Map<String, dynamic>? d;
  
  /// 扩展配置
  final ExtensionConfig? e;
  
  /// 工具配置
  final ToolConfig? t;
  
  /// 支付配置
  final PaymentConfig? p;

  SLayerActivateResponse({
    this.u,
    this.c,
    this.d,
    this.e,
    this.t,
    this.p,
  });

  factory SLayerActivateResponse.fromJson(Map<String, dynamic> json) {
    return SLayerActivateResponse(
      u: json['u'] != null
          ? UserUrlConfig.fromJson(json['u'] as Map<String, dynamic>)
          : null,
      c: json['c'] != null
          ? ClientConfig.fromJson(json['c'] as Map<String, dynamic>)
          : null,
      d: json['d'] as Map<String, dynamic>?,
      e: json['e'] != null
          ? ExtensionConfig.fromJson(json['e'] as Map<String, dynamic>)
          : null,
      t: json['t'] != null
          ? ToolConfig.fromJson(json['t'] as Map<String, dynamic>)
          : null,
      p: json['p'] != null
          ? PaymentConfig.fromJson(json['p'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'u': u?.toJson(),
      'c': c?.toJson(),
      'd': d,
      'e': e?.toJson(),
      't': t?.toJson(),
      'p': p?.toJson(),
    };
  }

  @override
  String toString() {
    return 'SLayerActivateResponse{u: $u, c: $c, d: $d, e: $e, t: $t, p: $p}';
  }
}

/// 用户URL配置
class UserUrlConfig {
  final String? pay;
  final String? kf;
  final String? bbs;
  final String? login;
  final String? reg;
  final String? autoassign;
  final String? mreg;
  final String? mscode;
  final String? mregRes;
  final String? sUserProtocol;
  final String? shop;
  final String? uagree;
  final UAgreeConfig? uagreeConfig;
  final String? resetPwd;
  final String? sFloatWindow;
  final String? sShowRedFloatWindow;
  final String? uFetchUInfo;
  final String? quickLogin;
  final String? cfgShanYan;
  final String? mobileShanYanLogin;
  final String? mobileSendScode;
  final String? mobileLoginScode;
  final String? mobileLoginPwd;
  final String? sFloatWindowNew;
  final String? validateInitFaceVerify;
  final String? validateCheckFaceVerify;
  final String? validateDescribeFaceVerify;

  UserUrlConfig({
    this.pay,
    this.kf,
    this.bbs,
    this.login,
    this.reg,
    this.autoassign,
    this.mreg,
    this.mscode,
    this.mregRes,
    this.sUserProtocol,
    this.shop,
    this.uagree,
    this.uagreeConfig,
    this.resetPwd,
    this.sFloatWindow,
    this.sShowRedFloatWindow,
    this.uFetchUInfo,
    this.quickLogin,
    this.cfgShanYan,
    this.mobileShanYanLogin,
    this.mobileSendScode,
    this.mobileLoginScode,
    this.mobileLoginPwd,
    this.sFloatWindowNew,
    this.validateInitFaceVerify,
    this.validateCheckFaceVerify,
    this.validateDescribeFaceVerify,
  });

  factory UserUrlConfig.fromJson(Map<String, dynamic> json) {
    return UserUrlConfig(
      pay: json['pay'],
      kf: json['kf'],
      bbs: json['bbs'],
      login: json['login'],
      reg: json['reg'],
      autoassign: json['autoassign'],
      mreg: json['mreg'],
      mscode: json['mscode'],
      mregRes: json['mreg_res'],
      sUserProtocol: json['s_user_protocol'],
      shop: json['shop'],
      uagree: json['uagree'],
      uagreeConfig: json['uagree_config'] != null
          ? UAgreeConfig.fromJson(json['uagree_config'] as Map<String, dynamic>)
          : null,
      resetPwd: json['resetPwd'],
      sFloatWindow: json['s_float_window'],
      sShowRedFloatWindow: json['s_show_red_float_window'],
      uFetchUInfo: json['u_fetch_u_info'],
      quickLogin: json['quick_login'],
      cfgShanYan: json['cfg_shan_yan'],
      mobileShanYanLogin: json['mobile_shan_yan_login'],
      mobileSendScode: json['mobile_send_scode'],
      mobileLoginScode: json['mobile_login_scode'],
      mobileLoginPwd: json['mobile_login_pwd'],
      sFloatWindowNew: json['s_float_window_new'],
      validateInitFaceVerify: json['validate_init_face_verify'],
      validateCheckFaceVerify: json['validate_check_face_verify'],
      validateDescribeFaceVerify: json['validate_describe_face_verify'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pay': pay,
      'kf': kf,
      'bbs': bbs,
      'login': login,
      'reg': reg,
      'autoassign': autoassign,
      'mreg': mreg,
      'mscode': mscode,
      'mreg_res': mregRes,
      's_user_protocol': sUserProtocol,
      'shop': shop,
      'uagree': uagree,
      'uagree_config': uagreeConfig?.toJson(),
      'resetPwd': resetPwd,
      's_float_window': sFloatWindow,
      's_show_red_float_window': sShowRedFloatWindow,
      'u_fetch_u_info': uFetchUInfo,
      'quick_login': quickLogin,
      'cfg_shan_yan': cfgShanYan,
      'mobile_shan_yan_login': mobileShanYanLogin,
      'mobile_send_scode': mobileSendScode,
      'mobile_login_scode': mobileLoginScode,
      'mobile_login_pwd': mobileLoginPwd,
      's_float_window_new': sFloatWindowNew,
      'validate_init_face_verify': validateInitFaceVerify,
      'validate_check_face_verify': validateCheckFaceVerify,
      'validate_describe_face_verify': validateDescribeFaceVerify,
    };
  }

  @override
  String toString() {
    return 'UserUrlConfig{pay: $pay, login: $login, reg: $reg, ...}';
  }
}

/// 用户协议配置
class UAgreeConfig {
  final int? type;
  final int? version;

  UAgreeConfig({this.type, this.version});

  factory UAgreeConfig.fromJson(Map<String, dynamic> json) {
    return UAgreeConfig(
      type: json['type'],
      version: json['version'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'version': version,
    };
  }

  @override
  String toString() {
    return 'UAgreeConfig{type: $type, version: $version}';
  }
}

/// 客户端配置
class ClientConfig {
  final GameGuideConfig? gg;
  final DownloadConfig? dl;
  final String? stip;
  final String? bptip;
  final int? pon;
  final int? pit;
  final String? gn;
  final String? smst;
  final int? aid;
  final String? brand;
  final OAuthConfig? oauth;
  final TutorialConfig? tt;
  final VersionConfig? version;
  final ContentConfig? content;

  ClientConfig({
    this.gg,
    this.dl,
    this.stip,
    this.bptip,
    this.pon,
    this.pit,
    this.gn,
    this.smst,
    this.aid,
    this.brand,
    this.oauth,
    this.tt,
    this.version,
    this.content,
  });

  factory ClientConfig.fromJson(Map<String, dynamic> json) {
    return ClientConfig(
      gg: json['gg'] != null
          ? GameGuideConfig.fromJson(json['gg'] as Map<String, dynamic>)
          : null,
      dl: json['dl'] != null
          ? DownloadConfig.fromJson(json['dl'] as Map<String, dynamic>)
          : null,
      stip: json['stip'],
      bptip: json['bptip'],
      pon: json['pon'],
      pit: json['pit'],
      gn: json['gn'],
      smst: json['smst'],
      aid: json['aid'],
      brand: json['brand'],
      oauth: json['oauth'] != null
          ? OAuthConfig.fromJson(json['oauth'] as Map<String, dynamic>)
          : null,
      tt: json['tt'] != null
          ? TutorialConfig.fromJson(json['tt'] as Map<String, dynamic>)
          : null,
      version: json['version'] != null
          ? VersionConfig.fromJson(json['version'] as Map<String, dynamic>)
          : null,
      content: json['content'] != null
          ? ContentConfig.fromJson(json['content'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'gg': gg?.toJson(),
      'dl': dl?.toJson(),
      'stip': stip,
      'bptip': bptip,
      'pon': pon,
      'pit': pit,
      'gn': gn,
      'smst': smst,
      'aid': aid,
      'brand': brand,
      'oauth': oauth?.toJson(),
      'tt': tt?.toJson(),
      'version': version?.toJson(),
      'content': content?.toJson(),
    };
  }

  @override
  String toString() {
    return 'ClientConfig{gn: $gn, brand: $brand, aid: $aid, ...}';
  }
}

/// 游戏指引配置
class GameGuideConfig {
  final String? n;
  final String? p;
  final String? u;

  GameGuideConfig({this.n, this.p, this.u});

  factory GameGuideConfig.fromJson(Map<String, dynamic> json) {
    return GameGuideConfig(
      n: json['n'],
      p: json['p'],
      u: json['u'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'n': n, 'p': p, 'u': u};
  }
}

/// 下载配置
class DownloadConfig {
  final String? n;
  final String? dpgn;
  final String? u;

  DownloadConfig({this.n, this.dpgn, this.u});

  factory DownloadConfig.fromJson(Map<String, dynamic> json) {
    return DownloadConfig(
      n: json['n'],
      dpgn: json['dpgn'],
      u: json['u'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'n': n, 'dpgn': dpgn, 'u': u};
  }
}

/// OAuth配置
class OAuthConfig {
  final int? qq;
  final int? weixin;
  final int? weibo;
  final int? apple;

  OAuthConfig({this.qq, this.weixin, this.weibo, this.apple});

  factory OAuthConfig.fromJson(Map<String, dynamic> json) {
    return OAuthConfig(
      qq: json['qq'],
      weixin: json['weixin'],
      weibo: json['weibo'],
      apple: json['apple'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'qq': qq,
      'weixin': weixin,
      'weibo': weibo,
      'apple': apple,
    };
  }
}

/// 教程配置
class TutorialConfig {
  final String? img;
  final String? u;
  final String? dpgn;

  TutorialConfig({this.img, this.u, this.dpgn});

  factory TutorialConfig.fromJson(Map<String, dynamic> json) {
    return TutorialConfig(
      img: json['img'],
      u: json['u'],
      dpgn: json['dpgn'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'img': img, 'u': u, 'dpgn': dpgn};
  }
}

/// 版本配置
class VersionConfig {
  final String? ui;
  final String? fregister;

  VersionConfig({this.ui, this.fregister});

  factory VersionConfig.fromJson(Map<String, dynamic> json) {
    return VersionConfig(
      ui: json['ui'],
      fregister: json['fregister'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'ui': ui, 'fregister': fregister};
  }
}

/// 内容配置
class ContentConfig {
  final String? totastKf;
  final String? totastGzh;

  ContentConfig({this.totastKf, this.totastGzh});

  factory ContentConfig.fromJson(Map<String, dynamic> json) {
    return ContentConfig(
      totastKf: json['totastKf'],
      totastGzh: json['totastGzh'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'totastKf': totastKf, 'totastGzh': totastGzh};
  }
}

/// 扩展配置
class ExtensionConfig {
  final String? flashScreenImg;

  ExtensionConfig({this.flashScreenImg});

  factory ExtensionConfig.fromJson(Map<String, dynamic> json) {
    return ExtensionConfig(
      flashScreenImg: json['flash_screen_img'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'flash_screen_img': flashScreenImg};
  }
}

/// 工具配置
class ToolConfig {
  final String? connkf;

  ToolConfig({this.connkf});

  factory ToolConfig.fromJson(Map<String, dynamic> json) {
    return ToolConfig(
      connkf: json['connkf'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'connkf': connkf};
  }
}

/// 支付配置
class PaymentConfig {
  final String? availablePways;
  final String? walletBalance;
  final String? getCoupon;
  final String? walletPay;
  final String? sPay;
  final String? orderStatus;

  PaymentConfig({
    this.availablePways,
    this.walletBalance,
    this.getCoupon,
    this.walletPay,
    this.sPay,
    this.orderStatus,
  });

  factory PaymentConfig.fromJson(Map<String, dynamic> json) {
    return PaymentConfig(
      availablePways: json['available_pways'],
      walletBalance: json['wallet_balance'],
      getCoupon: json['get_coupon'],
      walletPay: json['wallet_pay'],
      sPay: json['s_pay'],
      orderStatus: json['order_status'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'available_pways': availablePways,
      'wallet_balance': walletBalance,
      'get_coupon': getCoupon,
      'wallet_pay': walletPay,
      's_pay': sPay,
      'order_status': orderStatus,
    };
  }

  @override
  String toString() {
    return 'PaymentConfig{sPay: $sPay, walletPay: $walletPay, ...}';
  }
}