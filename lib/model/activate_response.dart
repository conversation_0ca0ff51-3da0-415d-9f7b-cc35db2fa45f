/// 激活接口响应模型
class ActivateResponse {
  final String? message;
  final bool success;
  final Map<String, dynamic>? data;

  ActivateResponse({
    this.message,
    required this.success,
    this.data,
  });

  factory ActivateResponse.fromJson(Map<String, dynamic> json) {
    return ActivateResponse(
      message: json['message'] as String?,
      success: json['success'] ?? false,
      data: json['data'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'success': success,
      'data': data,
    };
  }

  @override
  String toString() {
    return 'ActivateResponse{message: $message, success: $success, data: $data}';
  }
}