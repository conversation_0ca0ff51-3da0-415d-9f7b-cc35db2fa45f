/// Tgid信息
class TgidInfo {
  final String label;
  final int value;

  TgidInfo({
    required this.label,
    required this.value,
  });

  factory TgidInfo.fromJson(Map<String, dynamic> json) {
    return TgidInfo(
      label: json['label'] ?? '',
      value: json['value'] is String ? int.tryParse(json['value']) ?? 0 : (json['value'] ?? 0),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'label': label,
      'value': value,
    };
  }
}