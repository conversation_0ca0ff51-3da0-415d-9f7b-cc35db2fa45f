/// 附件设置
class SetAttach {
  final String supportImgExt;
  final String supportFileExt;
  final String supportMaxSize;
  final String supportMaxDownloadNum;
  final String supportMaxUploadAttachmentNum;
  final String maxUploadAttachmentNum;

  SetAttach({
    required this.supportImgExt,
    required this.supportFileExt,
    required this.supportMaxSize,
    required this.supportMaxDownloadNum,
    required this.supportMaxUploadAttachmentNum,
    required this.maxUploadAttachmentNum,
  });

  factory SetAttach.fromJson(Map<String, dynamic> json) {
    return SetAttach(
      supportImgExt: json['supportImgExt'] ?? '',
      supportFileExt: json['supportFileExt'] ?? '',
      supportMaxSize: json['supportMaxSize'] ?? '',
      supportMaxDownloadNum: json['supportMaxDownloadNum'] ?? '',
      supportMaxUploadAttachmentNum: json['supportMaxUploadAttachmentNum'] ?? '',
      maxUploadAttachmentNum: json['maxUploadAttachmentNum'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'supportImgExt': supportImgExt,
      'supportFileExt': supportFileExt,
      'supportMaxSize': supportMaxSize,
      'supportMaxDownloadNum': supportMaxDownloadNum,
      'supportMaxUploadAttachmentNum': supportMaxUploadAttachmentNum,
      'maxUploadAttachmentNum': maxUploadAttachmentNum,
    };
  }
}