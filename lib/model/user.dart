import 'login_info.dart';
import 'user_detail.dart';

/// 完整的用户对象，包含登录信息和用户详细信息
class User {
  /// 登录相关信息（登录态、票据等数据）
  final LoginInfo loginInfo;
  
  /// 用户详细信息（展示相关，主要用于展示）
  final UserDetail userDetail;

  const User({
    required this.loginInfo,
    required this.userDetail,
  });

  /// 获取用户ID（muid）
  String? get muid => loginInfo.muid;
  
  /// 获取显示名称
  String get alias => userDetail.displayName;

  /// 获取登录类型
  String get loginType => loginInfo.loginType;

  /// 获取票据
  String get ticket => loginInfo.ticket;
  
  /// 获取刷新票据
  String get refreshTicket => loginInfo.refreshTicket;

  /// 从登录结果创建用户对象
  factory User.fromLoginInfo(
    LoginInfo loginInfo, {
    String? mobile,
    DateTime? createdAt,
  }) {
    final userDetail = UserDetail(
      muid: loginInfo.muid,
      alias: loginInfo.muname,
      avatar: null,
      vipLevelNum: 0,
      area: null,
      mibaoPhone: null,
      loginPhone: mobile,
    );
    
    return User(
      loginInfo: loginInfo,
      userDetail: userDetail,
    );
  }

  /// 从JSON创建
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      loginInfo: LoginInfo.fromJson(json['loginInfo']),
      userDetail: UserDetail.fromJson(json['userDetail']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'loginInfo': loginInfo.toJson(),
      'userDetail': userDetail.toJson(),
    };
  }

  User copyWith({
    LoginInfo? loginInfo,
    UserDetail? userDetail,
  }) {
    return User(
      loginInfo: loginInfo ?? this.loginInfo,
      userDetail: userDetail ?? this.userDetail,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.muid == muid;
  }

  @override
  int get hashCode => muid.hashCode;

  @override
  String toString() {
    return 'User(muid: $muid, displayName: $alias, loginType: $loginType)';
  }
}