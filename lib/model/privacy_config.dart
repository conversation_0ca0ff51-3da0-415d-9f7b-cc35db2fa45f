class PrivacyConfig {
  final String urlInterim;
  final String urlProtocol;
  final String urlPolicy;
  final String urlSubscription;
  final UAgreeConfig uagreeConfig;

  PrivacyConfig({
    required this.urlInterim,
    required this.urlProtocol,
    required this.urlPolicy,
    required this.urlSubscription,
    required this.uagreeConfig,
  });

  factory PrivacyConfig.fromJson(Map<String, dynamic> json) {
    return PrivacyConfig(
      urlInterim: (json['url_interim'] ?? '').toString(),
      urlProtocol: (json['url_protocol'] ?? '').toString(),
      urlPolicy: (json['url_policy'] ?? '').toString(),
      urlSubscription: (json['url_subscription'] ?? '').toString(),
      uagreeConfig: UAgreeConfig.fromJson(Map<String, dynamic>.from(json['uagree_config'] ?? {})),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'url_interim': urlInterim,
      'url_protocol': urlProtocol,
      'url_policy': urlPolicy,
      'url_subscription': urlSubscription,
      'uagree_config': uagreeConfig.toJson(),
    };
  }
}

class UAgreeConfig {
  final int type;
  final int version;

  UAgreeConfig({required this.type, required this.version});

  factory UAgreeConfig.fromJson(Map<String, dynamic> json) {
    return UAgreeConfig(
      type: _toInt(json['type']),
      version: _toInt(json['version']),
    );
  }

  Map<String, dynamic> toJson() => {
        'type': type,
        'version': version,
      };
}

int _toInt(dynamic v) {
  if (v is int) return v;
  if (v is String) {
    return int.tryParse(v) ?? 0;
  }
  return 0;
}


