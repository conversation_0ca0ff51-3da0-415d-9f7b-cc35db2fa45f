import 'forum_post_list.dart';

/// 话题列表数据模型
class TopicList {
  final List<TopicData> pageData;
  final int currentPage;
  final int perPage;
  final String firstPageUrl;
  final String? nextPageUrl;
  final String prePageUrl;
  final int pageLength;
  final int totalCount;
  final int totalPage;

  TopicList({
    required this.pageData,
    required this.currentPage,
    required this.perPage,
    required this.firstPageUrl,
    this.nextPageUrl,
    required this.prePageUrl,
    required this.pageLength,
    required this.totalCount,
    required this.totalPage,
  });

  factory TopicList.fromJson(Map<String, dynamic> json) {
    return TopicList(
      pageData: (json['pageData'] as List<dynamic>? ?? [])
          .map((item) => TopicData.fromJson(item as Map<String, dynamic>))
          .toList(),
      currentPage: json['currentPage'] ?? 1,
      perPage: json['perPage'] ?? 10,
      firstPageUrl: json['firstPageUrl'] ?? '',
      nextPageUrl: json['nextPageUrl'],
      prePageUrl: json['prePageUrl'] ?? '',
      pageLength: json['pageLength'] ?? 0,
      totalCount: json['totalCount'] ?? 0,
      totalPage: json['totalPage'] ?? 1,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pageData': pageData.map((item) => item.toJson()).toList(),
      'currentPage': currentPage,
      'perPage': perPage,
      'firstPageUrl': firstPageUrl,
      'nextPageUrl': nextPageUrl,
      'prePageUrl': prePageUrl,
      'pageLength': pageLength,
      'totalCount': totalCount,
      'totalPage': totalPage,
    };
  }
}

/// 单个话题数据模型
class TopicData {
  final int topicId;
  final int userId;
  final String nickname;
  final String content;
  final String desc;
  final int showTopicData;
  final TopicStats? topicData;
  final String icon;
  final String background;
  final String banner;
  final String bannerTitle;
  final String bannerJumpLink;
  final bool recommended;
  final String recommendedAt;
  final List<ForumPost> threads;

  TopicData({
    required this.topicId,
    required this.userId,
    required this.nickname,
    required this.content,
    required this.desc,
    required this.showTopicData,
    this.topicData,
    required this.icon,
    required this.background,
    required this.banner,
    required this.bannerTitle,
    required this.bannerJumpLink,
    required this.recommended,
    required this.recommendedAt,
    required this.threads,
  });

  factory TopicData.fromJson(Map<String, dynamic> json) {
    return TopicData(
      topicId: json['topicId'] ?? 0,
      userId: json['userId'] ?? 0,
      nickname: json['nickname'] ?? '',
      content: json['content'] ?? '',
      desc: json['desc'] ?? '',
      showTopicData: json['showTopicData'] ?? 0,
      topicData: json['topicData'] != null && json['topicData'] is Map<String, dynamic>
          ? TopicStats.fromJson(json['topicData'] as Map<String, dynamic>)
          : null,
      icon: json['icon'] ?? '',
      background: json['background'] ?? '',
      banner: json['banner'] ?? '',
      bannerTitle: json['bannerTitle'] ?? '',
      bannerJumpLink: json['bannerJumpLink'] ?? '',
      recommended: json['recommended'] ?? false,
      recommendedAt: json['recommendedAt'] ?? '',
      threads: (json['threads'] as List<dynamic>? ?? [])
          .map((item) => ForumPost.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'topicId': topicId,
      'userId': userId,
      'nickname': nickname,
      'content': content,
      'desc': desc,
      'showTopicData': showTopicData,
      'topicData': topicData?.toJson(),
      'icon': icon,
      'background': background,
      'banner': banner,
      'bannerTitle': bannerTitle,
      'bannerJumpLink': bannerJumpLink,
      'recommended': recommended,
      'recommendedAt': recommendedAt,
      'threads': threads.map((item) => item.toJson()).toList(),
    };
  }
}

/// 话题统计数据模型
class TopicStats {
  final int threadCount;
  final String viewCount;
  final int likeCount;

  TopicStats({
    required this.threadCount,
    required this.viewCount,
    required this.likeCount,
  });

  factory TopicStats.fromJson(Map<String, dynamic> json) {
    return TopicStats(
      threadCount: json['threadCount'] ?? 0,
      viewCount: json['viewCount']?.toString() ?? '0',
      likeCount: json['likeCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'threadCount': threadCount,
      'viewCount': viewCount,
      'likeCount': likeCount,
    };
  }
}
