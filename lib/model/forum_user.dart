import 'user_group.dart';

/// 用户信息
class User {
  final List<UserGroup> groups;
  final String registerTime;
  final int userId;

  User({
    required this.groups,
    required this.registerTime,
    required this.userId,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      groups: (json['groups'] as List<dynamic>? ?? [])
          .map((item) => UserGroup.fromJson(item))
          .toList(),
      registerTime: json['registerTime'] ?? '',
      userId: json['userId'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'groups': groups.map((item) => item.toJson()).toList(),
      'registerTime': registerTime,
      'userId': userId,
    };
  }
}