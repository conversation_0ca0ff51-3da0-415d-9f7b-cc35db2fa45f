// 包体信息数据模型

/// 浮动配置信息
class FloatConfig {
  /// 是否显示浮动
  final bool showFloat;

  const FloatConfig({
    required this.showFloat,
  });

  factory FloatConfig.fromJson(Map<String, dynamic> json) {
    return FloatConfig(
      showFloat: json['show_float'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'show_float': showFloat,
    };
  }
}

/// 包体信息项
class PackageInfoItem {
  /// 包类型: official, wechat, other_package
  final String packageType;
  
  /// 选项标题: "官方包", "微信小游戏", "其他包"
  final String title;
  
  /// 包名
  final String packageName;
  
  /// 微信小游戏ID(当package_type为wechat时)
  final int? wechatMinigramId;
  
  /// icon链接
  final String iconUrl;
  
  /// 下载链接
  final String downloadUrl;
  
  /// 浮动配置(包类型为official时)
  final FloatConfig? floatConfig;
  
  /// 是否已经安装
  final bool hasInstall;

  const PackageInfoItem({
    required this.packageType,
    required this.title,
    required this.packageName,
    this.wechatMinigramId,
    required this.iconUrl,
    required this.downloadUrl,
    this.floatConfig,
    required this.hasInstall,
  });

  factory PackageInfoItem.fromJson(Map<String, dynamic> json) {
    return PackageInfoItem(
      packageType: json['package_type'] as String? ?? '',
      title: json['title'] as String? ?? '',
      packageName: json['package_name'] as String? ?? '',
      wechatMinigramId: json['wechat_minigram_id'] as int?,
      iconUrl: json['icon_url'] as String? ?? '',
      downloadUrl: json['download_url'] as String? ?? '',
      floatConfig: json['float_config'] != null 
          ? FloatConfig.fromJson(json['float_config'] as Map<String, dynamic>)
          : null,
      hasInstall: json['has_install'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'package_type': packageType,
      'title': title,
      'package_name': packageName,
      'wechat_minigram_id': wechatMinigramId,
      'icon_url': iconUrl,
      'download_url': downloadUrl,
      'float_config': floatConfig?.toJson(),
      'has_install': hasInstall,
    };
  }
}

/// 包体信息数据内容
class PackageInfoData {
  /// 包体信息列表
  final List<PackageInfoItem> packageInfo;

  const PackageInfoData({
    required this.packageInfo,
  });

  factory PackageInfoData.fromJson(Map<String, dynamic> json) {
    return PackageInfoData(
      packageInfo: (json['package_info'] as List<dynamic>?)
          ?.map((item) {
            if (item is Map<String, dynamic>) {
              return PackageInfoItem.fromJson(item);
            } else if (item is Map) {
              return PackageInfoItem.fromJson(Map<String, dynamic>.from(item));
            } else {
              return const PackageInfoItem(
                packageType: '',
                title: '',
                packageName: '',
                iconUrl: '',
                downloadUrl: '',
                hasInstall: false,
              );
            }
          })
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'package_info': packageInfo.map((item) => item.toJson()).toList(),
    };
  }
}

/// 包体信息响应数据模型
class PackageInfoResponse {
  /// 响应状态码
  final String state;
  
  /// 响应消息
  final String msg;
  
  /// 数据对象
  final PackageInfoData? data;

  const PackageInfoResponse({
    required this.state,
    required this.msg,
    this.data,
  });

  factory PackageInfoResponse.fromJson(Map<String, dynamic> json) {
    return PackageInfoResponse(
      state: json['state']?.toString() ?? '',
      msg: json['msg']?.toString() ?? '',
      data: json['data'] != null 
          ? PackageInfoData.fromJson(json['data'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'state': state,
      'msg': msg,
      'data': data?.toJson(),
    };
  }
}