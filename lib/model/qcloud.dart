/// 腾讯云设置
class QCloud {
  final String qcloudAppId;
  final bool qcloudClose;
  final bool qcloudCos;
  final bool qcloudCaptcha;
  final String qcloudCaptchaAppId;
  final bool qcloudFaceid;
  final bool qcloudSms;
  final bool qcloudVod;
  final bool qcloudCosDocPreview;
  final String qcloudCosBucketName;
  final String qcloudCosBucketArea;
  final bool qcloudCosSignUrl;
  final bool qcloudVodAutoPlay;
  final String qcloudVodToken;

  QCloud({
    required this.qcloudAppId,
    required this.qcloudClose,
    required this.qcloudCos,
    required this.qcloudCaptcha,
    required this.qcloudCaptchaAppId,
    required this.qcloudFaceid,
    required this.qcloudSms,
    required this.qcloudVod,
    required this.qcloudCosDocPreview,
    required this.qcloudCosBucketName,
    required this.qcloudCosBucketArea,
    required this.qcloudCosSignUrl,
    required this.qcloudVodAutoPlay,
    required this.qcloudVodToken,
  });

  factory QCloud.fromJson(Map<String, dynamic> json) {
    return QCloud(
      qcloudAppId: json['qcloudAppId'] ?? '',
      qcloudClose: json['qcloudClose'] ?? false,
      qcloudCos: json['qcloudCos'] ?? false,
      qcloudCaptcha: json['qcloudCaptcha'] ?? false,
      qcloudCaptchaAppId: json['qcloudCaptchaAppId'] ?? '',
      qcloudFaceid: json['qcloudFaceid'] ?? false,
      qcloudSms: json['qcloudSms'] ?? false,
      qcloudVod: json['qcloudVod'] ?? false,
      qcloudCosDocPreview: json['qcloudCosDocPreview'] ?? false,
      qcloudCosBucketName: json['qcloudCosBucketName'] ?? '',
      qcloudCosBucketArea: json['qcloudCosBucketArea'] ?? '',
      qcloudCosSignUrl: json['qcloudCosSignUrl'] ?? false,
      qcloudVodAutoPlay: json['qcloudVodAutoPlay'] ?? false,
      qcloudVodToken: json['qcloudVodToken'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'qcloudAppId': qcloudAppId,
      'qcloudClose': qcloudClose,
      'qcloudCos': qcloudCos,
      'qcloudCaptcha': qcloudCaptcha,
      'qcloudCaptchaAppId': qcloudCaptchaAppId,
      'qcloudFaceid': qcloudFaceid,
      'qcloudSms': qcloudSms,
      'qcloudVod': qcloudVod,
      'qcloudCosDocPreview': qcloudCosDocPreview,
      'qcloudCosBucketName': qcloudCosBucketName,
      'qcloudCosBucketArea': qcloudCosBucketArea,
      'qcloudCosSignUrl': qcloudCosSignUrl,
      'qcloudVodAutoPlay': qcloudVodAutoPlay,
      'qcloudVodToken': qcloudVodToken,
    };
  }
}