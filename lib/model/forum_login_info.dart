/// 论坛登录信息模型
class ForumLoginInfo {
  /// Token类型
  final String? tokenType;
  
  /// 过期时间（秒）
  final int? expiresIn;
  
  /// 访问令牌
  final String? accessToken;
  
  /// 刷新令牌
  final String? refreshToken;
  
  /// 是否缺少昵称
  final bool? isMissNickname;
  
  /// 头像URL
  final String? avatarUrl;
  
  /// 用户状态
  final int? userStatus;
  
  /// 用户ID
  final int? uid;
  
  /// 用户ID（备用字段）
  final int? userId;
  
  /// 平台ID
  final int? pid;
  
  /// 是否首次注册
  final int? firstReg;

  ForumLoginInfo({
    this.tokenType,
    this.expiresIn,
    this.accessToken,
    this.refreshToken,
    this.isMissNickname,
    this.avatarUrl,
    this.userStatus,
    this.uid,
    this.userId,
    this.pid,
    this.firstReg,
  });

  factory ForumLoginInfo.fromJson(Map<String, dynamic> json) {
    return ForumLoginInfo(
      tokenType: json['tokenType'] as String?,
      expiresIn: json['expiresIn'] as int?,
      accessToken: json['accessToken'] as String?,
      refreshToken: json['refreshToken'] as String?,
      isMissNickname: json['isMissNickname'] as bool?,
      avatarUrl: json['avatarUrl'] as String?,
      userStatus: json['userStatus'] as int?,
      uid: json['uid'] as int?,
      userId: json['userId'] as int?,
      pid: json['pid'] as int?,
      firstReg: json['firstReg'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'tokenType': tokenType,
      'expiresIn': expiresIn,
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'isMissNickname': isMissNickname,
      'avatarUrl': avatarUrl,
      'userStatus': userStatus,
      'uid': uid,
      'userId': userId,
      'pid': pid,
      'firstReg': firstReg,
    };
  }

  ForumLoginInfo copyWith({
    String? tokenType,
    int? expiresIn,
    String? accessToken,
    String? refreshToken,
    bool? isMissNickname,
    String? avatarUrl,
    int? userStatus,
    int? uid,
    int? userId,
    int? pid,
    int? firstReg,
  }) {
    return ForumLoginInfo(
      tokenType: tokenType ?? this.tokenType,
      expiresIn: expiresIn ?? this.expiresIn,
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      isMissNickname: isMissNickname ?? this.isMissNickname,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      userStatus: userStatus ?? this.userStatus,
      uid: uid ?? this.uid,
      userId: userId ?? this.userId,
      pid: pid ?? this.pid,
      firstReg: firstReg ?? this.firstReg,
    );
  }

  @override
  String toString() {
    return 'ForumLoginInfo(tokenType: $tokenType, expiresIn: $expiresIn, accessToken: $accessToken, refreshToken: $refreshToken, isMissNickname: $isMissNickname, avatarUrl: $avatarUrl, userStatus: $userStatus, uid: $uid, userId: $userId, pid: $pid, firstReg: $firstReg)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ForumLoginInfo &&
        other.tokenType == tokenType &&
        other.expiresIn == expiresIn &&
        other.accessToken == accessToken &&
        other.refreshToken == refreshToken &&
        other.isMissNickname == isMissNickname &&
        other.avatarUrl == avatarUrl &&
        other.userStatus == userStatus &&
        other.uid == uid &&
        other.userId == userId &&
        other.pid == pid &&
        other.firstReg == firstReg;
  }

  @override
  int get hashCode {
    return tokenType.hashCode ^
        expiresIn.hashCode ^
        accessToken.hashCode ^
        refreshToken.hashCode ^
        isMissNickname.hashCode ^
        avatarUrl.hashCode ^
        userStatus.hashCode ^
        uid.hashCode ^
        userId.hashCode ^
        pid.hashCode ^
        firstReg.hashCode;
  }
}
