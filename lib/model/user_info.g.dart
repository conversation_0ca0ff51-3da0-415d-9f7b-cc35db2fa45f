// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserInfo _$UserInfoFromJson(Map<String, dynamic> json) => UserInfo(
  userId: json['userId'] as String?,
  uname: json['uname'] as String?,
  alias: json['alias'] as String?,
  mobile: json['mobile'] as String?,
  loginType: json['loginType'] as String?,
  avatar: json['avatar'] as String?,
  createdAt:
      json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
  lastLoginAt:
      json['lastLoginAt'] == null
          ? null
          : DateTime.parse(json['lastLoginAt'] as String),
);

Map<String, dynamic> _$UserInfoToJson(UserInfo instance) => <String, dynamic>{
  'userId': instance.userId,
  'uname': instance.uname,
  'alias': instance.alias,
  'mobile': instance.mobile,
  'loginType': instance.loginType,
  'avatar': instance.avatar,
  'createdAt': instance.createdAt?.toIso8601String(),
  'lastLoginAt': instance.lastLoginAt?.toIso8601String(),
};
