import 'user_group_pivot.dart';

/// 用户组
class UserGroup {
  final int id;
  final String name;
  final String type;
  final String color;
  final String icon;
  final bool isDefault;
  final bool isDisplay;
  final int isPaid;
  final String fee;
  final int level;
  final int days;
  final int scale;
  final bool isSubordinate;
  final bool isCommission;
  final String description;
  final String notice;
  final int timeRange;
  final int contentRange;
  final int tgid;
  final UserGroupPivot pivot;

  UserGroup({
    required this.id,
    required this.name,
    required this.type,
    required this.color,
    required this.icon,
    required this.isDefault,
    required this.isDisplay,
    required this.isPaid,
    required this.fee,
    required this.level,
    required this.days,
    required this.scale,
    required this.isSubordinate,
    required this.isCommission,
    required this.description,
    required this.notice,
    required this.timeRange,
    required this.contentRange,
    required this.tgid,
    required this.pivot,
  });

  factory UserGroup.fromJson(Map<String, dynamic> json) {
    return UserGroup(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      type: json['type'] ?? '',
      color: json['color'] ?? '',
      icon: json['icon'] ?? '',
      isDefault: json['default'] ?? false,
      isDisplay: json['isDisplay'] ?? false,
      isPaid: json['isPaid'] ?? 0,
      fee: json['fee'] ?? '',
      level: json['level'] ?? 0,
      days: json['days'] ?? 0,
      scale: json['scale'] ?? 0,
      isSubordinate: json['isSubordinate'] ?? false,
      isCommission: json['isCommission'] ?? false,
      description: json['description'] ?? '',
      notice: json['notice'] ?? '',
      timeRange: json['timeRange'] ?? 0,
      contentRange: json['contentRange'] ?? 0,
      tgid: json['tgid'] ?? 0,
      pivot: UserGroupPivot.fromJson(json['pivot'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'color': color,
      'icon': icon,
      'default': isDefault,
      'isDisplay': isDisplay,
      'isPaid': isPaid,
      'fee': fee,
      'level': level,
      'days': days,
      'scale': scale,
      'isSubordinate': isSubordinate,
      'isCommission': isCommission,
      'description': description,
      'notice': notice,
      'timeRange': timeRange,
      'contentRange': contentRange,
      'tgid': tgid,
      'pivot': pivot.toJson(),
    };
  }
}