/// 授权应用项数据模型
class AuthAppItem {
  /// 微信小程序ID
  final String wechatMinigramId;
  
  /// 图标URL
  final String icon;
  
  /// 应用名称
  final String name;

  /// 游戏标识(tgid)
  final String tgid;

  const AuthAppItem({
    required this.wechatMinigramId,
    required this.icon,
    required this.name,
    required this.tgid,
  });

  AuthAppItem copyWith({
    String? wechatMinigramId,
    String? icon,
    String? name,
    String? tgid,
  }) {
    return AuthAppItem(
      wechatMinigramId: wechatMinigramId ?? this.wechatMinigramId,
      icon: icon ?? this.icon,
      name: name ?? this.name,
      tgid: tgid ?? this.tgid,
    );
  }

  factory AuthAppItem.fromJson(Map<String, dynamic> json) {
    return AuthAppItem(
      wechatMinigramId: json['wechat_minigram_id']?.toString() ?? '',
      icon: json['icon_url']?.toString() ?? '',
      name: json['display_name']?.toString() ?? '',
      tgid: json['tgid']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'wechat_minigram_id': wechatMinigramId,
      'icon': icon,
      'name': name,
      'tgid': tgid,
    };
  }
}

/// 授权应用列表响应数据模型
class AuthAppListData {
  /// 响应状态码
  final int state;
  
  /// 响应消息
  final String msg;
  
  /// 授权应用列表
  final List<AuthAppItem> list;

  const AuthAppListData({
    required this.state,
    required this.msg,
    required this.list,
  });

  AuthAppListData copyWith({
    int? state,
    String? msg,
    List<AuthAppItem>? list,
  }) {
    return AuthAppListData(
      state: state ?? this.state,
      msg: msg ?? this.msg,
      list: list ?? this.list,
    );
  }

  factory AuthAppListData.fromJson(Map<String, dynamic> json) {
    return AuthAppListData(
      state: json['state'] as int? ?? 0,
      msg: json['msg'] as String? ?? '',
      list: (json['list'] as List<dynamic>?)
          ?.map((item) {
            if (item is Map<String, dynamic>) {
              return AuthAppItem.fromJson(item);
            } else if (item is Map) {
              // 处理 Map<dynamic, dynamic> 类型
              return AuthAppItem.fromJson(Map<String, dynamic>.from(item));
            } else {
              // 如果不是Map类型，返回空的AuthAppItem
              return const AuthAppItem(wechatMinigramId: '', icon: '', name: '', tgid: '');
            }
          })
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'state': state,
      'msg': msg,
      'list': list.map((item) => item.toJson()).toList(),
    };
  }
}

/// 授权应用申请响应数据模型
class AuthAppApplyData {
  /// 追踪ID
  final String traceId;
  
  /// 随机值  
  final String nonce;

  const AuthAppApplyData({
    required this.traceId,
    required this.nonce,
  });

  AuthAppApplyData copyWith({
    String? traceId,
    String? nonce,
  }) {
    return AuthAppApplyData(
      traceId: traceId ?? this.traceId,
      nonce: nonce ?? this.nonce,
    );
  }

  factory AuthAppApplyData.fromJson(Map<String, dynamic> json) {
    return AuthAppApplyData(
      traceId: json['trace_id'] as String? ?? '',
      nonce: json['nonce'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'trace_id': traceId,
      'nonce': nonce,
    };
  }
}