/// M层SDK激活响应数据模型
/// 只处理API响应中data字段的内容
class MLayerActivateResponse {
  /// 设备ID
  final String? dev;
  
  /// 产品版本号
  final int? ph;
  
  /// 验证Token API
  final String? vptapi;
  
  /// 推送API
  final String? papi;
  
  /// 进入API
  final String? eapi;
  
  /// 网络URL
  final String? nurl;
  
  /// 测试URL
  final String? turl;
  
  /// 登录URL
  final String? lurl;
  
  /// 产品信息URL
  final String? pInfo;
  
  /// 广播列表API
  final String? rlapi;
  
  /// 广播频道API
  final String? rlcapi;
  
  /// 弹窗激活API
  final String? popUpsActiveApi;
  
  /// 弹窗登录API
  final String? popUpsLoginApi;
  
  /// 弹窗进入API
  final String? popUpsEnterApi;
  
  /// 弹窗充值API
  final String? popUpsRechargeApi;
  
  /// 是否启用认证
  final int? ry;
  
  /// 是否启用调试
  final int? de;
  
  /// 是否启用追踪
  final int? td;
  
  /// UV统计版本
  final String? uvs;
  
  /// 别名认证开关
  final int? aliauthswitch;
  
  /// 视频位置配置
  final Map<String, dynamic>? videopos;
  
  /// 更新类型
  final String? utype;
  
  /// 更新类型整数
  final int? utypeInt;
  
  /// 更新URL
  final String? uurl;
  
  /// 更新内容提示
  final String? uct;
  
  /// 安全数据
  final String? sdata;
  
  /// 防沉迷配置
  final AntiAddictionConfig? antiAddiction;
  
  /// SSS配置
  final SssConfig? sss;
  
  /// 用户配置
  final UserConfig? u;
  
  /// 是否触摸
  final int? isTouch;

  MLayerActivateResponse({
    this.dev,
    this.ph,
    this.vptapi,
    this.papi,
    this.eapi,
    this.nurl,
    this.turl,
    this.lurl,
    this.pInfo,
    this.rlapi,
    this.rlcapi,
    this.popUpsActiveApi,
    this.popUpsLoginApi,
    this.popUpsEnterApi,
    this.popUpsRechargeApi,
    this.ry,
    this.de,
    this.td,
    this.uvs,
    this.aliauthswitch,
    this.videopos,
    this.utype,
    this.utypeInt,
    this.uurl,
    this.uct,
    this.sdata,
    this.antiAddiction,
    this.sss,
    this.u,
    this.isTouch,
  });

  factory MLayerActivateResponse.fromJson(Map<String, dynamic> json) {
    return MLayerActivateResponse(
      dev: json['dev'],
      ph: json['ph'],
      vptapi: json['vptapi'],
      papi: json['papi'],
      eapi: json['eapi'],
      nurl: json['nurl'],
      turl: json['turl'],
      lurl: json['lurl'],
      pInfo: json['pInfo'],
      rlapi: json['rlapi'],
      rlcapi: json['rlcapi'],
      popUpsActiveApi: json['pop_ups_active_api'],
      popUpsLoginApi: json['pop_ups_login_api'],
      popUpsEnterApi: json['pop_ups_enter_api'],
      popUpsRechargeApi: json['pop_ups_recharge_api'],
      ry: json['ry'],
      de: json['de'],
      td: json['td'],
      uvs: json['uvs'],
      aliauthswitch: json['aliauthswitch'],
      videopos: json['videopos'] as Map<String, dynamic>?,
      utype: json['utype'],
      utypeInt: json['utypeInt'],
      uurl: json['uurl'],
      uct: json['uct'],
      sdata: json['sdata'],
      antiAddiction: json['anti_addiction'] != null
          ? AntiAddictionConfig.fromJson(json['anti_addiction'] as Map<String, dynamic>)
          : null,
      sss: json['sss'] != null
          ? SssConfig.fromJson(json['sss'] as Map<String, dynamic>)
          : null,
      u: json['u'] != null
          ? UserConfig.fromJson(json['u'] as Map<String, dynamic>)
          : null,
      isTouch: json['isTouch'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'dev': dev,
      'ph': ph,
      'vptapi': vptapi,
      'papi': papi,
      'eapi': eapi,
      'nurl': nurl,
      'turl': turl,
      'lurl': lurl,
      'pInfo': pInfo,
      'rlapi': rlapi,
      'rlcapi': rlcapi,
      'pop_ups_active_api': popUpsActiveApi,
      'pop_ups_login_api': popUpsLoginApi,
      'pop_ups_enter_api': popUpsEnterApi,
      'pop_ups_recharge_api': popUpsRechargeApi,
      'ry': ry,
      'de': de,
      'td': td,
      'uvs': uvs,
      'aliauthswitch': aliauthswitch,
      'videopos': videopos,
      'utype': utype,
      'utypeInt': utypeInt,
      'uurl': uurl,
      'uct': uct,
      'sdata': sdata,
      'anti_addiction': antiAddiction?.toJson(),
      'sss': sss?.toJson(),
      'u': u?.toJson(),
      'isTouch': isTouch,
    };
  }

  @override
  String toString() {
    return 'MLayerActivateResponse{dev: $dev, ph: $ph, vptapi: $vptapi, papi: $papi, ...}';
  }
}

/// 防沉迷配置
class AntiAddictionConfig {
  final int? state;

  AntiAddictionConfig({this.state});

  factory AntiAddictionConfig.fromJson(Map<String, dynamic> json) {
    return AntiAddictionConfig(
      state: json['state'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'state': state,
    };
  }

  @override
  String toString() {
    return 'AntiAddictionConfig{state: $state}';
  }
}

/// SSS配置
class SssConfig {
  final int? code;
  final String? hpid;
  final String? pid;
  final int? cid;
  final String? papid;

  SssConfig({
    this.code,
    this.hpid,
    this.pid,
    this.cid,
    this.papid,
  });

  factory SssConfig.fromJson(Map<String, dynamic> json) {
    return SssConfig(
      code: json['code'],
      hpid: json['hpid'],
      pid: json['pid'],
      cid: json['cid'],
      papid: json['papid'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'hpid': hpid,
      'pid': pid,
      'cid': cid,
      'papid': papid,
    };
  }

  @override
  String toString() {
    return 'SssConfig{code: $code, hpid: $hpid, pid: $pid, cid: $cid, papid: $papid}';
  }
}

/// 用户配置
class UserConfig {
  final String? uagreeUrl;
  final UagreeConfig? uagreeConfig;

  UserConfig({
    this.uagreeUrl,
    this.uagreeConfig,
  });

  factory UserConfig.fromJson(Map<String, dynamic> json) {
    return UserConfig(
      uagreeUrl: json['uagreeUrl'],
      uagreeConfig: json['uagreeConfig'] != null
          ? UagreeConfig.fromJson(json['uagreeConfig'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uagreeUrl': uagreeUrl,
      'uagreeConfig': uagreeConfig?.toJson(),
    };
  }

  @override
  String toString() {
    return 'UserConfig{uagreeUrl: $uagreeUrl, uagreeConfig: $uagreeConfig}';
  }
}

/// 用户协议配置
class UagreeConfig {
  final int? type;
  final int? version;

  UagreeConfig({
    this.type,
    this.version,
  });

  factory UagreeConfig.fromJson(Map<String, dynamic> json) {
    return UagreeConfig(
      type: json['type'],
      version: json['version'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'version': version,
    };
  }

  @override
  String toString() {
    return 'UagreeConfig{type: $type, version: $version}';
  }
}

