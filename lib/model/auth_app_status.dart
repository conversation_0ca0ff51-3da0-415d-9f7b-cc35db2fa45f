import 'login_info.dart';

/// 授权应用状态响应数据模型
class AuthAppStatusData {
  /// 状态 1:已确认通过；2:待确认；3:已过期
  final int status;

  /// 登录信息（包含用户信息和票据）
  final LoginInfo loginInfo;

  const AuthAppStatusData({required this.status, required this.loginInfo});

  // 为了向后兼容，提供访问 LoginInfo 字段的 getter
  String? get muname => loginInfo.muname;

  String? get muid => loginInfo.muid;

  String? get loginType => loginInfo.loginType;

  String? get appTicket => loginInfo.ticket;

  String? get appRefreshTicket => loginInfo.refreshTicket;

  String? get actionType => loginInfo.actionType;

  factory AuthAppStatusData.fromJson(Map<String, dynamic> json) {
    LoginInfo loginInfo = LoginInfo.fromJson(json);
    return AuthAppStatusData(status: json['status'] as int? ?? 0, loginInfo: loginInfo);
  }

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{'status': status};
    result.addAll(loginInfo.toJson());
    return result;
  }
}
