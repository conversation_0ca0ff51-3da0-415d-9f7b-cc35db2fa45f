/// 登录类型枚举
enum LoginType {
  /// 手机号登录
  phone,
  /// 微信登录
  wechat,
  /// 账号登录
  account,
}

/// 用户详细信息（从API接口获取的用户基本信息）
class UserDetail {
  /// 用户ID
  final String? muid;
  
  /// 头像URL
  final String? avatar;
  
  /// VIP等级
  final int vipLevelNum;
  
  /// 昵称
  final String? alias;
  
  /// 归属地
  final String? area;
  
  /// 密保手机
  final String? mibaoPhone;
  
  /// 登录手机
  final String? loginPhone;

  const UserDetail({
    this.muid,
    this.avatar,
    this.vipLevelNum = 0,
    this.alias,
    this.area,
    this.mibaoPhone,
    this.loginPhone,
  });

  /// 获取显示名称
  String get displayName {
    return alias ?? '未知用户';
  }

  /// 从JSON创建对象 (手动实现，类似LoginInfo的方式)
  factory UserDetail.fromJson(Map<String, dynamic> json) {
    return UserDetail(
      muid: json['muid']?.toString(),
      avatar: json['avatar_url'] as String?,
      vipLevelNum: json['vip_level_num'] as int? ?? 0,
      alias: json['nick_name'] as String?,
      area: json['area'] as String?,
      mibaoPhone: json['mibao_phone'] as String?,
      loginPhone: json['login_phone'] as String?,
    );
  }

  /// 转换为JSON (手动实现)
  Map<String, dynamic> toJson() {
    return {
      'muid': muid,
      'avatar_url': avatar,
      'vip_level_num': vipLevelNum,
      'nick_name': alias,
      'area': area,
      'mibao_phone': mibaoPhone,
      'login_phone': loginPhone,
    };
  }

  UserDetail copyWith({
    String? muid,
    String? avatar,
    int? vipLevelNum,
    String? alias,
    String? area,
    String? mibaoPhone,
    String? loginPhone,
  }) {
    return UserDetail(
      muid: muid ?? this.muid,
      avatar: avatar ?? this.avatar,
      vipLevelNum: vipLevelNum ?? this.vipLevelNum,
      alias: alias ?? this.alias,
      area: area ?? this.area,
      mibaoPhone: mibaoPhone ?? this.mibaoPhone,
      loginPhone: loginPhone ?? this.loginPhone,
    );
  }

  @override
  String toString() {
    return 'UserDetail(muid: $muid, alias: $alias, vipLevel: $vipLevelNum, area: $area)';
  }
}