class GameCircle {
  final String circleId;
  final String displayName;
  final String circleIcon;
  final String tgid;
  final String? description;
  final int? memberCount;
  final bool? isActive;

  GameCircle({
    required this.circleId,
    required this.displayName,
    required this.circleIcon,
    required this.tgid,
    this.description,
    this.memberCount,
    this.isActive,
  });

  factory GameCircle.fromJson(Map<String, dynamic> json) {
    return GameCircle(
      circleId: json['circle_id']?.toString() ?? '',
      displayName: json['display_name']?.toString() ?? '',
      circleIcon: json['circle_icon']?.toString() ?? '',
      tgid: json['tgid']?.toString() ?? '',
      description: json['description']?.toString(),
      memberCount: json['member_count'] is int 
          ? json['member_count'] 
          : int.tryParse(json['member_count']?.toString() ?? '0'),
      isActive: json['is_active'] is bool 
          ? json['is_active'] 
          : json['is_active']?.toString() == '1',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'circle_id': circleId,
      'display_name': displayName,
      'circle_icon': circleIcon,
      'tgid': tgid,
      'description': description,
      'member_count': memberCount,
      'is_active': isActive,
    };
  }

  @override
  String toString() {
    return 'GameCircle{circleId: $circleId, displayName: $displayName, circleIcon: $circleIcon, tgid: $tgid, description: $description, memberCount: $memberCount, isActive: $isActive}';
  }
}

class GameCircleListResponse {
  final List<GameCircle> circles;
  final int total;

  GameCircleListResponse({
    required this.circles,
    required this.total,
  });

  factory GameCircleListResponse.fromJson(Map<String, dynamic> json) {
    final List<dynamic> circleList = json['circles'] ?? json['list'] ?? json['data'] ?? [];
    final circles = circleList.map((item) => GameCircle.fromJson(item as Map<String, dynamic>)).toList();
    
    return GameCircleListResponse(
      circles: circles,
      total: json['total'] is int 
          ? json['total'] 
          : int.tryParse(json['total']?.toString() ?? '0') ?? circles.length,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'circles': circles.map((circle) => circle.toJson()).toList(),
      'total': total,
    };
  }

  @override
  String toString() {
    return 'GameCircleListResponse{circles: $circles, total: $total}';
  }
}