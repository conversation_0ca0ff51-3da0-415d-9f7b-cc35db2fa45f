/// 游戏角色信息数据模型
class GameRole {
  /// 角色ID
  final String drid;
  
  /// 服务器ID
  final String dsid;
  
  /// 角色名称
  final String drname;
  
  /// 服务器名称
  final String dsname;
  
  /// 角色等级
  final String drlevel;
  
  /// 角色游戏ID
  final int roleGid;
  
  /// 角色项目ID
  final int rolePid;
  
  /// 最后登录时间
  final String lastLogin;

  const GameRole({
    required this.drid,
    required this.dsid,
    required this.drname,
    required this.dsname,
    required this.drlevel,
    required this.roleGid,
    required this.rolePid,
    required this.lastLogin,
  });

  GameRole copyWith({
    String? drid,
    String? dsid,
    String? drname,
    String? dsname,
    String? drlevel,
    int? roleGid,
    int? rolePid,
    String? lastLogin,
  }) {
    return GameRole(
      drid: drid ?? this.drid,
      dsid: dsid ?? this.dsid,
      drname: drname ?? this.drname,
      dsname: dsname ?? this.dsname,
      drlevel: drlevel ?? this.drlevel,
      roleGid: roleGid ?? this.roleGid,
      rolePid: rolePid ?? this.rolePid,
      lastLogin: lastLogin ?? this.lastLogin,
    );
  }

  factory GameRole.fromJson(Map<String, dynamic> json) {
    return GameRole(
      drid: json['drid']?.toString() ?? '',
      dsid: json['dsid']?.toString() ?? '',
      drname: json['drname']?.toString() ?? '',
      dsname: json['dsname']?.toString() ?? '',
      drlevel: json['drlevel']?.toString() ?? '',
      roleGid: json['role_gid'] as int? ?? 0,
      rolePid: json['role_pid'] as int? ?? 0,
      lastLogin: json['last_login']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'drid': drid,
      'dsid': dsid,
      'drname': drname,
      'dsname': dsname,
      'drlevel': drlevel,
      'role_gid': roleGid,
      'role_pid': rolePid,
      'last_login': lastLogin,
    };
  }
}

/// 游戏角色V2信息数据模型 (接口二使用)
class GameRoleV2 {
  /// 角色ID (数字类型)
  final int drid;
  
  /// 服务器ID
  final String dsid;
  
  /// 角色名称
  final String drname;
  
  /// 服务器名称
  final String dsname;
  
  /// 用户ID
  final String uid;
  
  /// 角色收藏ID
  final String roleFavoriteId;
  
  /// 游戏ID
  final String tgid;
  
  /// 角色项目ID
  final String rolePid;
  
  /// 角色游戏ID
  final String roleGid;

  const GameRoleV2({
    required this.drid,
    required this.dsid,
    required this.drname,
    required this.dsname,
    required this.uid,
    required this.roleFavoriteId,
    required this.tgid,
    required this.rolePid,
    required this.roleGid,
  });

  GameRoleV2 copyWith({
    int? drid,
    String? dsid,
    String? drname,
    String? dsname,
    String? uid,
    String? roleFavoriteId,
    String? tgid,
    String? rolePid,
    String? roleGid,
  }) {
    return GameRoleV2(
      drid: drid ?? this.drid,
      dsid: dsid ?? this.dsid,
      drname: drname ?? this.drname,
      dsname: dsname ?? this.dsname,
      uid: uid ?? this.uid,
      roleFavoriteId: roleFavoriteId ?? this.roleFavoriteId,
      tgid: tgid ?? this.tgid,
      rolePid: rolePid ?? this.rolePid,
      roleGid: roleGid ?? this.roleGid,
    );
  }

  factory GameRoleV2.fromJson(Map<String, dynamic> json) {
    return GameRoleV2(
      drid: json['drid'] as int? ?? 0,
      dsid: json['dsid']?.toString() ?? '',
      drname: json['drname']?.toString() ?? '',
      dsname: json['dsname']?.toString() ?? '',
      uid: json['uid']?.toString() ?? '',
      roleFavoriteId: json['role_favorite_id']?.toString() ?? '',
      tgid: json['tgid']?.toString() ?? '',
      rolePid: json['role_pid']?.toString() ?? '',
      roleGid: json['role_gid']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'drid': drid,
      'dsid': dsid,
      'drname': drname,
      'dsname': dsname,
      'uid': uid,
      'role_favorite_id': roleFavoriteId,
      'tgid': tgid,
      'role_pid': rolePid,
      'role_gid': roleGid,
    };
  }
}

/// 游戏角色列表响应数据模型
class GameRoleListData {
  /// 响应状态码
  final int state;
  
  /// 响应消息
  final String msg;
  
  /// 角色列表
  final List<GameRole> data;

  const GameRoleListData({
    required this.state,
    required this.msg,
    required this.data,
  });

  GameRoleListData copyWith({
    int? state,
    String? msg,
    List<GameRole>? data,
  }) {
    return GameRoleListData(
      state: state ?? this.state,
      msg: msg ?? this.msg,
      data: data ?? this.data,
    );
  }

  factory GameRoleListData.fromJson(Map<String, dynamic> json) {
    return GameRoleListData(
      state: json['state'] as int? ?? 0,
      msg: json['msg'] as String? ?? '',
      data: (json['data'] as List<dynamic>?)
          ?.map((item) {
            if (item is Map<String, dynamic>) {
              return GameRole.fromJson(item);
            } else if (item is Map) {
              return GameRole.fromJson(Map<String, dynamic>.from(item));
            } else {
              return const GameRole(
                drid: '',
                dsid: '',
                drname: '',
                dsname: '',
                drlevel: '',
                roleGid: 0,
                rolePid: 0,
                lastLogin: '',
              );
            }
          })
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'state': state,
      'msg': msg,
      'data': data.map((item) => item.toJson()).toList(),
    };
  }
}

/// 游戏角色V2列表响应数据模型 (接口二使用)
class GameRoleListV2Data {
  /// 响应状态码
  final int state;
  
  /// 响应消息
  final String msg;
  
  /// 数据对象
  final GameRoleListV2DataContent? data;

  const GameRoleListV2Data({
    required this.state,
    required this.msg,
    this.data,
  });

  GameRoleListV2Data copyWith({
    int? state,
    String? msg,
    GameRoleListV2DataContent? data,
  }) {
    return GameRoleListV2Data(
      state: state ?? this.state,
      msg: msg ?? this.msg,
      data: data ?? this.data,
    );
  }

  factory GameRoleListV2Data.fromJson(Map<String, dynamic> json) {
    return GameRoleListV2Data(
      state: json['state'] as int? ?? 0,
      msg: json['msg'] as String? ?? '',
      data: json['data'] != null 
          ? GameRoleListV2DataContent.fromJson(json['data'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'state': state,
      'msg': msg,
      'data': data?.toJson(),
    };
  }
}

/// 游戏角色V2列表数据内容
class GameRoleListV2DataContent {
  /// 角色列表
  final List<GameRoleV2> list;

  const GameRoleListV2DataContent({
    required this.list,
  });

  GameRoleListV2DataContent copyWith({
    List<GameRoleV2>? list,
  }) {
    return GameRoleListV2DataContent(
      list: list ?? this.list,
    );
  }

  factory GameRoleListV2DataContent.fromJson(Map<String, dynamic> json) {
    return GameRoleListV2DataContent(
      list: (json['list'] as List<dynamic>?)
          ?.map((item) {
            if (item is Map<String, dynamic>) {
              return GameRoleV2.fromJson(item);
            } else if (item is Map) {
              return GameRoleV2.fromJson(Map<String, dynamic>.from(item));
            } else {
              return const GameRoleV2(
                drid: 0,
                dsid: '',
                drname: '',
                dsname: '',
                uid: '',
                roleFavoriteId: '',
                tgid: '',
                rolePid: '',
                roleGid: '',
              );
            }
          })
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'list': list.map((item) => item.toJson()).toList(),
    };
  }
}

/// 角色选择响应数据模型 (接口三使用)
class RolePickResponseData {
  /// 响应状态码
  final int state;
  
  /// 响应消息
  final String msg;
  
  /// 数据对象 (可能为空)
  final Map<String, dynamic>? data;

  const RolePickResponseData({
    required this.state,
    required this.msg,
    this.data,
  });

  RolePickResponseData copyWith({
    int? state,
    String? msg,
    Map<String, dynamic>? data,
  }) {
    return RolePickResponseData(
      state: state ?? this.state,
      msg: msg ?? this.msg,
      data: data ?? this.data,
    );
  }

  factory RolePickResponseData.fromJson(Map<String, dynamic> json) {
    return RolePickResponseData(
      state: json['state'] as int? ?? 0,
      msg: json['msg'] as String? ?? '',
      data: json['data'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'state': state,
      'msg': msg,
      'data': data,
    };
  }
}