class AppReviewStatusResponse {
  final bool inReview;

  AppReviewStatusResponse({
    required this.inReview,
  });

  factory AppReviewStatusResponse.fromJson(Map<String, dynamic> json) {
    return AppReviewStatusResponse(
      inReview: json['in_review'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'in_review': inReview,
    };
  }

  @override
  String toString() {
    return 'AppReviewStatusResponse{inReview: $inReview}';
  }
}