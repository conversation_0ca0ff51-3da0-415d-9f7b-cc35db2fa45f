/// ChatGPT设置
class SetChatgpt {
  final String fandaiurl;
  final String airenge;
  final String apikey;
  final int aicid;
  final int aiuid;
  final String aiusername;
  final String aipassword;
  final String hosturl;
  final String model;
  final bool callai;
  final bool offiaccount;
  final bool revoice;

  SetChatgpt({
    required this.fandaiurl,
    required this.airenge,
    required this.apikey,
    required this.aicid,
    required this.aiuid,
    required this.aiusername,
    required this.aipassword,
    required this.hosturl,
    required this.model,
    required this.callai,
    required this.offiaccount,
    required this.revoice,
  });

  factory SetChatgpt.fromJson(Map<String, dynamic> json) {
    return SetChatgpt(
      fandaiurl: json['fandaiurl'] ?? '',
      airenge: json['airenge'] ?? '',
      apikey: json['apikey'] ?? '',
      aicid: json['aicid'] ?? 0,
      aiuid: json['aiuid'] ?? 0,
      aiusername: json['aiusername'] ?? '',
      aipassword: json['aipassword'] ?? '',
      hosturl: json['hosturl'] ?? '',
      model: json['model'] ?? '',
      callai: json['callai'] ?? false,
      offiaccount: json['offiaccount'] ?? false,
      revoice: json['revoice'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fandaiurl': fandaiurl,
      'airenge': airenge,
      'apikey': apikey,
      'aicid': aicid,
      'aiuid': aiuid,
      'aiusername': aiusername,
      'aipassword': aipassword,
      'hosturl': hosturl,
      'model': model,
      'callai': callai,
      'offiaccount': offiaccount,
      'revoice': revoice,
    };
  }
}