
/// 游戏中心游戏配置数据模型
class GameJumpConfig {
  final GameActionConfig gameActionConfig;

  GameJumpConfig({
    required this.gameActionConfig,
  });

  factory GameJumpConfig.fromJson(Map<String, dynamic> json) {
    return GameJumpConfig(
      gameActionConfig: GameActionConfig.fromJson(Map<String, dynamic>.from(json['download_page_config'] ?? {})),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'gameActionConfig': gameActionConfig.toJson(),
    };
  }
}


/// 游戏操作配置
class GameActionConfig {
  final String actionType;
  final String directPackage;
  final String downloadUrl;
  final List<ChoiceOptions> choiceOptions;

  GameActionConfig({
    required this.actionType,
    required this.directPackage,
    required this.downloadUrl,
    required this.choiceOptions,
  });

  factory GameActionConfig.fromJson(Map<String, dynamic> json) {
    return GameActionConfig(
      actionType: json['action_type'] ?? '',
      directPackage: json['direct_package'] ?? '',
      downloadUrl: json['download_url'] ?? '',
      choiceOptions: (json['choice_options'] as List<dynamic>? ?? [])
          .map((item) => ChoiceOptions.fromJson(item))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'actionType': actionType,
      'directPackage': directPackage,
      'downloadUrl': downloadUrl,
      'choiceOptions': choiceOptions.map((post) => post.toJson()).toList(),
    };
  }
}

/// 选择选项
class ChoiceOptions {
  final String packageType;
  final String title;
  final String iconUrl;
  final String hasInstall;
  final String downloadUrl;
  final String packageName;
  final String wechatMinigramId;
  final FloatConfig floatConfig;

  ChoiceOptions({
    required this.packageType,
    required this.title,
    required this.iconUrl,
    required this.hasInstall,
    required this.downloadUrl,
    required this.packageName,
    required this.wechatMinigramId,
    required this.floatConfig,
  });

  factory ChoiceOptions.fromJson(Map<String, dynamic> json) {
    return ChoiceOptions(
      packageType: json['package_type'] ?? '',
      title: json['title'] ?? '',
      iconUrl: json['icon_url'] ?? '',
      hasInstall: json['has_install'] ?? '',
      downloadUrl: json['download_url'] ?? '',
      packageName: json['package_name'] ?? '',
      wechatMinigramId: json['wechat_minigram_id'] ?? '',
      floatConfig: FloatConfig.fromJson(Map<String, dynamic>.from(json['float_config'] ?? {})),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'packageType': packageType,
      'title': title,
      'iconUrl': iconUrl,
      'hasInstall': hasInstall,
      'downloadUrl': downloadUrl,
      'packageName': packageName,
      'wechatMinigramId': wechatMinigramId,
      'floatConfig': floatConfig.toJson(),
    };
  }
}

/// 悬浮信息配置
class FloatConfig {
  final String floatMsg;

  FloatConfig({
    required this.floatMsg,
  });

  factory FloatConfig.fromJson(Map<String, dynamic> json) {
    return FloatConfig(
        floatMsg: json['float_msg'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'floatMsg': floatMsg,
    };
  }
}