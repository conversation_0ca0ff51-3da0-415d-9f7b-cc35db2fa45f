/// 其他设置
class Other {
  final int countThreads;
  final int countPosts;
  final int countUsers;
  final bool canEditUserGroup;
  final bool canEditUserStatus;
  final bool canCreateThreadInCategory;
  final bool canViewThreads;
  final bool canFreeViewPaidThreads;
  final bool canCreateDialog;
  final bool canInviteUserScale;
  final bool canInsertThreadAttachment;
  final bool canInsertThreadPaid;
  final bool canInsertThreadVideo;
  final bool canInsertThreadImage;
  final bool canInsertThreadAudio;
  final bool canInsertThreadGoods;
  final bool canInsertThreadPosition;
  final bool canInsertThreadRedPacket;
  final bool canInsertThreadReward;
  final bool canInsertThreadAnonymous;
  final bool canInsertThreadVote;
  final bool initializedPayPassword;
  final bool createThreadWithCaptcha;
  final bool publishNeedBindPhone;
  final bool publishNeedBindWechat;
  final bool disabledChat;
  final bool threadOptimize;
  final int threadTab;

  Other({
    required this.countThreads,
    required this.countPosts,
    required this.countUsers,
    required this.canEditUserGroup,
    required this.canEditUserStatus,
    required this.canCreateThreadInCategory,
    required this.canViewThreads,
    required this.canFreeViewPaidThreads,
    required this.canCreateDialog,
    required this.canInviteUserScale,
    required this.canInsertThreadAttachment,
    required this.canInsertThreadPaid,
    required this.canInsertThreadVideo,
    required this.canInsertThreadImage,
    required this.canInsertThreadAudio,
    required this.canInsertThreadGoods,
    required this.canInsertThreadPosition,
    required this.canInsertThreadRedPacket,
    required this.canInsertThreadReward,
    required this.canInsertThreadAnonymous,
    required this.canInsertThreadVote,
    required this.initializedPayPassword,
    required this.createThreadWithCaptcha,
    required this.publishNeedBindPhone,
    required this.publishNeedBindWechat,
    required this.disabledChat,
    required this.threadOptimize,
    required this.threadTab,
  });

  factory Other.fromJson(Map<String, dynamic> json) {
    return Other(
      countThreads: json['countThreads'] ?? 0,
      countPosts: json['countPosts'] ?? 0,
      countUsers: json['countUsers'] ?? 0,
      canEditUserGroup: json['canEditUserGroup'] ?? false,
      canEditUserStatus: json['canEditUserStatus'] ?? false,
      canCreateThreadInCategory: json['canCreateThreadInCategory'] ?? false,
      canViewThreads: json['canViewThreads'] ?? false,
      canFreeViewPaidThreads: json['canFreeViewPaidThreads'] ?? false,
      canCreateDialog: json['canCreateDialog'] ?? false,
      canInviteUserScale: json['canInviteUserScale'] ?? false,
      canInsertThreadAttachment: json['canInsertThreadAttachment'] ?? false,
      canInsertThreadPaid: json['canInsertThreadPaid'] ?? false,
      canInsertThreadVideo: json['canInsertThreadVideo'] ?? false,
      canInsertThreadImage: json['canInsertThreadImage'] ?? false,
      canInsertThreadAudio: json['canInsertThreadAudio'] ?? false,
      canInsertThreadGoods: json['canInsertThreadGoods'] ?? false,
      canInsertThreadPosition: json['canInsertThreadPosition'] ?? false,
      canInsertThreadRedPacket: json['canInsertThreadRedPacket'] ?? false,
      canInsertThreadReward: json['canInsertThreadReward'] ?? false,
      canInsertThreadAnonymous: json['canInsertThreadAnonymous'] ?? false,
      canInsertThreadVote: json['canInsertThreadVote'] ?? false,
      initializedPayPassword: json['initializedPayPassword'] ?? false,
      createThreadWithCaptcha: json['createThreadWithCaptcha'] ?? false,
      publishNeedBindPhone: json['publishNeedBindPhone'] ?? false,
      publishNeedBindWechat: json['publishNeedBindWechat'] ?? false,
      disabledChat: json['disabledChat'] ?? false,
      threadOptimize: json['threadOptimize'] ?? false,
      threadTab: json['threadTab'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'countThreads': countThreads,
      'countPosts': countPosts,
      'countUsers': countUsers,
      'canEditUserGroup': canEditUserGroup,
      'canEditUserStatus': canEditUserStatus,
      'canCreateThreadInCategory': canCreateThreadInCategory,
      'canViewThreads': canViewThreads,
      'canFreeViewPaidThreads': canFreeViewPaidThreads,
      'canCreateDialog': canCreateDialog,
      'canInviteUserScale': canInviteUserScale,
      'canInsertThreadAttachment': canInsertThreadAttachment,
      'canInsertThreadPaid': canInsertThreadPaid,
      'canInsertThreadVideo': canInsertThreadVideo,
      'canInsertThreadImage': canInsertThreadImage,
      'canInsertThreadAudio': canInsertThreadAudio,
      'canInsertThreadGoods': canInsertThreadGoods,
      'canInsertThreadPosition': canInsertThreadPosition,
      'canInsertThreadRedPacket': canInsertThreadRedPacket,
      'canInsertThreadReward': canInsertThreadReward,
      'canInsertThreadAnonymous': canInsertThreadAnonymous,
      'canInsertThreadVote': canInsertThreadVote,
      'initializedPayPassword': initializedPayPassword,
      'createThreadWithCaptcha': createThreadWithCaptcha,
      'publishNeedBindPhone': publishNeedBindPhone,
      'publishNeedBindWechat': publishNeedBindWechat,
      'disabledChat': disabledChat,
      'threadOptimize': threadOptimize,
      'threadTab': threadTab,
    };
  }
}