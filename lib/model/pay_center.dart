/// 支付中心设置
class PayCenter {
  final bool wxpayClose;
  final bool wxpayIos;
  final bool wxpayMchpayClose;
  final bool wxpayMchpayClose2;

  PayCenter({
    required this.wxpayClose,
    required this.wxpayIos,
    required this.wxpayMchpayClose,
    required this.wxpayMchpayClose2,
  });

  factory PayCenter.fromJson(Map<String, dynamic> json) {
    return PayCenter(
      wxpayClose: json['wxpayClose'] ?? false,
      wxpayIos: json['wxpayIos'] ?? false,
      wxpayMchpayClose: json['wxpayMchpayClose'] ?? false,
      wxpayMchpayClose2: json['wxpayMchpayClose2'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'wxpayClose': wxpayClose,
      'wxpayIos': wxpayIos,
      'wxpayMchpayClose': wxpayMchpayClose,
      'wxpayMchpayClose2': wxpayMchpayClose2,
    };
  }
}