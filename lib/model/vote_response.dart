class VoteResponse {
  final int alreadyVoted;
  final VoteConfig voteConfig;
  final int voteUserCount;

  VoteResponse({
    required this.alreadyVoted,
    required this.voteConfig,
    required this.voteUserCount,
  });

  factory VoteResponse.fromJson(Map<String, dynamic> json) {
    return VoteResponse(
      alreadyVoted: json['alreadyVoted'] ?? 0,
      voteConfig: VoteConfig.fromJson(json['voteConfig'] ?? {}),
      voteUserCount: json['voteUserCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'alreadyVoted': alreadyVoted,
      'voteConfig': voteConfig.toJson(),
      'voteUserCount': voteUserCount,
    };
  }
}

class VoteConfig {
  final String title;
  final String items;
  final int type;
  final int maxChoice;
  final String startVoteTime;
  final String endVoteTime;
  final String voteState;
  final List<VoteItemDetail> voteItemsDetail;

  VoteConfig({
    required this.title,
    required this.items,
    required this.type,
    required this.maxChoice,
    required this.startVoteTime,
    required this.endVoteTime,
    required this.voteState,
    required this.voteItemsDetail,
  });

  factory VoteConfig.fromJson(Map<String, dynamic> json) {
    return VoteConfig(
      title: json['title'] ?? '',
      items: json['items'] ?? '',
      type: json['type'] ?? 1,
      maxChoice: json['max_choice'] ?? 1,
      startVoteTime: json['start_vote_time'] ?? '',
      endVoteTime: json['end_vote_time'] ?? '',
      voteState: json['vote_state'] ?? '',
      voteItemsDetail: (json['vote_items_detail'] as List<dynamic>?)
              ?.map((item) => VoteItemDetail.fromJson(item))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'items': items,
      'type': type,
      'max_choice': maxChoice,
      'start_vote_time': startVoteTime,
      'end_vote_time': endVoteTime,
      'vote_state': voteState,
      'vote_items_detail': voteItemsDetail.map((item) => item.toJson()).toList(),
    };
  }
}

class VoteItemDetail {
  final int id;
  final String content;
  final int number;
  final int? checked;

  VoteItemDetail({
    required this.id,
    required this.content,
    required this.number,
    this.checked,
  });

  factory VoteItemDetail.fromJson(Map<String, dynamic> json) {
    return VoteItemDetail(
      id: json['id'] ?? 0,
      content: json['content'] ?? '',
      number: json['number'] ?? 0,
      checked: json['checked'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'number': number,
      if (checked != null) 'checked': checked,
    };
  }
}