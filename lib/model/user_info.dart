import 'package:json_annotation/json_annotation.dart';

part 'user_info.g.dart';

/// 登录类型枚举
enum LoginType {
  /// 手机号登录
  phone,
  /// 微信登录
  wechat,
  /// 账号登录
  account,
}

/// 用户信息模型
@JsonSerializable()
class UserInfo {
  /// 用户ID
  final String? userId;
  
  /// 用户名
  final String? uname;
  
  /// 别名
  final String? alias;
  
  /// 手机号
  final String? mobile;
  
  /// 登录类型
  final String? loginType;
  
  /// 头像URL
  final String? avatar;
  
  /// 创建时间
  final DateTime? createdAt;
  
  /// 最后登录时间
  final DateTime? lastLoginAt;

  UserInfo({
    this.userId,
    this.uname,
    this.alias,
    this.mobile,
    this.loginType,
    this.avatar,
    this.createdAt,
    this.lastLoginAt,
  });

  /// 获取显示名称
  String get displayName {
    // 如果是手机号登录，直接显示手机号
    if (loginType == 'phone' && mobile != null && mobile!.isNotEmpty) {
      return mobile!;
    }
    // 其他情况按优先级显示
    if (alias != null && alias!.isNotEmpty) {
      return alias!;
    }
    if (uname != null && uname!.isNotEmpty) {
      return uname!;
    }
    if (mobile != null && mobile!.isNotEmpty) {
      return mobile!;
    }
    return '未知用户';
  }

  /// 获取登录类型枚举
  LoginType get loginTypeEnum {
    switch (loginType) {
      case 'phone':
        return LoginType.phone;
      case 'wechat':
        return LoginType.wechat;
      case 'account':
        return LoginType.account;
      default:
        return LoginType.account;
    }
  }

  /// 从JSON创建对象
  factory UserInfo.fromJson(Map<String, dynamic> json) => _$UserInfoFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$UserInfoToJson(this);

  /// 复制并更新对象
  UserInfo copyWith({
    String? userId,
    String? uname,
    String? alias,
    String? mobile,
    String? loginType,
    String? avatar,
    DateTime? createdAt,
    DateTime? lastLoginAt,
  }) {
    return UserInfo(
      userId: userId ?? this.userId,
      uname: uname ?? this.uname,
      alias: alias ?? this.alias,
      mobile: mobile ?? this.mobile,
      loginType: loginType ?? this.loginType,
      avatar: avatar ?? this.avatar,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserInfo && other.userId == userId;
  }

  @override
  int get hashCode => userId.hashCode;

  @override
  String toString() {
    return 'UserInfo(userId: $userId, displayName: $displayName, loginType: $loginType)';
  }
} 