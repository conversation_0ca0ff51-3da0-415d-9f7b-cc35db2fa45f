/// 护照设置
class Passport {
  final bool offiaccountOpen;
  final bool miniprogramOpen;

  Passport({
    required this.offiaccountOpen,
    required this.miniprogramOpen,
  });

  factory Passport.fromJson(Map<String, dynamic> json) {
    // 安全地获取值，处理类型转换
    T? _safeGet<T>(String key) {
      final value = json[key];
      if (value == null) return null;
      
      if (value is T) return value;
      
      if (T == bool) {
        if (value is String) {
          return (value.toLowerCase() == 'true' || value == '1') as T?;
        }
        if (value is int) return (value == 1) as T?;
      }
      
      return null;
    }

    return Passport(
      offiaccountOpen: _safeGet<bool>('offiaccountOpen') ?? false,
      miniprogramOpen: _safeGet<bool>('miniprogramOpen') ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'offiaccountOpen': offiaccountOpen,
      'miniprogramOpen': miniprogramOpen,
    };
  }
}