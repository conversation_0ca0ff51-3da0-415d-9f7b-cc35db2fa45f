/// 协议设置
class Agreement {
  final bool privacy;
  final String privacyContent;
  final bool register;
  final String registerContent;

  Agreement({
    required this.privacy,
    required this.privacyContent,
    required this.register,
    required this.registerContent,
  });

  factory Agreement.fromJson(Map<String, dynamic> json) {
    return Agreement(
      privacy: json['privacy'] ?? false,
      privacyContent: json['privacyContent'] ?? '',
      register: json['register'] ?? false,
      registerContent: json['registerContent'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'privacy': privacy,
      'privacyContent': privacyContent,
      'register': register,
      'registerContent': registerContent,
    };
  }
}