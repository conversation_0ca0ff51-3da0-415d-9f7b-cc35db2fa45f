/// 活动模块
class ActModule {
  final String url;
  final String icon;
  final int sort;
  final String actName;
  final String? linkType;
  final int needLogin;
  final String? miniProgramConfigId;

  ActModule({
    required this.url,
    required this.icon,
    required this.sort,
    required this.actName,
    this.linkType,
    required this.needLogin,
    this.miniProgramConfigId,
  });

  factory ActModule.fromJson(Map<String, dynamic> json) {
    return ActModule(
      url: json['url'] ?? '',
      icon: json['icon'] ?? '',
      sort: json['sort'] ?? 0,
      actName: json['act_name'] ?? '',
      linkType: json['linkType']?.toString(),
      needLogin: json['need_login'] is String
          ? int.tryParse(json['need_login']) ?? 0
          : (json['need_login'] ?? 0),
      // 该字段后端有时返回数字，有时返回字符串，这里统一转为字符串
      miniProgramConfigId: json['miniProgramConfigId'] == null
          ? null
          : json['miniProgramConfigId'].toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'url': url,
      'icon': icon,
      'sort': sort,
      'act_name': actName,
      'linkType': linkType,
      'need_login': needLogin,
      'miniProgramConfigId': miniProgramConfigId,
    };
  }
}