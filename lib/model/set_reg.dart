/// 注册设置
class SetReg {
  final int registerType;
  final bool registerClose;
  final bool registerValidate;
  final bool registerCaptcha;
  final int passwordLength;
  final List<dynamic> passwordStrength;
  final bool isNeedTransition;

  SetReg({
    required this.registerType,
    required this.registerClose,
    required this.registerValidate,
    required this.registerCaptcha,
    required this.passwordLength,
    required this.passwordStrength,
    required this.isNeedTransition,
  });

  factory SetReg.fromJson(Map<String, dynamic> json) {
    // 安全地获取值，处理类型转换
    T? _safeGet<T>(String key) {
      final value = json[key];
      if (value == null) return null;
      
      // 如果已经是目标类型，直接返回
      if (value is T) return value;
      
      // 特殊处理不同类型的转换
      if (T == int) {
        if (value is String) return int.tryParse(value) as T?;
        if (value is double) return value.toInt() as T?;
      } else if (T == bool) {
        if (value is String) {
          return (value.toLowerCase() == 'true' || value == '1') as T?;
        }
        if (value is int) return (value == 1) as T?;
      }
      
      return null;
    }

    return SetReg(
      registerType: _safeGet<int>('registerType') ?? 0,
      registerClose: _safeGet<bool>('registerClose') ?? false,
      registerValidate: _safeGet<bool>('registerValidate') ?? false,
      registerCaptcha: _safeGet<bool>('registerCaptcha') ?? false,
      passwordLength: _safeGet<int>('passwordLength') ?? 0,
      passwordStrength: json['passwordStrength'] ?? [],
      isNeedTransition: _safeGet<bool>('isNeedTransition') ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'registerType': registerType,
      'registerClose': registerClose,
      'registerValidate': registerValidate,
      'registerCaptcha': registerCaptcha,
      'passwordLength': passwordLength,
      'passwordStrength': passwordStrength,
      'isNeedTransition': isNeedTransition,
    };
  }
}