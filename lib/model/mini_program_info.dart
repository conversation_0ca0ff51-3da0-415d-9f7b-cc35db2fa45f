import 'dart:async';
import 'package:flutter/cupertino.dart';

/// 小程序跳转数据模型
class MiniProgramInfo {
  final int skipType;
  final String appId;
  final String miniProgramId;
  final String miniProgramPath;
  final String schemeUrl;

  MiniProgramInfo({
    required this.skipType,
    required this.appId,
    required this.miniProgramId,
    required this.miniProgramPath,
    required this.schemeUrl,
  });

  factory MiniProgramInfo.fromJson(Map<String, dynamic> json) {
    return MiniProgramInfo(
      skipType: json['skip_type'] ?? 0,
      appId: json['appid'] ?? '',
      miniProgramId: json['mini_program_id'] ?? '',
      miniProgramPath: json['mini_program_path'] ?? '',
      schemeUrl: json['scheme_url'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'skipType': skipType,
      'appId': appId,
      'miniProgramId': miniProgramId,
      'miniProgramPath': miniProgramPath,
      'schemeUrl': schemeUrl,
    };
  }
}