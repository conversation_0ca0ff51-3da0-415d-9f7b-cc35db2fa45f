/// 平台设置
class PlatformSetting {
  final int key;
  final String desc;
  final bool value;

  PlatformSetting({
    required this.key,
    required this.desc,
    required this.value,
  });

  factory PlatformSetting.fromJson(Map<String, dynamic> json) {
    // 安全地获取值，处理类型转换
    T? _safeGet<T>(String key) {
      final value = json[key];
      if (value == null) return null;
      
      // 如果已经是目标类型，直接返回
      if (value is T) return value;
      
      // 特殊处理不同类型的转换
      if (T == int) {
        if (value is String) return int.tryParse(value) as T?;
        if (value is double) return value.toInt() as T?;
      } else if (T == bool) {
        if (value is String) {
          return (value.toLowerCase() == 'true' || value == '1') as T?;
        }
        if (value is int) return (value == 1) as T?;
      } else if (T == String) {
        return value.toString() as T?;
      }
      
      return null;
    }

    return PlatformSetting(
      key: _safeGet<int>('key') ?? 0,
      desc: _safeGet<String>('desc') ?? '',
      value: _safeGet<bool>('value') ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'desc': desc,
      'value': value,
    };
  }
}