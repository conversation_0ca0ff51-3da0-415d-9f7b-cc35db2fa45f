import 'dart:math';

import 'package:json_annotation/json_annotation.dart';
part 'game_binding_info.g.dart';

@JsonSerializable()
class GameBindingInfo {
  @J<PERSON><PERSON>ey(name: "app_ticket")
  final String appTicket;
  @<PERSON><PERSON><PERSON><PERSON>(name: "appid")
  final String appId;
  @<PERSON><PERSON><PERSON><PERSON>(name: "app_pid")
  final String appPid;
  @<PERSON><PERSON><PERSON><PERSON>(name: "app_gid")
  final String appGid;
  @<PERSON>son<PERSON>ey(name: "trace_id")
  final String traceId;

  GameBindingInfo({
    required this.appTicket,
    required this.appId,
    required this.appPid,
    required this.appGid,
    required this.traceId,
  });

  static String generateTraceId() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    final random = Random();
    return String.fromCharCodes(Iterable.generate(
        16, (_) => chars.codeUnitAt(random.nextInt(chars.length))));
  }

  factory GameBindingInfo.fromJson(Map<String, dynamic> json) => _$GameBindingInfoFromJson(json);

  Map<String, dynamic> toJson() => _$GameBindingInfoToJson(this);
}
