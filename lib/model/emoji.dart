/// 表情包
class Emoji {
  final int id;
  final String category;
  final String url;
  final String code;
  final int order;
  final String createdAt;
  final String updatedAt;
  final int? tgid;

  Emoji({
    required this.id,
    required this.category,
    required this.url,
    required this.code,
    required this.order,
    required this.createdAt,
    required this.updatedAt,
    this.tgid,
  });

  factory Emoji.fromJson(Map<String, dynamic> json) {
    return Emoji(
      id: json['id'] is String ? int.tryParse(json['id']) ?? 0 : (json['id'] ?? 0),
      category: json['category'] ?? '',
      url: json['url'] ?? '',
      code: json['code'] ?? '',
      order: json['order'] is String ? int.tryParse(json['order']) ?? 0 : (json['order'] ?? 0),
      createdAt: json['createdAt'] ?? '',
      updatedAt: json['updatedAt'] ?? '',
      tgid: json['tgid'] is String ? int.tryParse(json['tgid']) : json['tgid'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category': category,
      'url': url,
      'code': code,
      'order': order,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'tgid': tgid,
    };
  }
}