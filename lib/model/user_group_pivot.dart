/// 用户组关联信息
class UserGroupPivot {
  final int userId;
  final int groupId;
  final String? expirationTime;

  UserGroupPivot({
    required this.userId,
    required this.groupId,
    this.expirationTime,
  });

  factory UserGroupPivot.fromJson(Map<String, dynamic> json) {
    return UserGroupPivot(
      userId: json['userId'] ?? 0,
      groupId: json['groupId'] ?? 0,
      expirationTime: json['expirationTime'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'groupId': groupId,
      'expirationTime': expirationTime,
    };
  }
}