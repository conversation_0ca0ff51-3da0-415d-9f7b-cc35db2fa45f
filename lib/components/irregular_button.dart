import 'package:flutter/material.dart';
import '../common/dl_color.dart';

class IrregularButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final Color textColor;
  final Color backgroundColor;
  final Color foregroundColor;
  final Color borderColor;
  final double borderWidth;
  final double borderRadius;
  final double height;

  const IrregularButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.textColor = DLColor.textSecondary,
    this.backgroundColor = Colors.white,
    this.foregroundColor = Colors.black,
    this.borderColor = Colors.grey,
    this.borderWidth = 0.2,
    this.borderRadius = 6.0,
    this.height = 44.0,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: height,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
            backgroundColor: backgroundColor,
            foregroundColor: foregroundColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(6),
            ),
            padding: EdgeInsets.zero,
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            side: BorderSide.none,
        ),
        onPressed: onPressed,
        child: Text(
          text,
          style: TextStyle(
            color: textColor,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}