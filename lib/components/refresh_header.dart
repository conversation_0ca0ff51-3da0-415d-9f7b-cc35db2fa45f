import 'dart:math' as math;

import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class CustomerHeader extends Header {
  final Key? key;
  final MainAxisAlignment mainAxisAlignment;
  final Color? backgroundColor;
  final BoxDecoration? boxDecoration;
  final String? dragText;
  final String? armedText;
  final String? readyText;
  final String? processingText;
  final String? processedText;
  final String? noMoreText;
  final String? failedText;
  final bool showText;
  final double iconDimension;
  final double spacing;
  final Widget? succeededIcon;
  final Widget? failedIcon;
  final Widget? noMoreIcon;
  final CIPullIconBuilder? pullIconBuilder;
  final TextStyle? textStyle;
  final CITextBuilder? textBuilder;
  final Clip clipBehavior;
  final IconThemeData? iconTheme;

  const CustomerHeader({
    this.key,
    super.triggerOffset = 70,
    super.clamping = false,
    super.position,
    super.processedDuration,
    super.spring,
    super.readySpringBuilder,
    super.springRebound,
    super.frictionFactor,
    super.safeArea,
    super.infiniteOffset,
    super.hitOver,
    super.infiniteHitOver,
    super.hapticFeedback,
    super.triggerWhenReach,
    super.triggerWhenRelease,
    super.maxOverOffset,
    this.mainAxisAlignment = MainAxisAlignment.center,
    this.backgroundColor,
    this.boxDecoration,
    this.dragText,
    this.armedText,
    this.readyText,
    this.processingText,
    this.processedText,
    this.noMoreText,
    this.failedText,
    this.showText = true,
    this.iconDimension = 24,
    this.spacing = 8,
    this.succeededIcon,
    this.failedIcon,
    this.noMoreIcon,
    this.pullIconBuilder,
    this.textStyle,
    this.textBuilder,
    this.clipBehavior = Clip.hardEdge,
    this.iconTheme,
  });

  @override
  Widget build(BuildContext context, IndicatorState state) {
    return _ClassicIndicator(
      key: key,
      state: state,
      backgroundColor: backgroundColor,
      boxDecoration: boxDecoration,
      mainAxisAlignment: mainAxisAlignment,
      dragText: dragText ?? '下拉刷新',
      armedText: armedText ?? '释放刷新',
      readyText: readyText ?? '刷新中',
      processingText: processingText ?? '刷新中',
      processedText: processedText ?? '刷新成功',
      noMoreText: noMoreText ?? 'No more',
      failedText: failedText ?? 'Failed',
      showText: showText,
      iconDimension: iconDimension,
      spacing: spacing,
      reverse: state.reverse,
      succeededIcon: succeededIcon,
      failedIcon: failedIcon,
      noMoreIcon: noMoreIcon,
      pullIconBuilder: pullIconBuilder,
      textStyle: textStyle,
      textBuilder: textBuilder,
      clipBehavior: clipBehavior,
      iconTheme: iconTheme,
    );
  }
}

typedef CIPullIconBuilder = Widget Function(
    BuildContext context, IndicatorState state, double animation);

typedef CITextBuilder = Widget Function(
    BuildContext context, IndicatorState state, String text);

class _ClassicIndicator extends StatefulWidget {
  final IndicatorState state;
  final MainAxisAlignment mainAxisAlignment;
  final Color? backgroundColor;
  final BoxDecoration? boxDecoration;
  final String dragText;
  final String armedText;
  final String readyText;
  final String processingText;
  final String processedText;
  final String noMoreText;
  final String failedText;
  final bool showText;
  final double iconDimension;
  final double spacing;
  final bool reverse;
  final Widget? succeededIcon;
  final Widget? failedIcon;
  final Widget? noMoreIcon;
  final CIPullIconBuilder? pullIconBuilder;
  final TextStyle? textStyle;
  final CITextBuilder? textBuilder;
  final Clip clipBehavior;
  final IconThemeData? iconTheme;

  const _ClassicIndicator({
    super.key,
    required this.state,
    required this.mainAxisAlignment,
    this.backgroundColor,
    this.boxDecoration,
    required this.dragText,
    required this.armedText,
    required this.readyText,
    required this.processingText,
    required this.processedText,
    required this.noMoreText,
    required this.failedText,
    this.showText = true,
    required this.reverse,
    this.iconDimension = 24,
    this.spacing = 8,
    this.succeededIcon,
    this.failedIcon,
    this.noMoreIcon,
    this.pullIconBuilder,
    this.textStyle,
    this.textBuilder,
    this.clipBehavior = Clip.hardEdge,
    this.iconTheme,
  }) : assert(
  mainAxisAlignment == MainAxisAlignment.start ||
      mainAxisAlignment == MainAxisAlignment.center ||
      mainAxisAlignment == MainAxisAlignment.end,
  'Only supports [MainAxisAlignment.center], [MainAxisAlignment.start] and [MainAxisAlignment.end].');

  @override
  State<_ClassicIndicator> createState() => _ClassicIndicatorState();
}

class _ClassicIndicatorState extends State<_ClassicIndicator>
    with TickerProviderStateMixin<_ClassicIndicator> {
  late GlobalKey _iconAnimatedSwitcherKey;
  late AnimationController _iconAnimationController;

  MainAxisAlignment get _mainAxisAlignment => widget.mainAxisAlignment;
  Axis get _axis => widget.state.axis;
  double get _offset => widget.state.offset;
  double get _actualTriggerOffset => widget.state.actualTriggerOffset;
  double get _triggerOffset => widget.state.triggerOffset;
  double get _safeOffset => widget.state.safeOffset;
  IndicatorMode get _mode => widget.state.mode;
  IndicatorResult get _result => widget.state.result;

  @override
  void initState() {
    super.initState();
    _iconAnimatedSwitcherKey = GlobalKey();
    _iconAnimationController = AnimationController(
      value: 0,
      vsync: this,
      duration: const Duration(microseconds: 200),
    );
    _iconAnimationController.addListener(() => setState(() {}));
  }

  @override
  void didUpdateWidget(_ClassicIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.state.mode == IndicatorMode.armed &&
        oldWidget.state.mode == IndicatorMode.drag) {
      _iconAnimationController.animateTo(1,
          duration: const Duration(milliseconds: 200));
    } else if (widget.state.mode == IndicatorMode.drag &&
        oldWidget.state.mode == IndicatorMode.armed) {
      _iconAnimationController.animateBack(0,
          duration: const Duration(milliseconds: 200));
    } else if ((widget.state.mode == IndicatorMode.processing ||
               widget.state.mode == IndicatorMode.ready) &&
        oldWidget.state.mode != IndicatorMode.processing &&
        oldWidget.state.mode != IndicatorMode.ready) {
      _iconAnimationController.repeat(
          period: const Duration(milliseconds: 1000));
    } else if (widget.state.mode != IndicatorMode.processing &&
        widget.state.mode != IndicatorMode.ready &&
        (oldWidget.state.mode == IndicatorMode.processing ||
         oldWidget.state.mode == IndicatorMode.ready)) {
      _iconAnimationController.stop();
      _iconAnimationController.reset();
    }
  }

  @override
  void dispose() {
    super.dispose();
    _iconAnimationController.dispose();
  }

  String get _currentText {
    if (_result == IndicatorResult.noMore) {
      return widget.noMoreText;
    }
    switch (_mode) {
      case IndicatorMode.drag:
        return widget.dragText;
      case IndicatorMode.armed:
        return widget.armedText;
      case IndicatorMode.ready:
        return widget.readyText;
      case IndicatorMode.processing:
        return widget.processingText;
      case IndicatorMode.processed:
      case IndicatorMode.done:
        if (_result == IndicatorResult.fail) {
          return widget.failedText;
        } else {
          return widget.processedText;
        }
      default:
        return widget.dragText;
    }
  }

  Widget _buildIcon() {
    if (widget.pullIconBuilder != null) {
      return widget.pullIconBuilder!
          .call(context, widget.state, _iconAnimationController.value);
    }
    
    double rotationAngle = 0;
    
    if (_mode == IndicatorMode.processing || _mode == IndicatorMode.ready) {
      // 刷新中持续旋转
      rotationAngle = _iconAnimationController.value * 2 * math.pi;
    } else if (_mode == IndicatorMode.drag || _mode == IndicatorMode.armed) {
      // 拖拽状态根据进度旋转
      rotationAngle = _iconAnimationController.value * math.pi;
    }
    
    return SizedBox(
      width: widget.iconDimension,
      height: widget.iconDimension,
      child: Transform.rotate(
        angle: rotationAngle,
        child: Image.asset(
          'assets/images/element_icon.png',
          width: widget.iconDimension,
          height: widget.iconDimension,
        ),
      ),
    );
  }

  Widget _buildText() {
    return widget.textBuilder?.call(context, widget.state, _currentText) ??
        Text(
          _currentText,
          style: widget.textStyle ?? Theme.of(context).textTheme.titleMedium,
        );
  }

  Widget _buildVerticalWidget() {
    return Stack(
      clipBehavior: widget.clipBehavior,
      children: [
        if (_mainAxisAlignment == MainAxisAlignment.center)
          Positioned(
            left: 0,
            right: 0,
            top: _offset < _actualTriggerOffset
                ? -(_actualTriggerOffset -
                _offset +
                (widget.reverse ? _safeOffset : -_safeOffset)) /
                2
                : (!widget.reverse ? _safeOffset : 0),
            bottom: _offset < _actualTriggerOffset
                ? null
                : (widget.reverse ? _safeOffset : 0),
            height:
            _offset < _actualTriggerOffset ? _actualTriggerOffset : null,
            child: Center(
              child: _buildVerticalBody(),
            ),
          ),
        if (_mainAxisAlignment != MainAxisAlignment.center)
          Positioned(
            left: 0,
            right: 0,
            top: _mainAxisAlignment == MainAxisAlignment.start
                ? (!widget.reverse ? _safeOffset : 0)
                : null,
            bottom: _mainAxisAlignment == MainAxisAlignment.end
                ? (widget.reverse ? _safeOffset : 0)
                : null,
            child: _buildVerticalBody(),
          ),
      ],
    );
  }

  Widget _buildVerticalBody() {
    return Container(
      alignment: Alignment.center,
      height: _triggerOffset,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            alignment: Alignment.center,
            width: widget.iconDimension,
            height: widget.iconDimension,
            child: _buildIcon(),
          ),
          if (widget.showText)
            Container(
              margin: EdgeInsets.only(top: widget.spacing),
              child: _buildText(),
            ),
        ],
      ),
    );
  }

  Widget _buildHorizontalWidget() {
    return Stack(
      clipBehavior: widget.clipBehavior,
      children: [
        if (_mainAxisAlignment == MainAxisAlignment.center)
          Positioned(
            left: _offset < _actualTriggerOffset
                ? -(_actualTriggerOffset -
                _offset +
                (widget.reverse ? _safeOffset : -_safeOffset)) /
                2
                : (!widget.reverse ? _safeOffset : 0),
            right: _offset < _actualTriggerOffset
                ? null
                : (widget.reverse ? _safeOffset : 0),
            top: 0,
            bottom: 0,
            width: _offset < _actualTriggerOffset ? _actualTriggerOffset : null,
            child: Center(
              child: _buildHorizontalBody(),
            ),
          ),
        if (_mainAxisAlignment != MainAxisAlignment.center)
          Positioned(
            left: _mainAxisAlignment == MainAxisAlignment.start
                ? (!widget.reverse ? _safeOffset : 0)
                : null,
            right: _mainAxisAlignment == MainAxisAlignment.end
                ? (widget.reverse ? _safeOffset : 0)
                : null,
            top: 0,
            bottom: 0,
            child: _buildHorizontalBody(),
          ),
      ],
    );
  }

  Widget _buildHorizontalBody() {
    return Container(
      alignment: Alignment.center,
      width: _triggerOffset,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (widget.showText)
            Container(
              margin: EdgeInsets.only(bottom: widget.spacing),
              child: RotatedBox(
                quarterTurns: -1,
                child: _buildText(),
              ),
            ),
          Container(
            alignment: Alignment.center,
            height: widget.iconDimension,
            child: _buildIcon(),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    double offset = _offset;
    if (widget.state.indicator.infiniteOffset != null &&
        widget.state.indicator.position == IndicatorPosition.locator &&
        (_mode != IndicatorMode.inactive ||
            _result == IndicatorResult.noMore)) {
      offset = _actualTriggerOffset;
    }
    return Container(
      color: widget.boxDecoration == null ? widget.backgroundColor : null,
      decoration: widget.boxDecoration,
      width: _axis == Axis.vertical ? double.infinity : offset,
      height: _axis == Axis.horizontal ? double.infinity : offset,
      child: _axis == Axis.vertical
          ? _buildVerticalWidget()
          : _buildHorizontalWidget(),
    );
  }
}