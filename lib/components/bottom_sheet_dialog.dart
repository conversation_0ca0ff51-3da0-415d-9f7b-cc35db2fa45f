import 'package:flutter/material.dart';

class BottomSheetDialog extends StatelessWidget {
  final String title;
  final List<GameItem> gameItems;
  final VoidCallback? onClose;

  const BottomSheetDialog({
    super.key,
    required this.title,
    required this.gameItems,
    this.onClose,
  });

  // 显示底部弹窗的静态方法
  static void show({
    required BuildContext context,
    required String title,
    required List<GameItem> gameItems,
    VoidCallback? onClose,
  }) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => BottomSheetDialog(
        title: title,
        gameItems: gameItems,
        onClose: onClose,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.75,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // 顶部标题栏
          _buildHeader(context),
          
          // 游戏选择列表
          Expanded(
            child: _buildGameList(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 20, 20, 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // 居中标题
          Center(
            child: Text(
              '选择游戏圈',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
          // 右侧关闭按钮
          Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            child: GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Container(
                padding: const EdgeInsets.all(8),
                child: const Center(
                  child: Icon(
                    Icons.close,
                    size: 20,
                    color: Colors.grey,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGameList() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 16,
          mainAxisSpacing: 6,
          childAspectRatio: 0.9,
        ),
        itemCount: gameItems.length,
        itemBuilder: (context, index) {
          return _buildGameItem(gameItems[index], context);
        },
      ),
    );
  }

  Widget _buildGameItem(GameItem gameItem, BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 处理游戏选择
        print('选择了游戏: ${gameItem.name}');
        Navigator.pop(context);
        if (onClose != null) {
          onClose!();
        }
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 游戏图标
          Icon(
            gameItem.icon,
            color: gameItem.color,
            size: 40,
          ),
          const SizedBox(height: 8),
          
          // 游戏名称
          Text(
            gameItem.name,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}

// 游戏项目数据模型
class GameItem {
  final String name;
  final IconData icon;
  final Color color;

  const GameItem({
    required this.name,
    required this.icon,
    required this.color,
  });
}

// 便捷显示方法
class BottomSheetHelper {
  static void showGameSelectionDialog(
    BuildContext context, {
    required String title,
    required List<GameItem> gameItems,
    VoidCallback? onClose,
  }) {
    BottomSheetDialog.show(
      context: context,
      title: title,
      gameItems: gameItems,
      onClose: onClose,
    );
  }
} 