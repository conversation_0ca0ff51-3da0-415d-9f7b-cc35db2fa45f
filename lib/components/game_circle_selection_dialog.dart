import 'package:flutter/material.dart';
import '../model/game_circle.dart';

class GameCircleSelectionDialog extends StatelessWidget {
  final String title;
  final List<GameCircle> gameCircles;
  final Function(GameCircle)? onCircleSelected;
  final VoidCallback? onClose;

  const GameCircleSelectionDialog({
    super.key,
    required this.title,
    required this.gameCircles,
    this.onCircleSelected,
    this.onClose,
  });

  // 显示底部弹窗的静态方法
  static void show({
    required BuildContext context,
    required String title,
    required List<GameCircle> gameCircles,
    Function(GameCircle)? onCircleSelected,
    VoidCallback? onClose,
  }) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => GameCircleSelectionDialog(
        title: title,
        gameCircles: gameCircles,
        onCircleSelected: onCircleSelected,
        onClose: onClose,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // 动态计算高度，最小50%，最大80%
    final screenHeight = MediaQuery.of(context).size.height;
    final minHeight = screenHeight * 0.5;
    final maxHeight = screenHeight * 0.8;
    
    return Container(
      constraints: BoxConstraints(
        minHeight: minHeight,
        maxHeight: maxHeight,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 顶部标题栏
          _buildHeader(context),
          
          // 游戏圈子选择列表
          Flexible(
            child: _buildGameCircleList(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 20, 20, 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // 居中标题
          Center(
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),

        ],
      ),
    );
  }

  Widget _buildGameCircleList() {
    if (gameCircles.isEmpty) {
      return Container(
        color: Colors.white,
        child: const Center(
          child: Text(
            '暂无可选择的游戏圈',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ),
      );
    }

    return Container(
      color: Colors.white,
      child: SingleChildScrollView(
        padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
        child: GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 0.85,
          ),
          itemCount: gameCircles.length,
          itemBuilder: (context, index) {
            return _buildGameCircleItem(gameCircles[index], context);
          },
        ),
      ),
    );
  }

  Widget _buildGameCircleItem(GameCircle gameCircle, BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 处理游戏圈选择
        print('选择了游戏圈: ${gameCircle.displayName}');
        Navigator.pop(context);
        if (onCircleSelected != null) {
          onCircleSelected!(gameCircle);
        }
        if (onClose != null) {
          onClose!();
        }
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 游戏圈图标
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.grey.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.network(
                gameCircle.circleIcon,
                width: 80,
                height: 80,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.grey.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.gamepad,
                      color: Colors.grey,
                      size: 40,
                    ),
                  );
                },
              ),
            ),
          ),
          const SizedBox(height: 8),
          
          // 游戏圈名称
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              child: Text(
                gameCircle.displayName,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// 便捷显示方法
class GameCircleSelectionHelper {
  static void showGameCircleSelectionDialog(
    BuildContext context, {
    required String title,
    required List<GameCircle> gameCircles,
    Function(GameCircle)? onCircleSelected,
    VoidCallback? onClose,
  }) {
    GameCircleSelectionDialog.show(
      context: context,
      title: title,
      gameCircles: gameCircles,
      onCircleSelected: onCircleSelected,
      onClose: onClose,
    );
  }
}