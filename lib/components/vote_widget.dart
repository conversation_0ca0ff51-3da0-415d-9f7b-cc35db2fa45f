import 'package:flutter/material.dart';
import '../model/forum_post_list.dart';
import '../model/vote_response.dart';
import '../net/api/forum_service.dart';
import '../net/config/http_base_config.dart';
import '../common/dl_color.dart';
import '../utils/log_util.dart';

enum VoteDisplayType {
  detail, // 详情页：显示所有选项
  list,   // 列表页：只显示前两个选项
}

class VoteWidget extends StatefulWidget {
  final ForumPost post;
  final VoteDisplayType displayType;
  final ValueChanged<ForumPost>? onVoteSuccess; // 投票成功回调

  const VoteWidget({
    super.key,
    required this.post,
    required this.displayType,
    this.onVoteSuccess,
  });

  @override
  State<VoteWidget> createState() => _VoteWidgetState();
}

class _VoteWidgetState extends State<VoteWidget> {
  final ForumService _forumService = ForumService();
  final Set<int> _selectedVoteOptions = {};
  bool _isVoting = false;
  VoteResponse? _voteResult;

  @override
  Widget build(BuildContext context) {
    // 检查投票数据是否存在
    if (widget.post.vote == null) {
      return const SizedBox.shrink();
    }

    try {
      // 使用投票结果数据（如果存在），否则使用原始帖子数据
      final Map<String, dynamic> voteData;
      final Map<String, dynamic> voteConfig;
      final int voteUserCount;
      final int alreadyVoted;
      
      if (_voteResult != null) {
        // 使用投票后返回的最新数据
        voteData = _voteResult!.toJson();
        voteConfig = voteData['voteConfig'] as Map<String, dynamic>;
        voteUserCount = _voteResult!.voteUserCount;
        alreadyVoted = _voteResult!.alreadyVoted;
      } else {
        // 使用原始帖子数据
        voteData = widget.post.vote as Map<String, dynamic>;
        voteConfig = voteData['voteConfig'] as Map<String, dynamic>? ?? {};
        voteUserCount = voteData['voteUserCount'] ?? 0;
        alreadyVoted = voteData['alreadyVoted'] ?? 0;
      }

      if (voteConfig.isEmpty) {
        return const SizedBox.shrink();
      }

      final String title = voteConfig['title'] ?? '投票';
      final List<dynamic> voteItemsDetail = voteConfig['vote_items_detail'] ?? [];
      final String voteState = voteConfig['vote_state'] ?? '';
      final String startVoteTime = voteConfig['start_vote_time'] ?? '';
      final int voteType = voteConfig['type'] ?? 1; // 1=单选, 2=多选
      final int maxChoice = voteConfig['max_choice'] ?? 1;
      
      // 计算总票数
      int totalVotes = 0;
      for (var item in voteItemsDetail) {
        totalVotes += (item['number'] ?? 0) as int;
      }

      // 根据显示类型决定显示的选项数量
      final displayItems = widget.displayType == VoteDisplayType.list 
          ? voteItemsDetail.take(2).toList() 
          : voteItemsDetail;

      return Padding(
        padding: widget.displayType == VoteDisplayType.list 
            ? const EdgeInsets.fromLTRB(0, 0, 0, 0)
            : const EdgeInsets.fromLTRB(16, 0, 16, 0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[200]!, width: 1),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 投票标题和类型
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          '$title（${voteType == 1 ? '单选' : '多选，最多${maxChoice}个'}）',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                            height: 1.3,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // 参与人数和选择状态提示
                  Row(
                    children: [
                      // 投票状态文本
                      if (_getVoteStatusText(voteState, alreadyVoted).isNotEmpty) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: DLColor.divider,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            _getVoteStatusText(voteState, alreadyVoted),
                            style: const TextStyle(
                              fontSize: 10,
                              color: DLColor.textThird,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                      ],
                      // 未开始状态额外显示开始时间
                      if (voteState == '未开始' && startVoteTime.isNotEmpty) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: DLColor.divider,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            '${startVoteTime}开始投票',
                            style: const TextStyle(
                              fontSize: 10,
                              color: DLColor.textThird,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                      ],
                      // 参与人数 - 未开始状态时隐藏
                      if (voteState != '未开始') ...[
                        Text(
                          '${voteUserCount}人参与',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                      if (voteType == 2 && _selectedVoteOptions.isNotEmpty) ...[
                        const Spacer(),
                        Text(
                          '已选择${_selectedVoteOptions.length}/${maxChoice}个',
                          style: TextStyle(
                            fontSize: 12,
                            color: DLColor.blue,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ],
                  ),

                  const SizedBox(height: 8),

                  // 投票选项
                  if (displayItems.isNotEmpty) ...[
                    ...displayItems.asMap().entries.map((entry) {
                      final int index = entry.key;
                      final item = entry.value;
                      final String content = item['content'] ?? '';
                      final int number = item['number'] ?? 0;
                      final int? checked = item['checked'];
                      final bool isSelected = _selectedVoteOptions.contains(index);
                      final bool isChecked = checked == 1;
                      
                      // 如果已投票，显示结果样式
                      if (alreadyVoted == 1) {
                        // 计算百分比
                        double percentage = totalVotes > 0 ? (number / totalVotes) * 100 : 0;
                        
                        return Container(
                          width: double.infinity,
                          margin: const EdgeInsets.only(bottom: 8),
                          decoration: BoxDecoration(
                            color: isChecked ? DLColor.blue : Colors.white,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            children: [
                              // 选项内容区域
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                                child: Row(
                                  children: [
                                    // 选项内容
                                    Expanded(
                                      child: Row(
                                        children: [
                                          Flexible(
                                            child: Text(
                                              content,
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Colors.black87,
                                                fontWeight: isChecked ? FontWeight.w500 : FontWeight.normal,
                                              ),
                                              maxLines: widget.displayType == VoteDisplayType.list ? 1 : null,
                                              overflow: widget.displayType == VoteDisplayType.list ? TextOverflow.ellipsis : null,
                                            ),
                                          ),
                                          if (isChecked) ...[
                                            const SizedBox(width: 4),
                                            Image.asset(
                                              'assets/images/select_icon.png',
                                              width: 16,
                                              height: 16,
                                            ),
                                          ],
                                        ],
                                      ),
                                    ),
                                    // 百分比和票数
                                    Text(
                                      '${percentage.toStringAsFixed(1)}% ($number票)',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              // 进度条（只有当总票数大于0时才显示）
                              if (totalVotes > 0)
                                Container(
                                  height: 3,
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                    borderRadius: const BorderRadius.only(
                                      bottomLeft: Radius.circular(8),
                                      bottomRight: Radius.circular(8),
                                    ),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: const BorderRadius.only(
                                      bottomLeft: Radius.circular(8),
                                      bottomRight: Radius.circular(8),
                                    ),
                                    child: LayoutBuilder(
                                      builder: (context, constraints) {
                                        // 计算进度条宽度：百分比 * 容器宽度
                                        double progressWidth = (percentage / 100) * constraints.maxWidth;
                                        return Stack(
                                          children: [
                                            // 背景
                                            Container(
                                              width: double.infinity,
                                              height: 3,
                                              color: Colors.grey[200],
                                            ),
                                            // 进度
                                            Container(
                                              width: progressWidth,
                                              height: 3,
                                              color: DLColor.primary,
                                            ),
                                          ],
                                        );
                                      },
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        );
                      } else {
                        // 未投票，显示可选择样式
                        return GestureDetector(
                          onTap: (alreadyVoted == 1 || widget.displayType == VoteDisplayType.list || voteState == '未开始' || voteState == '已结束')
                              ? null // 已投票时、列表页时、未开始时或已结束时禁用点击
                              : () => _handleVoteOptionTap(index, voteType, maxChoice),
                          child: Container(
                            width: double.infinity,
                            margin: const EdgeInsets.only(bottom: 8),
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                            decoration: BoxDecoration(
                              color: isSelected ? DLColor.blue : Colors.white,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                // 选项内容
                                Expanded(
                                  child: Row(
                                    children: [
                                      Flexible(
                                        child: Text(
                                          content,
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: Colors.black87,
                                            fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                                          ),
                                          maxLines: widget.displayType == VoteDisplayType.list ? 1 : null,
                                          overflow: widget.displayType == VoteDisplayType.list ? TextOverflow.ellipsis : null,
                                        ),
                                      ),
                                      // 选中图标紧挨着内容
                                      if (isSelected) ...[
                                        const SizedBox(width: 4),
                                        Image.asset(
                                          'assets/images/select_icon.png',
                                          width: 16,
                                          height: 16,
                                        ),
                                      ],
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }
                    }).toList(),
                    
                    // 列表页显示"更多选项"提示
                    if (widget.displayType == VoteDisplayType.list && voteItemsDetail.length > 2)
                      Container(
                        width: double.infinity,
                        margin: const EdgeInsets.only(bottom: 0),
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        child: Center(
                          child: Text(
                            '展开选项(${voteItemsDetail.length - 2}) >',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ),
                      ),
                  ],

                  // 投票按钮（只在详情页显示）
                  if (widget.displayType == VoteDisplayType.detail) ...[
                    const SizedBox(height: 12),
                    Center(
                      child: ElevatedButton(
                        onPressed: (alreadyVoted == 1 || voteState == '未开始' || voteState == '已结束')
                            ? null // 已投票时、未开始时或已结束时禁用按钮
                            : (_selectedVoteOptions.isEmpty || _isVoting ? null : _submitVote),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: (alreadyVoted == 1 || voteState == '未开始' || voteState == '已结束')
                              ? Colors.grey[400] // 已投票时、未开始时或已结束时灰色背景
                              : DLColor.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: voteState == '未开始'
                            ? const Text(
                                '未开始',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              )
                            : voteState == '已结束'
                                ? const Text(
                                    '已结束',
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  )
                                : alreadyVoted == 1
                                    ? const Text(
                                        '已投票',
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      )
                                    : _isVoting
                                        ? const SizedBox(
                                            height: 20,
                                            width: 20,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                            ),
                                          )
                                        : Text(
                                            '投票${_selectedVoteOptions.isNotEmpty ? '(${_selectedVoteOptions.length})' : ''}',
                                            style: const TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      );
    } catch (e) {
      // 如果解析投票数据失败，返回空组件
      LogUtil.e('解析投票数据失败: $e', tag: 'VoteWidget');
      return const SizedBox.shrink();
    }
  }

  /// 获取投票状态文本
  String _getVoteStatusText(String voteState, int alreadyVoted) {
    // 如果vote_state非已结束，且alreadyVoted为1时，显示已投票
    if (voteState != '已结束' && alreadyVoted == 1) {
      return '已投票';
    }
    // 如果vote_state为已结束、未开始，显示vote_state的内容
    if (voteState == '已结束' || voteState == '未开始') {
      return voteState;
    }
    // 如果vote_state为进行中，且alreadyVoted为0，则不显示text
    if (voteState == '进行中' && alreadyVoted == 0) {
      return '';
    }
    return '';
  }

  // 处理投票选项选择
  void _handleVoteOptionTap(int optionIndex, int voteType, int maxChoice) {
    setState(() {
      if (voteType == 1) {
        // 单选：清除所有选择，只选择当前选项
        _selectedVoteOptions.clear();
        _selectedVoteOptions.add(optionIndex);
      } else {
        // 多选：根据maxChoice限制选择数量
        if (_selectedVoteOptions.contains(optionIndex)) {
          // 如果已选中，则取消选择
          _selectedVoteOptions.remove(optionIndex);
        } else {
          // 如果未选中，检查是否超过最大选择数量
          if (_selectedVoteOptions.length < maxChoice) {
            _selectedVoteOptions.add(optionIndex);
          } else {
            // 如果已达到最大选择数量，提示用户
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('最多只能选择${maxChoice}个')),
            );
          }
        }
      }
    });
  }

  // 提交投票
  Future<void> _submitVote() async {
    if (_selectedVoteOptions.isEmpty) {
      // 显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请选择投票选项')),
      );
      return;
    }

    if (widget.post.voteId == null) return;

    setState(() {
      _isVoting = true;
    });

    try {
      final voteData = widget.post.vote as Map<String, dynamic>;
      final voteConfig = voteData['voteConfig'] as Map<String, dynamic>;
      final List<dynamic> voteItemsDetail = voteConfig['vote_items_detail'] ?? [];
      
      // 构建选中项目的ID列表
      final selectedIds = <int>[];
      for (int index in _selectedVoteOptions) {
        if (index < voteItemsDetail.length) {
          final item = voteItemsDetail[index];
          selectedIds.add(item['id'] ?? 0);
        }
      }

      // 格式化items参数为字符串 [id1,id2]
      final itemsStr = selectedIds.toString();

      final response = await _forumService.submitVote(
        baseUrl: HttpBaseConfig.forumBaseUrl,
        voteId: widget.post.voteId.toString(),
        items: itemsStr,
        context: context,
      );

      if (!mounted) return;

      if (response.code == 0 && response.data != null) {
        setState(() {
          _voteResult = response.data;
          _isVoting = false;
          _selectedVoteOptions.clear(); // 清空选择项，因为已经投票完成
        });
        
        // 调用投票成功回调
        if (widget.onVoteSuccess != null) {
          widget.onVoteSuccess!(widget.post);
        }
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('投票成功')),
        );
      } else {
        setState(() {
          _isVoting = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(response.message ?? '投票失败')),
        );
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isVoting = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('投票失败: $e')),
      );
      LogUtil.e('投票提交失败', error: e, tag: 'VoteWidget');
    }
  }
}