import 'md5_utils.dart';
import '../config/app_config.dart';

/// 签名工具类
class SignUtil {
  /// 生成签名
  /// 
  /// [params] 请求参数
  /// 返回 MD5 签名字符串
  static String generateSign(Map<String, dynamic> params) {
    // 移除sign参数（如果存在）
    final signParams = Map<String, dynamic>.from(params);
    signParams.remove('sign');
    
    // 按key排序
    final sortedKeys = signParams.keys.toList()..sort();
    
    // 构建签名字符串
    final signStringBuffer = StringBuffer();
    for (final key in sortedKeys) {
      final value = signParams[key];
      if (value != null && value.toString().isNotEmpty) {
        if (signStringBuffer.isNotEmpty) {
          signStringBuffer.write('&');
        }
        signStringBuffer.write('$key=$value');
      }
    }
    
    // 添加密钥（从配置文件获取）
    final signKey = AppConfig.appKey;
    final signString = '${signStringBuffer.toString()}&key=$signKey';
    
    // 生成MD5签名
    return Md5Utils.generateMd5(signString);
  }
}