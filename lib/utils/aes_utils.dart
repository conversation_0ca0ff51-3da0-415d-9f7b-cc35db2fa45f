import 'package:encrypt/encrypt.dart';
import 'dart:convert';

class AesUtil {

  /// AES 加密
  static String encrypt(String plainText, String password) {
    // 处理password：不足16位补0，超过16位截取前16位
    String encodeKey = '';
    if (password.isNotEmpty) {
      int length = password.length;
      if (length < 16) {
        // 不足16位则补齐0
        encodeKey = password.padRight(16, '0');
      } else {
        // 满16位则截取前16位
        encodeKey = password.substring(0, 16);
      }
    }
    
    // 将处理后的encodeKey转换为UTF-8字节，然后创建Key
    final keyBytes = utf8.encode(encodeKey);
    final key = Key(keyBytes);
    
    // 使用ECB模式，无IV，PKCS7填充（与Java默认兼容）
    final encrypter = Encrypter(AES(
      key,
      mode: AESMode.ecb,
      padding: 'PKCS7',
    ));
    
    final encrypted = encrypter.encrypt(plainText);
    return encrypted.base64;
  }

  /// AES 解密
  static String decrypt(String encryptedBase64, String password) {
    try {
      // 处理password：不足16位补0，超过16位截取前16位
      String encodeKey = '';
      if (password.isNotEmpty) {
        int length = password.length;
        if (length < 16) {
          // 不足16位则补齐0
          encodeKey = password.padRight(16, '0');
        } else {
          // 满16位则截取前16位
          encodeKey = password.substring(0, 16);
        }
      }
      
      // 将处理后的encodeKey转换为UTF-8字节，然后创建Key
      final keyBytes = utf8.encode(encodeKey);
      final key = Key(keyBytes);
      
      // 使用ECB模式，无IV，PKCS7填充
      final encrypter = Encrypter(AES(
        key,
        mode: AESMode.ecb,
        padding: 'PKCS7',
      ));
      
      final encrypted = Encrypted.from64(encryptedBase64);
      return encrypter.decrypt(encrypted);
    } catch (e) {
      return "";
    }
  }
}
