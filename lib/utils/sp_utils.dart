import 'package:shared_preferences/shared_preferences.dart';

class SpManager {
  //单例生命
  factory SpManager() => _instance;

  SpManager._internal();

  static final SpManager _instance = SpManager._internal();

  //保持一个SharedPreferences的引用
  static late final SharedPreferences _sp;

  static bool _isInit = false;

  //初始化方法，只需要调用一次。
  static Future<SpManager> init() async {
    if (_isInit) {
      return _instance;
    }
    _isInit = true;
    _sp = await SharedPreferences.getInstance();
    return _instance;
  }

  static SpManager getInstance() {
    return _instance;
  }

  void put<T>(String key, T value) {
    if (value is String) {
      _sp.setString(key, value);
    } else if (value is int) {
      _sp.setInt(key, value);
    } else if (value is bool) {
      _sp.setBool(key, value);
    } else if (value is double) {
      _sp.setDouble(key, value);
    } else if (value is List<String>) {
      _sp.setStringList(key, value);
    } else {
      throw UnsupportedError("Unsupported type: ${value.runtimeType}");
    }
  }

  String? getString(String key, [String? defValue]) {
    try {
      return _sp.getString(key) ?? defValue;
    } catch (e) {
      return defValue;
    }
  }

  int? getInt(String key, [int? defValue]) {
    try {
      return _sp.getInt(key) ?? defValue;
    } catch (e) {
      return defValue;
    }
  }

  bool? getBool(String key, [bool? defValue]) {
    try {
      return _sp.getBool(key) ?? defValue;
    } catch (e) {
      return defValue;
    }
  }

  double? getDouble(String key, [double? defValue]) {
    try {
      return _sp.getDouble(key) ?? defValue;
    } catch (e) {
      return defValue;
    }
  }

  List<String>? getStringList(String key, [List<String>? defValue]) {
    try {
      return _sp.getStringList(key) ?? defValue;
    } catch (e) {
      return defValue;
    }
  }
}
