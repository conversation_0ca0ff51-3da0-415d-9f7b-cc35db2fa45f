import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:logging/logging.dart';

/// 日志级别
enum LogLevel {
  debug,
  info,
  warning,
  error,
}

/// 日志工具类
class LogUtil {
  static bool _isEnabled = kDebugMode; // 默认在Debug模式下启用
  static LogLevel _minLevel = LogLevel.debug;
  static String _tag = 'DLYZ';
  static late Logger _logger;
  static bool _isInitialized = false;

  /// 初始化日志系统
  static void init() {
    if (_isInitialized) return;
    
    _logger = Logger(_tag);
    
    // 配置日志级别
    Logger.root.level = Level.ALL;
    
    // 配置日志监听器，直接输出到系统日志
    Logger.root.onRecord.listen((record) {
      if (Platform.isAndroid) {
        // 在Android上使用系统日志
        developer.log(
          record.message,
          time: record.time,
          level: _convertToSystemLevel(record.level),
          name: record.loggerName,
          error: record.error,
          stackTrace: record.stackTrace,
        );
      } else {
        // 在其他平台使用标准输出
        print('${record.time} [${record.level.name}] ${record.loggerName}: ${record.message}');
      }
    });
    
    _isInitialized = true;
  }

  /// 转换日志级别到系统级别
  static int _convertToSystemLevel(Level level) {
    if (level.value >= Level.SEVERE.value) return 1000; // ERROR
    if (level.value >= Level.WARNING.value) return 900; // WARNING  
    if (level.value >= Level.INFO.value) return 800;    // INFO
    return 500; // DEBUG
  }

  /// 设置是否启用日志
  static void setEnabled(bool enabled) {
    _isEnabled = enabled;
  }

  /// 设置最低日志级别
  static void setMinLevel(LogLevel level) {
    _minLevel = level;
  }

  /// 设置全局标签
  static void setTag(String tag) {
    _tag = tag;
  }

  /// Debug日志
  static void d(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    if (!_isInitialized) init();
    _logWithLevel(Level.FINE, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Info日志
  static void i(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    if (!_isInitialized) init();
    _logWithLevel(Level.INFO, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Warning日志
  static void w(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    if (!_isInitialized) init();
    _logWithLevel(Level.WARNING, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Error日志
  static void e(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    if (!_isInitialized) init();
    _logWithLevel(Level.SEVERE, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// JSON数据日志
  static void json(String title, dynamic data, {String? tag, LogLevel level = LogLevel.debug}) {
    if (!_isInitialized) init();
    
    final buffer = StringBuffer();
    buffer.writeln('📄 $title');
    buffer.writeln('--- JSON Start ---');
    buffer.writeln(_formatData(data));
    buffer.writeln('--- JSON End ---');
    
    final logLevel = level == LogLevel.debug ? Level.FINE :
                     level == LogLevel.info ? Level.INFO :
                     level == LogLevel.warning ? Level.WARNING : Level.SEVERE;
    
    _logWithLevel(logLevel, buffer.toString(), tag: tag ?? '📄 JSON');
  }

  /// 使用指定级别记录日志
  static void _logWithLevel(Level level, String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    if (!_isEnabled) return;
    
    final actualTag = tag ?? _tag;
    final logger = Logger(actualTag);
    
    String fullMessage = message;
    if (error != null) {
      fullMessage += '\nError: $error';
    }
    if (stackTrace != null) {
      fullMessage += '\nStackTrace: $stackTrace';
    }
    
    // 分段输出长内容
    _logLongMessage(logger, level, fullMessage, actualTag, error, stackTrace);
  }

  /// 分段输出长消息
  static void _logLongMessage(Logger logger, Level level, String message, String tag, Object? error, StackTrace? stackTrace) {
    final int maxLogLength = 500; // Android logcat 限制约4KB，其他平台保守一些
    
    if (message.length <= maxLogLength) {
      // 消息不长，直接输出
      logger.log(level, message, error, stackTrace);
      return;
    }
    
    // 消息过长，需要分段输出
    final lines = message.split('\n');
    final buffer = StringBuffer();
    int partIndex = 1;
    int totalParts = (message.length / maxLogLength).ceil();
    
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];
      
      // 检查添加这一行是否会超出限制
      if (buffer.length + line.length + 1 > maxLogLength) {
        // 输出当前缓冲区内容
        if (buffer.isNotEmpty) {
          final partMessage = totalParts > 1 
              ? '[$tag] Part $partIndex/$totalParts:\n${buffer.toString()}'
              : buffer.toString();
          logger.log(level, partMessage, partIndex == 1 ? error : null, partIndex == 1 ? stackTrace : null);
          partIndex++;
          buffer.clear();
        }
      }
      
      buffer.writeln(line);
      
      // 如果单行就超过限制，需要进一步分割
      if (line.length > maxLogLength) {
        _logVeryLongLine(logger, level, line, tag, partIndex, totalParts);
        buffer.clear();
        partIndex++;
      }
    }
    
    // 输出剩余内容
    if (buffer.isNotEmpty) {
      final partMessage = totalParts > 1 
          ? '[$tag] Part $partIndex/$totalParts:\n${buffer.toString()}'
          : buffer.toString();
      logger.log(level, partMessage);
    }
  }
  
  /// 处理超长单行
  static void _logVeryLongLine(Logger logger, Level level, String line, String tag, int partIndex, int totalParts) {
    final int maxLogLength = Platform.isAndroid ? 4000 : 1000;
    
    for (int i = 0; i < line.length; i += maxLogLength) {
      final end = (i + maxLogLength < line.length) ? i + maxLogLength : line.length;
      final chunk = line.substring(i, end);
      final partMessage = '[$tag] Part $partIndex/$totalParts (chunk ${i ~/ maxLogLength + 1}):\n$chunk';
      logger.log(level, partMessage);
    }
  }

  /// 格式化数据输出
  static String _formatData(dynamic data) {
    try {
      if (data is String) {
        // 尝试解析为 JSON 并格式化
        try {
          final jsonData = jsonDecode(data);
          return _formatJson(jsonData);
        } catch (e) {
          // 不是 JSON，直接返回字符串
          return data;
        }
      } else if (data is Map || data is List) {
        // 直接格式化 Map 或 List
        return _formatJson(data);
      } else {
        return data.toString();
      }
    } catch (e) {
      return data.toString();
    }
  }

  /// 格式化 JSON 数据
  static String _formatJson(dynamic json) {
    try {
      const encoder = JsonEncoder.withIndent('  ');
      return encoder.convert(json);
    } catch (e) {
      return json.toString();
    }
  }

  /// 获取数据结构信息
  static String _getDataStructureInfo(dynamic data) {
    if (data is Map) {
      final keys = data.keys.take(5).toList(); // 只显示前5个key
      final keyInfo = keys.map((k) => '"$k"').join(', ');
      final moreInfo = data.length > 5 ? '... (${data.length} total keys)' : '';
      return 'Map with keys: [$keyInfo$moreInfo]';
    } else if (data is List) {
      final itemTypes = <String>[];
      for (int i = 0; i < data.length && i < 3; i++) {
        final item = data[i];
        if (item is Map) {
          itemTypes.add('Map(${item.length})');
        } else if (item is List) {
          itemTypes.add('List(${item.length})');
        } else {
          itemTypes.add(item.runtimeType.toString());
        }
      }
      final typeInfo = itemTypes.join(', ');
      final moreInfo = data.length > 3 ? '... (${data.length} total items)' : '';
      return 'List with items: [$typeInfo$moreInfo]';
    } else {
      return data.runtimeType.toString();
    }
  }

  /// 网络请求日志
  static void request(String url, {String method = 'GET', Map<String, dynamic>? params, Map<String, dynamic>? headers}) {
    if (!_isInitialized) init();
    if (!_isEnabled) return;
    
    final buffer = StringBuffer();
    buffer.writeln('🌐 HTTP Request');
    buffer.writeln('Method: $method');
    buffer.writeln('URL: $url');
    
    if (headers != null && headers.isNotEmpty) {
      buffer.writeln('Headers:');
      buffer.writeln(_formatJson(headers));
    }
    
    if (params != null && params.isNotEmpty) {
      buffer.writeln('Params:');
      buffer.writeln(_formatJson(params));
    }
    
    _logWithLevel(Level.FINE, buffer.toString(), tag: '📤 NETWORK');
  }

  /// 网络响应日志
  static void response(String url, int statusCode, dynamic data, {String method = 'GET', int? duration, int maxDataLength = 2000}) {
    if (!_isInitialized) init();
    if (!_isEnabled) return;
    
    final buffer = StringBuffer();
    buffer.writeln('🌐 HTTP Response');
    buffer.writeln('Method: $method');
    buffer.writeln('URL: $url');
    buffer.writeln('Status Code: $statusCode');
    
    if (duration != null) {
      buffer.writeln('Duration: ${duration}ms');
    }
    
    if (data != null) {
      buffer.writeln('Response Data:');
      buffer.writeln('--- Response Start ---');
      
      final formattedData = _formatData(data);
      if (formattedData.length > maxDataLength) {
        // 数据太长，截断并显示前一部分
        final truncatedData = formattedData.substring(0, maxDataLength);
        buffer.writeln(truncatedData);
        buffer.writeln('...');
        buffer.writeln('⚠️ Response data truncated (${formattedData.length} chars > $maxDataLength limit)');
        buffer.writeln('📊 Data length: ${formattedData.length} characters');
        
        // 如果是JSON数据，尝试显示结构信息
        if (data is Map || data is List) {
          buffer.writeln('📋 Data structure: ${_getDataStructureInfo(data)}');
        }
        
        // 提示可以使用完整日志方法
        buffer.writeln('💡 Use LogUtil.responseFull() to see complete data');
      } else {
        buffer.writeln(formattedData);
      }
      
      buffer.writeln('--- Response End ---');
    }
    
    _logWithLevel(Level.FINE, buffer.toString(), tag: '📥 NETWORK');
  }

  /// 网络响应完整日志（不截断数据）
  static void responseFull(String url, int statusCode, dynamic data, {String method = 'GET', int? duration}) {
    if (!_isInitialized) init();
    if (!_isEnabled) return;
    
    final buffer = StringBuffer();
    buffer.writeln('🌐 HTTP Response (Full Data)');
    buffer.writeln('Method: $method');
    buffer.writeln('URL: $url');
    buffer.writeln('Status Code: $statusCode');
    
    if (duration != null) {
      buffer.writeln('Duration: ${duration}ms');
    }
    
    if (data != null) {
      buffer.writeln('Response Data:');
      buffer.writeln('--- Response Start ---');
      buffer.writeln(_formatData(data));
      buffer.writeln('--- Response End ---');
    }
    
    _logWithLevel(Level.FINE, buffer.toString(), tag: '📥 NETWORK_FULL');
  }

  /// 页面生命周期日志
  static void lifecycle(String pageName, String lifecycle) {
    if (!_isInitialized) init();
    if (!_isEnabled) return;
    
    _logWithLevel(Level.FINE, '$pageName - $lifecycle', tag: '🔄 LIFECYCLE');
  }

  /// 用户行为日志
  static void action(String action, {Map<String, dynamic>? params}) {
    if (!_isInitialized) init();
    if (!_isEnabled) return;
    
    final message = params != null ? '$action: $params' : action;
    _logWithLevel(Level.INFO, message, tag: '👆 ACTION');
  }

  /// 性能日志
  static void performance(String operation, int duration) {
    if (!_isInitialized) init();
    if (!_isEnabled) return;
    
    final level = duration > 1000 ? Level.WARNING : Level.INFO;
    final emoji = duration > 1000 ? '🐌' : '⚡';
    _logWithLevel(level, '$operation took ${duration}ms', tag: '$emoji PERFORMANCE');
  }

  /// 代码块执行时间测量
  static Future<T> measureTime<T>(String operation, Future<T> Function() block) async {
    if (!_isInitialized) init();
    if (!_isEnabled) {
      return await block();
    }
    
    final stopwatch = Stopwatch()..start();
    try {
      final result = await block();
      stopwatch.stop();
      performance(operation, stopwatch.elapsedMilliseconds);
      return result;
    } catch (error) {
      stopwatch.stop();
      e(operation, error: error);
      rethrow;
    }
  }

  /// 同步代码块执行时间测量
  static T measureTimeSync<T>(String operation, T Function() block) {
    if (!_isInitialized) init();
    if (!_isEnabled) {
      return block();
    }
    
    final stopwatch = Stopwatch()..start();
    try {
      final result = block();
      stopwatch.stop();
      performance(operation, stopwatch.elapsedMilliseconds);
      return result;
    } catch (error) {
      stopwatch.stop();
      e('$operation failed after ${stopwatch.elapsedMilliseconds}ms', error: error);
      rethrow;
    }
  }
}

/*
使用示例：

// 普通响应日志（会自动截断长数据）
LogUtil.response(
  'https://api.example.com/data',
  200,
  responseData,
  method: 'POST',
  duration: 1500,
  maxDataLength: 2000, // 可选，默认2000字符
);

// 完整响应日志（不截断数据）
LogUtil.responseFull(
  'https://api.example.com/data',
  200,
  responseData,
  method: 'POST',
  duration: 1500,
);

// 在调试时临时查看完整数据
if (kDebugMode) {
  LogUtil.responseFull(
    'https://api.example.com/data',
    200,
    responseData,
    method: 'POST',
    duration: 1500,
  );
}
*/