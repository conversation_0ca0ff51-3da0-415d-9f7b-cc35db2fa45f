import 'package:flutter/cupertino.dart';

import '../model/forum_config.dart';
import '../model/platform_setting.dart';
import '../model/emoji.dart';
import '../model/act_module.dart';
import '../model/tgid_info.dart';
import '../model/forum_user.dart';
import '../net/api/forum_service.dart';
import '../net/config/http_base_config.dart';
import '../utils/log_util.dart';

/// 论坛信息全局管理类
class ForumInfo {
  static final ForumInfo _instance = ForumInfo._internal();
  factory ForumInfo() => _instance;
  ForumInfo._internal();

  ForumConfig? _forumConfig;
  bool _isLoading = false;
  bool _isInitialized = false;

  /// 获取论坛配置
  ForumConfig? get forumConfig => _forumConfig;

  /// 是否正在加载
  bool get isLoading => _isLoading;

  /// 是否已初始化
  bool get isInitialized => _isInitialized;

  /// 网站名称
  String get siteName => _forumConfig?.setSite.siteName ?? '';

  /// 网站URL
  String get siteUrl => _forumConfig?.setSite.siteUrl ?? '';

  /// 网站Logo
  String get siteLogo => _forumConfig?.setSite.siteLogo ?? '';

  /// 网站Favicon
  String get siteFavicon => _forumConfig?.setSite.siteFavicon ?? '';

  /// 用户背景图
  String get siteUserBackgroundImage => _forumConfig?.setSite.siteUserBackgroundImage ?? '';

  /// 表情包列表
  List<Emoji> get emojis => _forumConfig?.emojis ?? [];

  /// 活动模块列表
  List<ActModule> get actModules => _forumConfig?.actModule ?? [];

  /// Tgid信息列表
  List<TgidInfo> get tgidInfos => _forumConfig?.tgidInfo ?? [];

  /// 当前用户信息
  User? get currentUser => _forumConfig?.user;

  /// 初始化论坛配置
  Future<bool> initialize(BuildContext? context) async {
    if (_isLoading || _isInitialized) {
      return _isInitialized;
    }

    _isLoading = true;
    LogUtil.d('开始初始化论坛配置', tag: 'ForumInfo');

    try {
      final forumService = ForumService();
      final response = await forumService.getForumConfig(
        baseUrl: HttpBaseConfig.forumBaseUrl,
        context: context
      );

      if (response.code == 0 && response.data != null) {
        _forumConfig = response.data;
        _isInitialized = true;
        LogUtil.d('论坛配置初始化成功: ${siteName}', tag: 'ForumInfo');
        return true;
      } else {
        LogUtil.w('论坛配置初始化失败: ${response.message}', tag: 'ForumInfo');
        return false;
      }
    } catch (e, stackTrace) {
      LogUtil.e('论坛配置初始化异常', error: e, stackTrace: stackTrace, tag: 'ForumInfo');
      return false;
    } finally {
      _isLoading = false;
    }
  }

  /// 刷新论坛配置
  Future<bool> refresh(BuildContext context) async {
    _isInitialized = false;
    return await initialize(context);
  }

  /// 清除配置
  void clear() {
    _forumConfig = null;
    _isInitialized = false;
    _isLoading = false;
  }

  /// 检查功能是否开启
  bool isFunctionEnabled(String functionName, {int platform = 2}) {
    if (_forumConfig == null) return false;

    List<PlatformSetting>? settings;

    switch (functionName) {
      case 'rewards':
        settings = _forumConfig!.setSite.siteRewards;
        break;
      case 'areward':
        settings = _forumConfig!.setSite.siteAreward;
        break;
      case 'redpacket':
        settings = _forumConfig!.setSite.siteRedpacket;
        break;
      case 'anonymous':
        settings = _forumConfig!.setSite.siteAnonymous;
        break;
      case 'personalletter':
        settings = _forumConfig!.setSite.sitePersonalletter;
        break;
      case 'shop':
        settings = _forumConfig!.setSite.siteShop;
        break;
      case 'pay':
        settings = _forumConfig!.setSite.sitePay;
        break;
      case 'usergroup':
        settings = _forumConfig!.setSite.siteUsergroup;
        break;
      case 'recharges':
        settings = _forumConfig!.setSite.siteRecharges;
        break;
      case 'withdrawal':
        settings = _forumConfig!.setSite.siteWithdrawal;
        break;
      case 'comment':
        settings = _forumConfig!.setSite.siteComment;
        break;
      default:
        return false;
    }

    if (settings == null) return false;

    // 查找对应平台的设置 (1=PC端, 2=H5端, 3=小程序端)
    final platformSetting = settings.firstWhere(
          (setting) => setting.key == platform,
      orElse: () => PlatformSetting(key: platform, desc: '', value: false),
    );

    return platformSetting.value;
  }

  /// 获取用户权限
  bool hasPermission(String permission) {
    if (_forumConfig?.other == null) return false;

    final other = _forumConfig!.other;

    switch (permission) {
      case 'canEditUserGroup':
        return other.canEditUserGroup;
      case 'canEditUserStatus':
        return other.canEditUserStatus;
      case 'canCreateThreadInCategory':
        return other.canCreateThreadInCategory;
      case 'canViewThreads':
        return other.canViewThreads;
      case 'canFreeViewPaidThreads':
        return other.canFreeViewPaidThreads;
      case 'canCreateDialog':
        return other.canCreateDialog;
      case 'canInviteUserScale':
        return other.canInviteUserScale;
      case 'canInsertThreadAttachment':
        return other.canInsertThreadAttachment;
      case 'canInsertThreadPaid':
        return other.canInsertThreadPaid;
      case 'canInsertThreadVideo':
        return other.canInsertThreadVideo;
      case 'canInsertThreadImage':
        return other.canInsertThreadImage;
      case 'canInsertThreadAudio':
        return other.canInsertThreadAudio;
      case 'canInsertThreadGoods':
        return other.canInsertThreadGoods;
      case 'canInsertThreadPosition':
        return other.canInsertThreadPosition;
      case 'canInsertThreadRedPacket':
        return other.canInsertThreadRedPacket;
      case 'canInsertThreadReward':
        return other.canInsertThreadReward;
      case 'canInsertThreadAnonymous':
        return other.canInsertThreadAnonymous;
      case 'canInsertThreadVote':
        return other.canInsertThreadVote;
      default:
        return false;
    }
  }

  /// 获取统计数据
  Map<String, int> get statistics {
    if (_forumConfig?.other == null) {
      return {'threads': 0, 'posts': 0, 'users': 0};
    }

    final other = _forumConfig!.other;
    return {
      'threads': other.countThreads,
      'posts': other.countPosts,
      'users': other.countUsers,
    };
  }

  /// 根据code获取表情包
  Emoji? getEmojiByCode(String code) {
    try {
      return emojis.firstWhere((emoji) => emoji.code == code);
    } catch (e) {
      return null; // 找不到时返回null
    }
  }

  /// 获取表情包分类
  Map<String, List<Emoji>> get emojisByCategory {
    final Map<String, List<Emoji>> result = {};
    for (final emoji in emojis) {
      if (!result.containsKey(emoji.category)) {
        result[emoji.category] = [];
      }
      result[emoji.category]!.add(emoji);
    }
    return result;
  }
}