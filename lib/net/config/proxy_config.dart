import '../../utils/sp_utils.dart';

class ProxyConfig {
  static final ProxyConfig _instance = ProxyConfig._internal();
  
  factory ProxyConfig() => _instance;
  
  ProxyConfig._internal();
  
  static ProxyConfig getInstance() => _instance;
  
  static const String _proxyEnabledKey = 'proxy_enabled';
  static const String _proxyIpKey = 'proxy_ip';
  static const String _proxyPortKey = 'proxy_port';
  
  bool get isEnabled {
    final sp = SpManager.getInstance();
    return sp.getBool(_proxyEnabledKey) ?? false;
  }
  
  String get ip {
    final sp = SpManager.getInstance();
    return sp.getString(_proxyIpKey) ?? '';
  }
  
  String get port {
    final sp = SpManager.getInstance();
    return sp.getString(_proxyPortKey) ?? '';
  }
  
  String getProxyString() {
    if (!isEnabled) {
      return 'DIRECT';
    }
    
    final proxyIp = ip;
    final proxyPort = port;
    
    if (proxyIp.isEmpty || proxyPort.isEmpty) {
      return 'DIRECT';
    }
    
    return 'PROXY $proxyIp:$proxyPort';
  }
  
  bool shouldTrustCertificate() {
    return isEnabled;
  }
  
  void setConfig(bool enabled, String ip, String port) {
    final sp = SpManager.getInstance();
    sp.put(_proxyEnabledKey, enabled);
    sp.put(_proxyIpKey, ip.trim());
    sp.put(_proxyPortKey, port.trim());
  }
  
  void resetConfig() {
    final sp = SpManager.getInstance();
    sp.put(_proxyEnabledKey, false);
    sp.put(_proxyIpKey, '');
    sp.put(_proxyPortKey, '');
  }
  
  bool get isValidConfig {
    if (!isEnabled) return true;
    return ip.isNotEmpty && port.isNotEmpty;
  }
  
  Map<String, dynamic> toMap() {
    return {
      'enabled': isEnabled,
      'ip': ip,
      'port': port,
      'proxyString': getProxyString(),
    };
  }
  
  @override
  String toString() {
    return 'ProxyConfig(enabled: $isEnabled, ip: $ip, port: $port)';
  }
}