import 'dart:io';

import '../../config/app_config.dart';

/// 将 Android 侧 CommonParamsV2 转为 Dart 版本
/// 用于在发起请求前组装通用参数
class CommonParamsV2 {
  final bool beforeActive;

  CommonParamsV2({this.beforeActive = false});

  /// 模拟 DevLogic.getInstance(context).getValue()
  /// 这里直接返回一个占位符或设备标识（可按需替换为真实实现）
  String _getDevValue() {
    // 如需接入真实设备指纹/ID，请在此实现
    return 'dev_${Platform.operatingSystem}_${Platform.localHostname}';
  }

  /// 登录码（Java: getCodeOfLogin）
  String _getCodeOfLogin() {
    if (beforeActive) return '';
    // 如需接入真实登录码来源，在此替换逻辑
    // 这里用 refer 兜底，避免为空
    return AppConfig.refer;
  }

  /// 生成通用参数
  Map<String, String> transform([Map<String, String>? params]) {
    final map = <String, String>{}..addAll(params ?? const {});

    map['pid'] = AppConfig.pid;                // 渠道/合作方
    map['gid'] = AppConfig.gid;                // 游戏ID
    map['refer'] = AppConfig.refer;            // 渠道标识
    map['dev'] = _getDevValue();               // 设备标识
    map['sversion'] = AppConfig.sversion;      // SDK版本
    map['version'] = AppConfig.sversion;       // App版本（可替换为真实应用版本）
    map['gwversion'] = AppConfig.gwversion;    // 网关版本
    map['time'] = (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString();
    map['scut'] = _getCodeOfLogin();           // 登录码
    map['from'] = Platform.isAndroid ? 'android' : (Platform.isIOS ? 'ios' : Platform.operatingSystem);
    map['host_sdk_version'] = AppConfig.hostSdkVersion; // 宿主版本

    return map;
  }
}


