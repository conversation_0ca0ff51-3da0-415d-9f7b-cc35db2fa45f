import 'package:dlyz_flutter/net/example/user.dart';
import 'package:dlyz_flutter/net/http_service.dart';


Future<void> testApi() async {

  final res = await HttpService.getInstance().get<List<User>>(
    '/users',
    baseUrl: 'https://jsonplaceholder.typicode.com',
    fromJsonT: (json) {
      return (json as List).map((e) => User.fromJson(e)).toList();
    },
  );

  if (res.success) {
    print('用户数量: ${res.data?.length}');
    for (var user in res.data!) {
      print('用户姓名: ${user.name}');
    }
  } else {
    print('请求失败：${res.message}');
  }
}
