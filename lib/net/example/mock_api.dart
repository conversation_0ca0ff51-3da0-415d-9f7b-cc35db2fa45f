import 'dart:async';
import 'dart:math';

/// Mock网络服务类
/// todo 自测使用，不应该在正式环境使用
class MockApiService {
  static final MockApiService _instance = MockApiService._internal();
  factory MockApiService() => _instance;
  MockApiService._internal();

  /// 模拟网络延迟
  static const Duration _networkDelay = Duration(milliseconds: 1500);

  /// 发送验证码
  /// 
  /// [phoneNumber] 手机号
  /// 返回 [Future<bool>] 发送结果，true表示成功，false表示失败
  Future<bool> sendVerifyCode(String phoneNumber) async {
    // 模拟网络请求延迟
    await Future.delayed(_networkDelay);
    
    // 模拟网络请求结果
    // 这里可以根据实际需求调整成功概率
    final random = Random();
    final success = random.nextBool(); // 50% 成功概率
    
    // 模拟一些手机号总是失败的情况（比如无效手机号）
    if (phoneNumber.startsWith('000') || phoneNumber.startsWith('111')) {
      return false;
    }
    
    // 模拟一些手机号总是成功的情况
    if (phoneNumber.startsWith('138') || phoneNumber.startsWith('139')) {
      return true;
    }
    
    return success;
  }

  /// 验证验证码
  /// 
  /// [phoneNumber] 手机号
  /// [verifyCode] 验证码
  /// 返回 [Future<bool>] 验证结果，true表示成功，false表示失败
  Future<bool> verifyCode(String phoneNumber, String verifyCode) async {
    // 模拟网络请求延迟
    await Future.delayed(_networkDelay);
    
    // 模拟验证码验证逻辑
    // 这里可以根据实际需求调整验证规则
    if (verifyCode.length != 6) {
      return false;
    }
    
    // 模拟特定验证码总是成功的情况
    if (verifyCode == '123456') {
      return true;
    }
    
    // 模拟特定验证码总是失败的情况
    if (verifyCode == '000000') {
      return false;
    }
    
    // 其他情况随机返回结果
    final random = Random();
    return random.nextBool();
  }

  /// 模拟登录请求
  /// 
  /// [phoneNumber] 手机号
  /// [password] 密码（可选）
  /// [verifyCode] 验证码（可选）
  /// 返回 [Future<Map<String, dynamic>>] 登录结果
  Future<Map<String, dynamic>> login({
    required String phoneNumber,
    String? password,
    String? verifyCode,
  }) async {
    // 模拟网络请求延迟
    await Future.delayed(_networkDelay);
    
    // 模拟登录验证逻辑
    bool success = false;
    String message = '';
    
    if (verifyCode != null) {
      // 验证码登录
      success = await this.verifyCode(phoneNumber, verifyCode);
      message = success ? '验证码登录成功' : '验证码错误';
    } else if (password != null) {
      // 密码登录
      success = password == '123456'; // 模拟密码验证
      message = success ? '密码登录成功' : '密码错误';
    } else {
      success = false;
      message = '登录参数错误';
    }
    
    return {
      'success': success,
      'message': message,
      'data': success ? {
        'userId': 'user_$phoneNumber',
        'token': 'mock_token_${DateTime.now().millisecondsSinceEpoch}',
        'phoneNumber': phoneNumber,
      } : null,
    };
  }

  /// 验证码登录接口
  /// 
  /// [phoneNumber] 手机号
  /// [verifyCode] 验证码
  /// 返回 [Future<Map<String, dynamic>>] 登录结果，包含用户信息
  Future<Map<String, dynamic>> loginWithVerifyCode({
    required String phoneNumber,
    required String verifyCode,
  }) async {
    // 模拟网络请求延迟
    await Future.delayed(_networkDelay);
    
    // 验证验证码
    final verifySuccess = await this.verifyCode(phoneNumber, verifyCode);
    
    if (!verifySuccess) {
      return {
        'success': false,
        'message': '验证码错误',
        'data': null,
      };
    }
    
    // 模拟登录成功，返回用户信息
    final userId = 'user_${phoneNumber}_${DateTime.now().millisecondsSinceEpoch}';
    final token = 'mock_token_${DateTime.now().millisecondsSinceEpoch}';
    
    return {
      'success': true,
      'message': '登录成功',
      'data': {
        'userId': userId,
        'token': token,
        'phoneNumber': phoneNumber,
        'userInfo': {
          'userId': userId,
          'uname': 'user_$phoneNumber',
          'alias': '用户${phoneNumber.substring(7)}', // 显示手机号后4位
          'mobile': phoneNumber,
          'loginType': 'phone',
          'avatar': null,
          'createdAt': DateTime.now().toIso8601String(),
          'lastLoginAt': DateTime.now().toIso8601String(),
        },
      },
    };
  }

  /// 微信登录接口
  /// 
  /// 返回 [Future<Map<String, dynamic>>] 登录结果，包含用户信息
  Future<Map<String, dynamic>> wechatLogin() async {
    // 模拟网络请求延迟
    await Future.delayed(_networkDelay);
    
    // 模拟微信登录成功概率
    final random = Random();
    final success = random.nextBool(); // 50% 成功概率
    
    if (!success) {
      return {
        'success': false,
        'message': '微信登录失败，请重试',
        'data': null,
      };
    }
    
    // 模拟登录成功，返回用户信息
    final userId = 'wechat_user_${DateTime.now().millisecondsSinceEpoch}';
    final token = 'wechat_token_${DateTime.now().millisecondsSinceEpoch}';
    
    return {
      'success': true,
      'message': '微信登录成功',
      'data': {
        'userId': userId,
        'token': token,
        'userInfo': {
          'userId': userId,
          'uname': '微信用户',
          'alias': '微信用户${DateTime.now().millisecondsSinceEpoch % 10000}',
          'mobile': null,
          'loginType': 'wechat',
          'avatar': null,
          'createdAt': DateTime.now().toIso8601String(),
          'lastLoginAt': DateTime.now().toIso8601String(),
        },
      },
    };
  }
} 