import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

/**
 * 使用注意：
 * 1.user增加注解 @JsonSerializable()
 * 2.flutter pub run build_runner build
 * 3.fromJson，toJson方法
 */
@JsonSerializable()
class User {
  final int id;
  final String name;

  User({required this.id, required this.name});

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  Map<String, dynamic> toJson() => _$UserToJson(this);
}
