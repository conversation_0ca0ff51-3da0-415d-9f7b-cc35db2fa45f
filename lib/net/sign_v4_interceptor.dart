import 'package:dio/dio.dart';
import '../utils/md5_utils.dart';
import '../utils/log_util.dart';

/// SignV4签名拦截器
/// 1. 拼接{$pid}{$gid}{$time}{$appkey}{$uid}
/// 2. 对签名原始字符串计算md5值, 得到最终签名(小写)
/// 
/// 参考Harmony项目中的SignV4Interceptor实现
class SignV4Interceptor extends Interceptor {
  static const String _paramName = 'sign';
  final String _appKey;

  SignV4Interceptor({required String appKey}) : _appKey = appKey {
    if (appKey.isEmpty) {
      throw ArgumentError('App key cannot be empty!');
    }
  }

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    try {
      // 只对POST请求进行签名
      if (options.method.toUpperCase() == 'POST' && options.data != null) {
        if (options.data is Map<String, dynamic>) {
          final params = Map<String, dynamic>.from(options.data);
          final sign = _generateSign(params);
          if (sign.isNotEmpty) {
            params[_paramName] = sign;
            options.data = params;
            LogUtil.d('[SignV4] POST签名完成: $sign', tag: 'SIGN_V4');
          }
        } else if (options.data is String) {
          // 处理form-encoded字符串
          final params = _parseFormData(options.data as String);
          final sign = _generateSign(params);
          if (sign.isNotEmpty) {
            params[_paramName] = sign;
            options.data = params;
            LogUtil.d('[SignV4] POST Form签名完成: $sign', tag: 'SIGN_V4');
          }
        }
      }
      
      handler.next(options);
    } catch (e) {
      LogUtil.e('[SignV4] 签名异常: $e', tag: 'SIGN_V4');
      handler.next(options);
    }
  }

  /// 生成V4签名
  /// 1. 拼接{$pid}{$gid}{$time}{$appkey}{$uid}
  /// 2. 对签名原始字符串计算md5值, 得到最终签名(小写)
  String _generateSign(Map<String, dynamic> params) {
    try {
      // 收集必需参数
      final String? pid = params['pid'];
      final String? gid = params['gid'];
      final String? time = params['time'];

      // 验证必需参数
      if (pid == null || pid.isEmpty) {
        throw ArgumentError('pid字段不能为空');
      }
      if (gid == null || gid.isEmpty) {
        throw ArgumentError('gid字段不能为空');
      }
      if (time == null || time.isEmpty) {
        throw ArgumentError('time字段不能为空');
      }

      // 1. 拼接{$pid}{$gid}{$time}{$appkey}{$uid}
      final signStr = pid + gid + time + _appKey;
      
      // 2. 对签名原始字符串计算md5值, 得到最终签名(小写)
      final sign = Md5Utils.generateMd5(signStr).toLowerCase();
      
      LogUtil.d('[SignV4] 签名原串: $signStr', tag: 'SIGN_V4');
      LogUtil.d('[SignV4] 签名结果: $sign', tag: 'SIGN_V4');
      
      return sign;
    } catch (e) {
      LogUtil.e('[SignV4] 生成签名失败: $e', tag: 'SIGN_V4');
      return '';
    }
  }

  /// 解析form-encoded数据
  Map<String, String> _parseFormData(String formData) {
    final params = <String, String>{};
    if (formData.isNotEmpty) {
      final pairs = formData.split('&');
      for (final pair in pairs) {
        final parts = pair.split('=');
        if (parts.length == 2) {
          params[parts[0]] = Uri.decodeComponent(parts[1]);
        } else if (parts.length == 1) {
          params[parts[0]] = '';
        }
      }
    }
    return params;
  }

}

/// 签名扩展字符串类
/// 对应Java代码中的SignExt
class SignExt {
  String value = '';

  SignExt([Object? value]) {
    if (value != null) {
      this.value = value.toString();
    }
  }

  /// 追加值到扩展字符串
  SignExt append(Object? value) {
    if (value != null) {
      this.value += value.toString();
    }
    return this;
  }

  @override
  String toString() {
    return value;
  }
}