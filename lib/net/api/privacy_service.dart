import '../http_base_response.dart';
import '../transformers/response_transformers.dart';
import '../sign_interceptor.dart';
import '../common/common_params_v2.dart';
import '../../model/privacy_config.dart';
import '../http_service.dart';

class PrivacyService {
  static final PrivacyService _instance = PrivacyService._internal();
  factory PrivacyService() => _instance;
  PrivacyService._internal();
  
  /// 获取隐私协议配置
  Future<BaseResponse<PrivacyConfig>> getPrivacyConfig({
    required String baseUrl,
  }) async {
    try {
      // 组装通用参数（隐私协议请求通常在激活/登录前，因此 beforeActive=true）
      final params = CommonParamsV2(beforeActive: true).transform();

      // 使用 HttpService 发送请求，指定 V3 签名
      final response = await HttpService.getInstance().get<PrivacyConfig>(
        '/go/cfg/user_protocol',
        baseUrl: baseUrl,
        queryParameters: params,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) => PrivacyConfig.fromJson(Map<String, dynamic>.from(data)),
        parseStrategy: ResponseTransformers.stateData<PrivacyConfig>(),
      );

      return response;
    } catch (e) {
      return BaseResponse.error('未知错误: $e', code: -1);
    }
  }
} 