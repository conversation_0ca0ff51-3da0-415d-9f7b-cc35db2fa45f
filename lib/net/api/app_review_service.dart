import 'package:flutter/material.dart';
import '../../utils/log_util.dart';
import '../http_service.dart';
import '../http_base_response.dart';
import '../transformers/response_transformers.dart';
import '../sign_interceptor.dart';
import '../config/http_base_config.dart';
import '../../model/app_review_status_response.dart';
import 'init_service.dart';

class AppReviewService {
  static final AppReviewService _instance = AppReviewService._internal();
  factory AppReviewService() => _instance;
  AppReviewService._internal();

  /// 过审状态检查接口
  /// 检查应用是否处于审核状态
  /// [context] 用于获取准确的屏幕尺寸，如果不提供则使用默认值
  /// [additionalParams] 额外的参数，会合并到公共参数中
  static Future<BaseResponse<AppReviewStatusResponse>> checkReviewStatus({
    BuildContext? context,
    Map<String, dynamic>? additionalParams,
  }) async {
    try {
      // 接口URL
      const url = '/go/cfg/app/status';
      
      // 获取M层激活接口的公共参数
      final commonParams = await InitService.getCommonParams(context: context);
      
      // 合并额外参数
      if (additionalParams != null) {
        commonParams.addAll(additionalParams);
      }

      LogUtil.d('过审状态检查请求: $url, 参数: $commonParams');

      // 使用 HttpService 发送 POST 请求，指定 V3 签名
      final response = await HttpService.getInstance().post<AppReviewStatusResponse>(
        url,
        baseUrl: HttpBaseConfig.mActivateBaseUrl,
        data: commonParams,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) {
          if (data == null) {
            throw Exception('响应数据为空');
          }
          if (data is Map<String, dynamic>) {
            return AppReviewStatusResponse.fromJson(data);
          }
          throw Exception('数据格式不正确');
        },
        parseStrategy: ResponseTransformers.stateData<AppReviewStatusResponse>(),
      );

      LogUtil.d('过审状态检查响应: ${response.code}, 数据: ${response.data}');
      return response;
    } catch (e) {
      LogUtil.e('过审状态检查请求失败: $e');
      return BaseResponse<AppReviewStatusResponse>(
        code: 500,
        message: '过审状态检查失败: $e',
        data: null,
      );
    }
  }
}