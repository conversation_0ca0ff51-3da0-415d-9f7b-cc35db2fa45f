import 'package:flutter/material.dart';
import '../../utils/log_util.dart';
import '../http_service.dart';
import '../http_base_response.dart';
import '../transformers/response_transformers.dart';
import '../sign_interceptor.dart';
import '../config/http_base_config.dart';
import '../../model/game_circle.dart';
import '../../model/game_circle_config.dart';
import 'init_service.dart';

class GameCircleService {
  static final GameCircleService _instance = GameCircleService._internal();
  factory GameCircleService() => _instance;
  GameCircleService._internal();

  /// 获取游戏圈子列表接口
  /// 获取可用的游戏圈子列表
  /// [context] 用于获取准确的屏幕尺寸，如果不提供则使用默认值
  /// [additionalParams] 额外的参数，会合并到公共参数中
  static Future<BaseResponse<GameCircleListResponse>> getGameCircleList({
    BuildContext? context,
    Map<String, dynamic>? additionalParams,
  }) async {
    try {
      // 接口URL
      const url = 'game_circle/get_list';
      
      // 获取M层激活接口的公共参数
      final commonParams = await InitService.getCommonParams(context: context);
      
      // 合并额外参数
      if (additionalParams != null) {
        commonParams.addAll(additionalParams);
      }

      LogUtil.d('游戏圈子列表请求: $url, 参数: $commonParams');

      // 使用 HttpService 发送 POST 请求，指定 V3 签名
      final response = await HttpService.getInstance().post<GameCircleListResponse>(
        url,
        baseUrl: HttpBaseConfig.gameBaseUrl,
        data: commonParams,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) {
          if (data == null) {
            throw Exception('响应数据为空');
          }
          if (data is Map<String, dynamic>) {
            return GameCircleListResponse.fromJson(data);
          }
          throw Exception('数据格式不正确');
        },
        parseStrategy: ResponseTransformers.stateData<GameCircleListResponse>(),
      );

      LogUtil.d('游戏圈子列表响应: ${response.code}, 数据: ${response.data}');
      return response;
    } catch (e) {
      LogUtil.e('游戏圈子列表请求失败: $e');
      return BaseResponse<GameCircleListResponse>(
        code: 500,
        message: '游戏圈子列表请求失败: $e',
        data: null,
      );
    }
  }

  /// 获取游戏圈子配置信息接口
  /// 获取指定圈子的配置信息
  /// [context] 用于获取准确的屏幕尺寸，如果不提供则使用默认值
  /// [tgid] 目标游戏ID
  /// [circleId] 圈子ID
  /// [additionalParams] 额外的参数，会合并到公共参数中
  static Future<BaseResponse<GameCircleConfig>> getGameCircleConfig({
    BuildContext? context,
    required String tgid,
    required String circleId,
    Map<String, dynamic>? additionalParams,
  }) async {
    try {
      // 接口URL
      const url = 'game_circle/get_info';
      
      // 获取M层激活接口的公共参数
      final commonParams = await InitService.getCommonParams(context: context);
      
      // 添加必需的参数
      commonParams['tgid'] = tgid;
      commonParams['circle_id'] = circleId;
      
      // 合并额外参数
      if (additionalParams != null) {
        commonParams.addAll(additionalParams);
      }

      LogUtil.d('游戏圈子配置请求: $url, 参数: $commonParams');

      // 使用 HttpService 发送 POST 请求，指定 V3 签名
      final response = await HttpService.getInstance().post<GameCircleConfig>(
        url,
        baseUrl: HttpBaseConfig.gameBaseUrl,
        data: commonParams,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) {
          if (data == null) {
            throw Exception('响应数据为空');
          }
          if (data is Map<String, dynamic>) {
            return GameCircleConfig.fromJson(data);
          }
          throw Exception('数据格式不正确');
        },
        parseStrategy: ResponseTransformers.stateData<GameCircleConfig>(),
      );

      LogUtil.d('游戏圈子配置响应: ${response.code}, 数据: ${response.data}');
      return response;
    } catch (e) {
      LogUtil.e('游戏圈子配置请求失败: $e');
      return BaseResponse<GameCircleConfig>(
        code: 500,
        message: '游戏圈子配置请求失败: $e',
        data: null,
      );
    }
  }
}