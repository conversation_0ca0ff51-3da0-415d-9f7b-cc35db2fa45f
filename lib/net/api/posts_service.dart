import '../http_service.dart';
import '../http_base_response.dart';
import '../../model/posts_list.dart';
import '../transformers/response_transformers.dart';

class PostsService {
  static final PostsService _instance = PostsService._internal();
  factory PostsService() => _instance;
  PostsService._internal();

  final HttpService _httpService = HttpService();
  
  // 帖子接口统一headers
  static const Map<String, dynamic> _postsHeaders = {
    'authorization': 'Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
    'forum-tgid': '147',
    'accept': '*/*',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'cache-control': 'no-cache',
    'content-type': 'application/json',
    'pragma': 'no-cache',
    'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
  };

  /// 获取帖子回复/评论列表
  /// 请求例子：https://forum-api.odchqpto.com/api/v3/posts.list?filter[thread]=197276&filter[isFold]=0&sort=hot&page=1&perPage=20&index=1
  Future<BaseResponse<PostsList>> getPostsList({
    required String baseUrl,
    required int threadId,
    int page = 1,
    int pageSize = 20,
    String sort = 'hot',
    int isFold = 0,
    int index = 1,
  }) async {
    final queryParams = <String, dynamic>{
      'filter[thread]': threadId,
      'filter[isFold]': isFold,
      'sort': sort,
      'page': page,
      'perPage': pageSize,
      'index': index,
    };

    return await _httpService.get<PostsList>(
      '/api/v3/posts.list',
      baseUrl: baseUrl,
      queryParameters: queryParams,
      headers: _postsHeaders,
      parseStrategy: ResponseTransformers.codeData<PostsList>(),
      fromJsonT: (data) {
        // 处理服务器返回的数据结构 {"Code":0,"Message":"","Data":{...}}
        if (data is Map<String, dynamic>) {
          final responseData = data['Data'];
          if (responseData != null) {
            return PostsList.fromJson(responseData);
          }
        }
        // 如果直接是对象格式
        return PostsList.fromJson(data);
      },
    );
  }
}