import 'package:dio/dio.dart';
import 'package:dlyz_flutter/net/sign_v2_interceptor.dart';
import 'package:dlyz_flutter/net/sign_v3_interceptor.dart';
import 'package:dlyz_flutter/net/sign_v4_interceptor.dart';
import 'package:dlyz_flutter/net/sign_v5_interceptor.dart';

/// 签名版本枚举
enum SignType {
  v2('V2'),
  v3('V3'),
  v4('V4'),
  v5('V5'),
  none('NONE');

  const SignType(this.version);

  final String version;
}

/// 动态签名拦截器
/// 支持在请求时动态指定签名类型，而不需要预先固定签名版本
class SignInterceptor extends Interceptor {
  static const String _tag = 'SignInterceptor';

  /// 请求选项中用于指定签名类型的键名
  /// 使用方式: options.extra[SignInterceptor.signTypeKey] = SignType.v3
  static const String signTypeKey = 'signType';

  final String _appKey;

  late SignV2Interceptor _mV2;
  late SignV3Interceptor _mV3;
  late SignV4Interceptor _mV4;
  late SignV5Interceptor _mV5;

  /// 构造函数
  /// [appKey] 应用密钥
  SignInterceptor({required String appKey}) : _appKey = appKey {
    _validateAppKey();
    _mV2 = SignV2Interceptor(appKey: appKey);
    _mV3 = SignV3Interceptor(appKey: appKey);
    _mV4 = SignV4Interceptor(appKey: appKey);
    _mV5 = SignV5Interceptor(appKey: appKey);
  }

  /// 验证AppKey
  void _validateAppKey() {
    if (_appKey.isEmpty) {
      throw ArgumentError.value(_appKey, 'appKey', 'App key cannot be empty');
    }
  }

  /// 获取或创建指定版本的签名拦截器
  Interceptor? _getInterceptor(SignType signType) {
    switch (signType) {
      case SignType.v2:
        return _mV2;
      case SignType.v3:
        return _mV3;
      case SignType.v4:
        return _mV4;
      case SignType.v5:
        return _mV5;
      case SignType.none:
        return null;
    }
  }

  /// 从请求选项中提取签名类型
  SignType _extractSignType(RequestOptions? options) {
    final signTypeValue = options?.headers[signTypeKey];

    if (signTypeValue == null) {
      return SignType.none;
    }

    if (signTypeValue is SignType) {
      return signTypeValue;
    }

    if (signTypeValue is String) {
      switch (signTypeValue.toUpperCase()) {
        case 'V2':
          return SignType.v2;
        case 'V3':
          return SignType.v3;
        case 'V4':
          return SignType.v4;
        case 'V5':
          return SignType.v5;
        default:
          return SignType.none;
      }
    }

    return SignType.none;
  }

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final signType = _extractSignType(options);

    // 移除签名类型参数，避免传递给服务端
    options.extra.remove(signTypeKey);

    // 获取对应的签名拦截器并处理请求
    final interceptor = _getInterceptor(signType);
    if (interceptor != null) {
      interceptor.onRequest(options, handler);
    } else {
      handler.next(options);
    }
  }

  /// 获取当前使用的AppKey
  String get appKey => _appKey;
}
