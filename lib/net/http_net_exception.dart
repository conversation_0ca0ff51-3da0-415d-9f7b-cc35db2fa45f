import 'package:dio/dio.dart';

import 'http_base_response.dart';
import 'http_error_code.dart';

class NetException implements Exception {
  final String message;
  final int? code;

  NetException(this.message, {this.code});

  @override
  String toString() => 'HttpException: $message (code: $code)';
}

BaseResponse<T> handleDioError<T>(DioException e) {
  switch (e.type) {
    case DioExceptionType.connectionTimeout:
    case DioExceptionType.sendTimeout:
    case DioExceptionType.receiveTimeout:
      return BaseResponse.error('请求超时', code: ErrorCode.networkTimeout);
    case DioExceptionType.cancel:
      return BaseResponse.error('请求被取消', code: ErrorCode.requestCancelled);
    case DioExceptionType.connectionError:
      return BaseResponse.error('网络连接失败', code: ErrorCode.networkError);
    case DioExceptionType.badResponse:
      final statusCode = e.response?.statusCode ?? ErrorCode.httpError;
      return BaseResponse.error('服务器错误: $statusCode', code: statusCode);
    case DioExceptionType.unknown:
    default:
      return BaseResponse.error(
        '未知网络错误: ${e.message}',
        code: ErrorCode.unknown,
      );
  }
}
