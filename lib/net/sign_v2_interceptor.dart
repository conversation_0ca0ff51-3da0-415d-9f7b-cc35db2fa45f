import 'package:dio/dio.dart';
import '../utils/md5_utils.dart';
import '../utils/log_util.dart';
import '../config/app_config.dart';

/// SignV2签名拦截器
/// 国内旧版签名规则
/// 1. 拼接{$pid}{$gid}{$refer}{$dev}{$version}{$time}{$key}
/// 2. 对签名原始字符串计算md5值, 得到最终签名(小写)
/// 
/// 参考Android项目中的SignV2Interceptor实现
class SignV2Interceptor extends Interceptor {
  static const String _paramName = 'sign';
  final String _appKey;

  SignV2Interceptor({required String appKey}) : _appKey = appKey {
    if (appKey.isEmpty) {
      throw ArgumentError('App key cannot be empty!');
    }
  }

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    try {
      // 只对POST请求进行签名
      if (options.method.toUpperCase() == 'POST' && options.data != null) {
        if (options.data is Map<String, dynamic>) {
          final params = Map<String, String>.from(options.data);
          final sign = _generateSign(params);
          if (sign.isNotEmpty) {
            params[_paramName] = sign;
            options.data = params;
            LogUtil.d('[SignV2] POST签名完成: $sign', tag: 'SIGN_V2');
          }
        } else if (options.data is String) {
          // 处理form-encoded字符串
          final params = _parseFormData(options.data as String);
          final sign = _generateSign(params);
          if (sign.isNotEmpty) {
            params[_paramName] = sign;
            options.data = params;
            LogUtil.d('[SignV2] POST Form签名完成: $sign', tag: 'SIGN_V2');
          }
        }
      }
      
      handler.next(options);
    } catch (e) {
      LogUtil.e('[SignV2] 签名异常: $e', tag: 'SIGN_V2');
      handler.next(options);
    }
  }

  /// 生成V2签名
  /// 1. 拼接{$pid}{$gid}{$refer}{$dev}{$version}{$time}{$key}
  /// 2. 对签名原始字符串计算md5值, 得到最终签名(小写)
  String _generateSign(Map<String, String> params) {
    try {
      // 收集必需参数
      final String? pid = params['pid'];
      final String? gid = params['gid'];
      final String? refer = params['refer'];
      final String? dev = params['dev'];
      final String? version = params['version'];
      final String? time = params['time'];

      // 验证必需参数
      if (pid == null || pid.isEmpty) {
        throw ArgumentError('pid字段不能为空');
      }
      if (gid == null || gid.isEmpty) {
        throw ArgumentError('gid字段不能为空');
      }
      if (refer == null || refer.isEmpty) {
        throw ArgumentError('refer字段不能为空');
      }
      if (dev == null || dev.isEmpty) {
        throw ArgumentError('dev字段不能为空');
      }
      if (version == null || version.isEmpty) {
        throw ArgumentError('version字段不能为空');
      }
      if (time == null || time.isEmpty) {
        throw ArgumentError('time字段不能为空');
      }

      // 1. 拼接{$pid}{$gid}{$refer}{$dev}{$version}{$time}{$key}
      final signStr = pid + gid + refer + dev + version + time + _appKey;
      
      // 2. 对签名原始字符串计算md5值, 得到最终签名(小写)
      final sign = Md5Utils.generateMd5(signStr).toLowerCase();
      
      LogUtil.d('[SignV2] 签名原串: $signStr', tag: 'SIGN_V2');
      LogUtil.d('[SignV2] 签名结果: $sign', tag: 'SIGN_V2');
      
      return sign;
    } catch (e) {
      LogUtil.e('[SignV2] 生成签名失败: $e', tag: 'SIGN_V2');
      return '';
    }
  }

  /// 解析form-encoded数据
  Map<String, String> _parseFormData(String formData) {
    final params = <String, String>{};
    if (formData.isNotEmpty) {
      final pairs = formData.split('&');
      for (final pair in pairs) {
        final parts = pair.split('=');
        if (parts.length == 2) {
          params[parts[0]] = Uri.decodeComponent(parts[1]);
        } else if (parts.length == 1) {
          params[parts[0]] = '';
        }
      }
    }
    return params;
  }

  /// 静态方法：直接生成签名（使用默认appKey）
  /// 用于独立的签名计算，不依赖拦截器实例
  static String sign(Map<String, String> params) {
    try {
      final appKey = AppConfig.appKey;
      return signWithKey(appKey, params);
    } catch (e) {
      LogUtil.e('[SignV2] 静态签名方法异常: $e', tag: 'SIGN_V2');
      return '';
    }
  }

  /// 静态方法：使用指定appKey生成签名
  /// [appKey] 应用密钥
  /// [params] 参数Map
  /// [ext] 扩展签名字符串，可选
  static String signWithKey(String appKey, Map<String, String> params, {SignExt? ext}) {
    if (params.isEmpty || appKey.isEmpty) {
      LogUtil.w('[SignV2] 参数为空或appKey为空', tag: 'SIGN_V2');
      return '';
    }

    try {
      final String? pid = params['pid'];
      final String? gid = params['gid'];
      final String? refer = params['refer'];
      final String? dev = params['dev'];
      final String? version = params['version'];
      final String? time = params['time'];

      // 验证必需参数
      if (pid == null || gid == null || refer == null || dev == null || version == null || time == null) {
        LogUtil.w('[SignV2] 缺少必需参数', tag: 'SIGN_V2');
        return '';
      }

      // 1. 拼接{$pid}{$gid}{$refer}{$dev}{$version}{$time}{$key}
      String signStr = pid + gid + refer + dev + version + time + appKey;
      
      // 添加扩展字符串
      if (ext != null && ext.value.isNotEmpty) {
        signStr += ext.value;
      }
      
      // 2. 对签名原始字符串计算md5值, 得到最终签名(小写)
      final sign = Md5Utils.generateMd5(signStr).toLowerCase();
      
      LogUtil.d('[SignV2] 静态方法签名原串: $signStr', tag: 'SIGN_V2');
      LogUtil.d('[SignV2] 静态方法签名结果: $sign', tag: 'SIGN_V2');
      
      return sign;
    } catch (e) {
      LogUtil.e('[SignV2] 静态签名生成失败: $e', tag: 'SIGN_V2');
      return '';
    }
  }

  /// 验证签名是否正确
  /// [params] 包含签名的参数Map
  /// [appKey] 应用密钥，如果不提供则使用默认值
  /// [ext] 扩展签名字符串，可选
  static bool verifySign(Map<String, String> params, {String? appKey, SignExt? ext}) {
    try {
      final actualSign = params[_paramName];
      if (actualSign == null || actualSign.isEmpty) {
        LogUtil.w('[SignV2] 参数中没有签名字段', tag: 'SIGN_V2');
        return false;
      }

      // 移除sign字段后计算签名
      final paramsWithoutSign = Map<String, String>.from(params);
      paramsWithoutSign.remove(_paramName);

      final expectedSign = appKey != null 
          ? signWithKey(appKey, paramsWithoutSign, ext: ext)
          : sign(paramsWithoutSign);

      final isValid = actualSign.toLowerCase() == expectedSign.toLowerCase();
      LogUtil.d('[SignV2] 签名验证结果: $isValid (实际: $actualSign, 期望: $expectedSign)', tag: 'SIGN_V2');
      
      return isValid;
    } catch (e) {
      LogUtil.e('[SignV2] 验证签名失败: $e', tag: 'SIGN_V2');
      return false;
    }
  }
}

/// 签名扩展字符串类
/// 对应Java代码中的SignExt
class SignExt {
  String value = '';

  SignExt([Object? value]) {
    if (value != null) {
      this.value = value.toString();
    }
  }

  /// 追加值到扩展字符串
  SignExt append(Object? value) {
    if (value != null) {
      this.value += value.toString();
    }
    return this;
  }

  @override
  String toString() {
    return value;
  }
}