import 'response_parse_strategy.dart';
import '../http_base_response.dart';
import '../http_error_code.dart';
import '../../utils/log_util.dart';

/// State/Msg/Data 格式解析策略
class StateMsgDataStrategy<T> extends ResponseParseStrategy<T> {
  final int successState;

  StateMsgDataStrategy({this.successState = 1});

  @override
  BaseResponse<T> parse(dynamic responseData, T Function(dynamic) fromJsonT) {
    try {
      if (responseData is Map<String, dynamic>) {
        final state = responseData['state'] ?? responseData['State'];
        final msg = responseData['msg'] ?? responseData['Msg'] ?? responseData['message'] ?? '';
        final data = responseData['data'] ?? responseData['Data'];

        if (state == successState) {
          // 成功状态
          final parsedData = fromJsonT(data);
          return BaseResponse<T>(
            code: 200, // 转换为标准成功码
            message: msg.toString(),
            data: parsedData,
          );
        } else {
          // 失败状态
          LogUtil.w('业务逻辑失败: state=$state, msg=$msg');
          return BaseResponse.error(
            msg.toString(),
            code: state ?? ErrorCode.unknown,
          );
        }
      } else {
        LogUtil.e('响应格式不正确，期望Map<String, dynamic>，实际: ${responseData.runtimeType}');
        return BaseResponse.error(
          '响应格式错误',
          code: ErrorCode.parseError,
        );
      }
    } catch (e) {
      LogUtil.e('StateMsgData格式解析失败', error: e);
      return BaseResponse.error(
        '解析失败: $e',
        code: ErrorCode.parseError,
      );
    }
  }

  @override
  String get strategyName => 'StateMsgDataStrategy';
}