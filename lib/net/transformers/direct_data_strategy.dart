import 'response_parse_strategy.dart';
import '../http_base_response.dart';
import '../http_error_code.dart';
import '../../utils/log_util.dart';

/// 直接数据格式解析策略
class DirectDataStrategy<T> extends ResponseParseStrategy<T> {
  @override
  BaseResponse<T> parse(dynamic responseData, T Function(dynamic) fromJsonT) {
    try {
      // 直接解析数据，认为都是成功的
      final parsedData = fromJsonT(responseData);
      return BaseResponse<T>(
        code: 200,
        message: 'OK',
        data: parsedData,
      );
    } catch (e) {
      LogUtil.e('DirectData格式解析失败', error: e);
      return BaseResponse.error(
        '解析失败: $e',
        code: ErrorCode.parseError,
      );
    }
  }

  @override
  String get strategyName => 'DirectDataStrategy';
}