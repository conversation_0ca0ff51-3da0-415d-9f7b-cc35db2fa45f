import 'state_msg_data_strategy.dart';
import 'code_message_data_strategy.dart';
import 'direct_data_strategy.dart';

/// 响应转换器工厂类
class ResponseTransformers {
  /// 状态转换器（state/msg/data格式）
  static StateMsgDataStrategy<T> stateData<T>({int successState = 1}) {
    return StateMsgDataStrategy<T>(successState: successState);
  }

  /// 标准转换器（code/message/data格式）  
  static CodeMessageDataStrategy<T> codeData<T>({int successCode = 0}) {
    return CodeMessageDataStrategy<T>(successCode: successCode);
  }

  /// 直接转换器（响应体直接是数据）
  static DirectDataStrategy<T> direct<T>() {
    return DirectDataStrategy<T>();
  }
}