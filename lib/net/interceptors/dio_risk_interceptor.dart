import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import '../../pages/risk_web_page.dart';

class RiskInterceptor extends Interceptor {
  static const int riskCode = 42000;
  static bool _isShowingRisk = false;

  final Dio dio;
  final GlobalKey<NavigatorState> navigatorKey;

  RiskInterceptor(this.dio, this.navigatorKey);

  bool _isGetOrPost(RequestOptions o) {
    final m = o.method.toUpperCase();
    return m == 'GET' || m == 'POST';
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    final opts = response.requestOptions;

    try {
      final int? state = _tryReadState(response.data);
      final String? riskData = _tryReadRiskDataString(response.data);

      final canShowUi = navigatorKey.currentState?.mounted == true && navigatorKey.currentState?.overlay != null;

      if (state != riskCode || riskData == null || riskData.isEmpty || !_isGetOrPost(opts) || !canShowUi) {
        handler.next(response);
        return;
      }

      if (opts.extra['riskRetried'] == true) {
        // 防止死循环：只允许一次重试
        handler.next(response);
        return;
      }

      if (_isShowingRisk) {
        // 已有风控页面在展示，放行当前响应（保持与原生防抖一致）
        handler.next(response);
        return;
      }

      _isShowingRisk = true;
      final bool retry = await _openRiskPageAndWait(riskData);

      if (retry) {
        final RequestOptions newOpts = _cloneOptionsForRetry(opts);
        newOpts.extra['riskRetried'] = true;
        final Response retried = await dio.fetch(newOpts);
        handler.resolve(retried);
      } else {
        handler.reject(
          DioException(requestOptions: opts, response: response, type: DioExceptionType.badResponse, message: 'risk not passed'),
        );
      }
    } catch (e) {
      handler.next(response); // 兜底不拦截
    } finally {
      _isShowingRisk = false;
    }
  }

  int? _tryReadState(dynamic data) {
    try {
      if (data is Map) {
        if (data['state'] is int) {
          return data['state'] as int;
        }
        if (data['code'] is int) {
          return data['code'] as int;
        }
        // 检查state字段是否是其他类型但可以转换为int
        if (data['state'] != null) {
          try {
            final intState = int.parse(data['state'].toString());
            return intState;
          } catch (e) {
            // ignore parsing error
          }
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  String? _tryReadRiskDataString(dynamic data) {
    try {
      if (data is Map && data['data'] != null) {
        final v = data['data'];
        
        if (v is String) {
          return v;
        }
        
        // 检查是否是webview结构: data.webview.pop_up_url
        if (v is Map && v['webview'] != null && v['webview'] is Map) {
          final webviewData = v['webview'] as Map;
          if (webviewData['pop_up_url'] != null) {
            // 构造风控数据，包含URL信息
            final result = jsonEncode({
              'url': webviewData['pop_up_url'],
              'type': 'webview'
            });
            return result;
          }
        }
        
        // 兜底：返回完整的data内容
        final result = jsonEncode(v);
        return result;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  RequestOptions _cloneOptionsForRetry(RequestOptions o) {
    return RequestOptions(
      path: o.path,
      method: o.method,
      baseUrl: o.baseUrl,
      queryParameters: Map<String, dynamic>.from(o.queryParameters),
      data: o.data,
      headers: Map<String, dynamic>.from(o.headers),
      responseType: o.responseType,
      contentType: o.contentType,
      followRedirects: o.followRedirects,
      receiveDataWhenStatusError: o.receiveDataWhenStatusError,
      extra: Map<String, dynamic>.from(o.extra),
      listFormat: o.listFormat,
      sendTimeout: o.sendTimeout,
      receiveTimeout: o.receiveTimeout,
      connectTimeout: o.connectTimeout,
    );
  }

  Future<bool> _openRiskPageAndWait(String riskJson) async {
    final ctx = navigatorKey.currentState!.overlay!.context;
    final bool? result = await RiskWebPage.showRiskDialog(ctx, riskJson);
    return result == true;
  }
}
