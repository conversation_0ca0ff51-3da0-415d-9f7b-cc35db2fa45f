import '../manager/channel_manager.dart';
import 'fast_login_http_util.dart';
import 'agreement_service.dart';

/// 闪验登录服务类
class FastLoginService {
  static final FastLoginService _instance = FastLoginService._internal();
  factory FastLoginService() => _instance;
  FastLoginService._internal();
  
  final ChannelManager _channelManager = ChannelManager();
  Map<String, dynamic>? _fastLoginConfig;
  
  /// 检查闪验环境
  Future<bool> checkEnvironment() async {
    try {
      // 获取配置并设置到原生端
      _fastLoginConfig ??= await FastLoginHttpUtil.requestFastConfig();
      
      // 获取协议地址
      String userAgreementUrl = AgreementService.getUserAgreementUrl();
      String privacyPolicyUrl = AgreementService.getPrivacyPolicyUrl();
      
      await _channelManager.setFastLoginConfig(
        _fastLoginConfig!,
        userAgreementUrl: userAgreementUrl,
        privacyPolicyUrl: privacyPolicyUrl,
      );
      return await _channelManager.checkFastLoginEnvironment();
    } catch (e) {
      print('检查闪验环境失败: $e');
      return false;
    }
  }
  
  /// 初始化闪验SDK
  Future<void> initialize() async {
    try {
      // 确保配置已存在
      if (_fastLoginConfig == null) {
        throw FastLoginException('CONFIG_NOT_FOUND', '请先调用 checkEnvironment() 检查环境');
      }
      print('获取到闪验配置: $_fastLoginConfig');
      final success = await _channelManager.initializeFastLogin();
      if (!success) {
        throw FastLoginException('INIT_FAILED', '闪验SDK初始化失败');
      }
    } catch (e) {
      print('初始化闪验SDK失败: $e');
      rethrow;
    }
  }
  
  /// 执行闪验登录
  Future<Map<String, dynamic>> doFastLogin() async {
    return await _channelManager.doFastLogin();
  }
}