import 'dart:io';

import 'package:flutter/material.dart';
import 'package:fluwx/fluwx.dart';

class WechatLoginService {
  static final WechatLoginService _instance = WechatLoginService._internal();
  factory WechatLoginService() => _instance;
  WechatLoginService._internal();

  bool _isInitialized = false;
  Function(Map<String, dynamic>)? _loginCallback;
  late Fluwx _fluwx;
  FluwxCancelable? _responseSubscription;

  /// 初始化微信SDK
  Future<bool> initialize() async {
    if (_isInitialized) return true;
    
    try {
      _fluwx = Fluwx();
      // 根据平台选择 App ID，Todo 这里暂时写死后续有参数再配置
      String appId = Platform.isAndroid ? "wx2d611131c6591acd" : "wx906417b1471f5485";
      // 注册微信 API
      bool result = await _fluwx.registerApi(
          appId: appId,
          universalLink: "https://applink.akbkgame.com/lhzj/"
      );
      
      if (result) {
        _responseSubscription = _fluwx.addSubscriber((response) {
          debugPrint('微信回调: $response');
          _handleWechatResponse(response);
        });
        debugPrint('微信SDK初始化成功');
        _isInitialized = true;
        return true;
      } else {
        debugPrint('微信SDK注册失败');
        return false;
      }
    } catch (e) {
      debugPrint('微信SDK初始化失败: $e');
      return false;
    }
  }

  /// 微信登录
  Future<Map<String, dynamic>> login({Function(Map<String, dynamic>)? callback}) async {
    if (!_isInitialized) {
      bool success = await initialize();
      if (!success) {
        return {
          'success': false,
          'message': '微信SDK初始化失败',
        };
      }
    }

    try {
      // 检查微信是否安装
      bool isInstalled = await isWechatInstalled();
      if (!isInstalled) {
        return {
          'success': false,
          'message': '请先安装微信',
        };
      }

      // 保存回调函数
      _loginCallback = callback;

      // 发起微信登录
      await _fluwx.authBy(which: NormalAuth(scope: "snsapi_userinfo", state: "wechat_sdk_demo"));
      debugPrint('发送微信登录请求');

      return {
        'success': true,
        'message': '微信登录请求已发送',
      };
    } catch (e) {
      return {
        'success': false,
        'message': '微信登录失败: $e',
      };
    }
  }

  /// 处理微信回调
  void _handleWechatResponse(dynamic response) {
    debugPrint('微信回调: $response');
    
    if (response != null) {
      if (response is WeChatAuthResponse) {
        if (response.errCode == 0) {
          _handleAuthSuccess(response.code);
        } else {
          String errorMsg = _getErrorMessage(response.errCode ?? -1);
          _handleAuthFailure(errorMsg);
        }
      }
    } else {
      _handleAuthFailure('回调数据为空');
    }
  }

  /// 获取错误信息
  String _getErrorMessage(int errCode) {
    switch (errCode) {
      case -1:
        return '微信授权失败';
      case -2:
        return '用户取消授权';
      case -3:
        return '发送请求失败';
      case -4:
        return '授权请求被拒绝';
      case -5:
        return '微信不支持';
      default:
        return '未知错误 (错误码: $errCode)';
    }
  }

  /// 处理授权成功
  void _handleAuthSuccess(String? code) {
    debugPrint('授权成功，code: $code');
    
    if (code == null || code.isEmpty) {
      _handleAuthFailure('授权码为空');
      return;
    }

    // 这里应该将code发送到服务器换取用户信息
    _processServerResponse(code);
  }

  /// 处理授权失败
  void _handleAuthFailure(String? error) {
    debugPrint('授权失败: $error');
    
    if (_loginCallback != null) {
      _loginCallback!({
        'success': false,
        'message': '微信授权失败: $error',
      });
    }
  }

  /// 处理服务器响应
  void _processServerResponse(String code) async {
    try {
      //Todo 这里应该发送code到服务器换取用户信息
      // 目前返回基本的成功响应
      await Future.delayed(const Duration(seconds: 1));
      
      final result = {
        'success': true,
        'message': '微信登录成功',
        'data': {
          'code': code,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      };
      
      if (_loginCallback != null) {
        _loginCallback!(result);
      }
    } catch (e) {
      debugPrint('服务器处理异常: $e');
      if (_loginCallback != null) {
        _loginCallback!({
          'success': false,
          'message': '服务器处理异常: $e',
        });
      }
    }
  }

  /// 检查微信是否安装
  Future<bool> isWechatInstalled() async {
    try {
      return await _fluwx.isWeChatInstalled;
    } catch (e) {
      debugPrint('检查微信安装状态失败: $e');
      return false;
    }
  }

  /// 处理微信回调（兼容方法）
  void handleWechatResponse(dynamic response) {
    _handleWechatResponse(response);
  }

  /// 清理资源
  void dispose() {
    _responseSubscription?.cancel();
    _responseSubscription = null;
    if (_isInitialized) {
      _fluwx.clearSubscribers();
    }
    _isInitialized = false;
  }
}