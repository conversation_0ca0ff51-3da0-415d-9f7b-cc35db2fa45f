import 'package:dlyz_flutter/config/app_config.dart';
import 'package:dlyz_flutter/net/sign_interceptor.dart';
import 'package:dlyz_flutter/utils/aes_utils.dart';

import '../net/http_service.dart';
import '../net/http_base_response.dart';
import '../model/login_info.dart';
import '../model/send_code_data.dart';
import '../model/check_code_data.dart';
import '../model/auth_app_item.dart';
import '../model/auth_app_status.dart';
import '../model/mini_program_info.dart';
import '../model/user_detail.dart';

/// 登录API服务类
class LoginApiService {
  static final LoginApiService _instance = LoginApiService._internal();
  factory LoginApiService() => _instance;
  LoginApiService._internal();

  HttpService get _httpService => HttpService.getInstance();
  
  // API接口配置
  static const String _baseUrl = 'http://gamehub-api.37.com.cn';
  static const String _validateBaseUrl = 'http://validate-api.37.com.cn';
  static const String _loginPath = '/api/gamehub-api/v1/login/account/pwd';
  static const String _mobilePasswordPath = '/api/gamehub-api/v1/login/mobile/pwd';
  static const String _fastLoginPath = '/api/gamehub-api/v1/login/shanyan/ali';
  static const String _mobileTicketPath = '/api/gamehub-api/v1/login/mobile/ticket';
  static const String _refreshTicketPath = '/api/gamehub-api/v1/login/ticket/refresh';
  static const String _authAppListPath = '/api/gamehub-api/v1/login/auth_app/get_list';
  static const String _authAppStatusPath = '/api/gamehub-api/v1/login/auth_app/status';
  static const String _authAppApplyPath = '/api/gamehub-api/v1/login/auth_app/apply';
  static const String _skipAppletInfoPath = '/api/mapi-service/v1/sdk/skip-applet-info';
  static const String _sendCodePath = '/app/phone/common/send_code';
  static const String _checkCodePath = '/app/phone/common/check_code';
  static const String _getUserBasicInfoPath = '/api/gamehub-api/v1/my_zone/get_basic_info';
  
  /// 手机号密码登录
  /// 
  /// [mobile] 手机号
  /// [password] 密码
  /// 返回 [Future<BaseResponse<LoginInfo>>] 登录结果
  Future<BaseResponse<LoginInfo>> loginWithMobilePassword({
    required String mobile,
    required String password,
  }) async {
    try {
      // 构建请求参数（按照接口文档要求）
      final Map<String, dynamic> params = {
        'mobile': mobile,
        'upwd': AesUtil.encrypt(password, AppConfig.appKey),
        'refer': 'app', // 来源标识
      };

      // 发起请求（使用v3签名）
      return await _httpService.post<LoginInfo>(
        _mobilePasswordPath,
        baseUrl: _baseUrl,
        data: params,
        contentType: ContentType.form,
        fromJsonT: (data) => LoginInfo.fromJson(data),
        signType: SignType.v3,
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 账号密码登录
  /// 
  /// [username] 用户名
  /// [password] 密码
  /// 返回 [Future<BaseResponse<LoginInfo>>] 登录结果
  Future<BaseResponse<LoginInfo>> loginWithPassword({
    required String username,
    required String password,
  }) async {
    try {
      // 构建请求参数（只需要用户名和密码，其他参数由拦截器自动添加）
      final Map<String, dynamic> params = {
        'muname': username,
        'upwd': AesUtil.encrypt(password, AppConfig.appKey),
      };

      // 发起请求（默认使用 StateMsgData 解析策略）
      return await _httpService.post<LoginInfo>(
        _loginPath,
        baseUrl: _baseUrl,
        data: params,
        contentType: ContentType.form,
        fromJsonT: (data) => LoginInfo.fromJson(data),
        signType: SignType.v3
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 闪验token换票据登录
  Future<BaseResponse<LoginInfo>> loginWithFastToken({
    required String token,
  }) async {
    try {
      final Map<String, dynamic> params = {
        'shan_yan_token': token,
      };

      return await _httpService.post<LoginInfo>(
        _fastLoginPath,
        baseUrl: _baseUrl,
        data: params,
        contentType: ContentType.form,
        fromJsonT: (data) => LoginInfo.fromJson(data),
        signType: SignType.v3,
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 刷新登录票据
  /// 
  /// [appTicket] 当前票据
  /// [appRefreshTicket] 刷新票据
  /// 返回 [Future<BaseResponse<LoginInfo>>]
  Future<BaseResponse<LoginInfo>> refreshTicket({
    required String appTicket,
    required String appRefreshTicket,
  }) async {
    try {
      final Map<String, dynamic> params = {
        'app_ticket': appTicket,
        'app_refresh_ticket': appRefreshTicket,
      };

      return await _httpService.post<LoginInfo>(
        _refreshTicketPath,
        baseUrl: _baseUrl,
        data: params,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) => LoginInfo.fromJson(data),
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 手机号票据登录
  /// 
  /// [ticket] 票据
  /// 返回 [Future<BaseResponse<LoginInfo>>] 登录结果
  Future<BaseResponse<LoginInfo>> loginWithMobileTicket({
    required String ticket,
    required String nonce,
  }) async {
    try {
      final Map<String, dynamic> params = {
        'nonce' : nonce,
        'ticket': ticket,
      };

      return await _httpService.post<LoginInfo>(
        _mobileTicketPath,
        baseUrl: _baseUrl,
        data: params,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) => LoginInfo.fromJson(data),
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 发送手机验证码
  /// 
  /// [scene] 场景：app_login_phone（斗罗App）
  /// [phone] 手机号
  /// 返回 [Future<BaseResponse<SendCodeData>>] 发送结果
  Future<BaseResponse<SendCodeData>> sendCode({
    required String phone,
  }) async {
    try {
      // 构建请求参数
      final Map<String, dynamic> params = {
        'scene': 'app_login_phone',
        'phone': phone,
      };
      // 发起请求（使用v3签名）
      return await _httpService.post<SendCodeData>(
        _sendCodePath,
        baseUrl: _validateBaseUrl,
        data: params,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) => SendCodeData.fromJson(data),
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 验证手机验证码
  /// 
  /// [scene] 场景：app_login_phone（斗罗App）
  /// [phone] 手机号
  /// [code] 验证码
  /// [nonce] 随机值（从发送验证码接口返回）
  /// 返回 [Future<BaseResponse<CheckCodeData>>] 验证结果
  Future<BaseResponse<CheckCodeData>> checkCode({
    required String phone,
    required String code,
    required String nonce,
  }) async {
    try {
      // 构建请求参数
      final Map<String, dynamic> params = {
        'scene': 'app_login_phone',
        'phone': phone,
        'code': code,
        'nonce': nonce,
      };

      // 发起请求
      return await _httpService.post<CheckCodeData>(
        _checkCodePath,
        baseUrl: _validateBaseUrl,
        data: params,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) => CheckCodeData.fromJson(data),
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 获取授权应用列表
  /// 
  /// 返回 [Future<BaseResponse<AuthAppListData>>] 授权应用列表
  Future<BaseResponse<AuthAppListData>> getAuthAppList() async {

    Map<String, dynamic> payload = {};
    try {
      // 发起请求（使用v3签名，其他参数由拦截器自动添加）
      return await _httpService.post<AuthAppListData>(
        _authAppListPath,
        baseUrl: _baseUrl,
        data: payload, // 空参数，所有必要参数由拦截器自动添加
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) => AuthAppListData.fromJson(data),
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 获取小程序跳转信息
  /// 
  /// [pid] 项目ID
  /// [gid] 游戏ID  
  /// [flagId] 唯一标识
  /// 返回 [Future<BaseResponse<MiniProgramInfo>>] 小程序跳转信息
  Future<BaseResponse<MiniProgramInfo>> getSkipAppletInfo({
    required int pid,
    required int gid,
    required int flagId,
  }) async {
    try {
      // 构建请求参数
      final Map<String, dynamic> params = {
        'pid': pid,
        'gid': gid,
        'flag_id': flagId
      };

      // 发起GET请求（使用v3签名）
      return await _httpService.get<MiniProgramInfo>(
        _skipAppletInfoPath,
        baseUrl: _baseUrl,
        queryParameters: params,
        signType: SignType.v3,
        fromJsonT: (data) => MiniProgramInfo.fromJson(data),
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 获取授权应用状态
  /// 
  /// [traceId] 追踪ID
  /// [nonce] 随机值
  /// 返回 [Future<BaseResponse<AuthAppStatusData>>] 授权应用状态
  Future<BaseResponse<AuthAppStatusData>> getAuthAppStatus({
    String? traceId,
    String? nonce,
  }) async {
    try {
      Map<String, dynamic> payload = {};
      
      // 添加trace_id和nonce参数
      if (traceId != null && traceId.isNotEmpty) {
        payload['trace_id'] = traceId;
      }
      if (nonce != null && nonce.isNotEmpty) {
        payload['nonce'] = nonce;
      }
      
      // 发起请求（使用v3签名，所有必要参数由拦截器自动添加）
      return await _httpService.post<AuthAppStatusData>(
        _authAppStatusPath,
        baseUrl: _baseUrl,
        data: payload,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) => AuthAppStatusData.fromJson(data),
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 获取用户基本信息
  /// 
  /// [appTicket] 应用票据，必需参数
  /// 返回 [Future<BaseResponse<UserDetail>>] 用户基本信息
  Future<BaseResponse<UserDetail>> getUserBasicInfo(String ticket) async {
    try {
      // 构建请求参数
      final Map<String, dynamic> params = {
        "app_ticket": ticket,
      };

      // 发起请求（使用v3签名）
      return await _httpService.post<UserDetail>(
        _getUserBasicInfoPath,
        baseUrl: _baseUrl,
        data: params,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) => UserDetail.fromJson(data),
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 申请授权应用
  Future<BaseResponse<AuthAppApplyData>> applyAuthApp({
    required String tgid,
  }) async {
    try {
      // 构建请求参数
      final Map<String, dynamic> params = {
        'tgid': tgid,
      };
      // 发起请求（使用v3签名）
      return await _httpService.post<AuthAppApplyData>(
        _authAppApplyPath,
        baseUrl: _baseUrl,
        data: params,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) => AuthAppApplyData.fromJson(data),
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }
}