import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../model/user.dart';

/// 用户数据持久化服务
class UserStorageService {
  static const String _userListKey = 'user_list';
  static const String _currentUserKey = 'current_user';
  
  static final UserStorageService _instance = UserStorageService._internal();
  factory UserStorageService() => _instance;
  UserStorageService._internal();

  /// 保存用户列表
  Future<void> saveUserList(List<User> userList) async {
    final prefs = await SharedPreferences.getInstance();
    
    // 按最后登录时间排序（最新的在前面）
    final sortedUserList = List<User>.from(userList);
    sortedUserList.sort((a, b) {
      final aLastLogin = a.loginInfo.lastLoginAt;
      final bLastLogin = b.loginInfo.lastLoginAt;
      
      // 如果都有登录时间，按时间倒序排列
      if (aLastLogin != null && bLastLogin != null) {
        return bLastLogin.compareTo(aLastLogin);
      }
      
      // 如果只有一个有登录时间，有登录时间的排在前面
      if (aLastLogin != null && bLastLogin == null) {
        return -1;
      }
      if (aLastLogin == null && bLastLogin != null) {
        return 1;
      }
      
      // 如果都没有登录时间，保持原有顺序
      return 0;
    });
    
    final userListJson = sortedUserList.map((user) => user.toJson()).toList();
    await prefs.setString(_userListKey, jsonEncode(userListJson));
  }

  /// 读取用户列表
  Future<List<User>> getUserList() async {
    final prefs = await SharedPreferences.getInstance();
    final userListString = prefs.getString(_userListKey);
    
    if (userListString == null || userListString.isEmpty) {
      return [];
    }
    
    try {
      final userListJson = jsonDecode(userListString) as List;
      return userListJson.map((json) => User.fromJson(json)).toList();
    } catch (e) {
      print('解析用户列表失败: $e');
      return [];
    }
  }

  /// 添加用户
  Future<void> addUser(User user) async {
    final userList = await getUserList();
    
    // 检查是否已存在相同用户ID的用户
    final existingIndex = userList.indexWhere((u) => u.muid == user.muid);
    if (existingIndex != -1) {
      // 更新现有用户
      userList[existingIndex] = user;
    } else {
      // 添加新用户
      userList.add(user);
    }
    
    // 保存时会自动按 lastLoginAt 排序
    await saveUserList(userList);
  }

  /// 删除用户
  Future<void> deleteUser(User user) async {
    final userList = await getUserList();
    userList.removeWhere((u) => u.muid == user.muid);
    await saveUserList(userList);
    
    // 如果删除的是当前用户，清除当前用户状态
    final currentUser = await getCurrentUser();
    if (currentUser?.muid == user.muid) {
      await saveCurrentUser(null);
    }
  }

  /// 清除所有用户数据
  Future<void> clearAllUsers() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userListKey);
    await prefs.remove(_currentUserKey);
  }

  /// 保存当前用户
  Future<void> saveCurrentUser(User? user) async {
    final prefs = await SharedPreferences.getInstance();
    if (user == null) {
      await prefs.remove(_currentUserKey);
    } else {
      await prefs.setString(_currentUserKey, jsonEncode(user.toJson()));
    }
  }

  /// 获取当前用户
  Future<User?> getCurrentUser() async {
    final prefs = await SharedPreferences.getInstance();
    final currentUserString = prefs.getString(_currentUserKey);
    
    if (currentUserString == null || currentUserString.isEmpty) {
      return null;
    }
    
    try {
      final userJson = jsonDecode(currentUserString);
      return User.fromJson(userJson);
    } catch (e) {
      print('解析当前用户失败: $e');
      return null;
    }
  }

  /// 获取用户数量
  Future<int> getUserCount() async {
    final userList = await getUserList();
    return userList.length;
  }

  /// 检查是否存在用户
  Future<bool> hasUsers() async {
    final userList = await getUserList();
    return userList.isNotEmpty;
  }

  /// 获取按登录时间排序的用户列表（用于调试）
  Future<List<User>> getSortedUserList() async {
    return await getUserList(); // 已经自动排序
  }
} 