abstract class ALDownloaderConstant {
  static final kInitiallize = 'initialize';
  static final kDownload = 'download';
  static final kHandlerInterfaceId = 'handlerInterfaceId';
  static final kHeaders = 'headers';
  static final kUrl = 'url';
  static final kUrls = 'urls';
  static final kAddHandlerInterface = 'addHandlerInterface';
  static final kAddForeverHandlerInterface = 'addForeverHandlerInterface';
  static final kRemoveHandlerInterfaceForUrl = 'removeHandlerInterfaceForUrl';
  static final kRemoveHandlerInterfaceForAll = 'removeHandlerInterfaceForAll';
  static final kPause = 'pause';
  static final kPauseAll = 'pauseAll';
  static final kCancel = 'cancel';
  static final kCancelAll = 'cancelAll';
  static final kRemove = 'remove';
  static final kRemoveAll = 'removeAll';
  static final kRootIsolateToken = 'rootIsolateToken';
  static final kStoragePortRootToAL = 'storagePortRootToAL';
  static final kPortALToRoot = 'portALToRoot';
  static final kPrintEnabled = 'printEnabled';
  static final kFrequentPrintEnabled = 'frequentPrintEnabled';
  static final kTask = 'kTask';
  static final kCallInterface = 'callInterface';
  static final kIsNeedCallProgressHandler = 'isNeedCallProgressHandler';
  static final kIsNeedCallSucceededHandler = 'isNeedCallSucceededHandler';
  static final kIsNeedCallFailedHandler = 'isNeedCallFailedHandler';
  static final kIsNeedCallPausedHandler = 'isNeedCallPausedHandler';
  static final kStatus = 'status';
  static final kProgress = 'progress';
  static final kSpeed = 'speed';
  static final kIsNeedRemoveInterface = 'isNeedRemoveInterface';
  static final kConfigurePrint = 'configurePrint';
  static final kEnabled = 'enabled';
  static final kFrequentEnabled = 'frequentEnabled';
  static final kALDownloaderIMP = 'ALDownloaderIMP';
  static final kALDownloaderBatcherIMP = 'ALDownloaderBatcherIMP';
  static final kDownloadUrlsWithVOs = 'downloadUrlsWithVOs';
  static final kALDownloaderBatcherInputVOs = 'ALDownloaderBatcherInputVOs';
  static final kRemoveHandlerInterfaceForId = 'removeHandlerInterfaceForId';
  static final kGlobalScope = 'globalScope';
  static final kGetStatusForUrl = 'getStatusForUrl';
  static final kGetStatusForUrls = 'getStatusForUrls';
  static final kCallStatusHandler = 'callStatusHandler';
  static final kGetProgressForUrl = 'getProgressForUrl';
  static final kGetProgressForUrls = 'getProgressForUrls';
  static final kCallProgressHandler = 'callProgressHandler';
  static final kALDownloaderFileManagerIMP = 'ALDownloaderFileManagerIMP';
  static final kGetPhysicalFileForUrl = 'getPhysicalFileForUrl';
  static final kGetPhysicalFilePathForUrl = 'getPhysicalFilePathForUrl';
  static final kGetVirtualFilePathForUrl = 'getVirtualFilePathForUrl';
  static final kData = 'data';
  static final kType = 'type';
  static final kCallFileManagerHandler = 'callFileManagerHandler';
  static final kHandlerId = 'handlerId';
  static final kId = 'id';
  static final kIsExistPhysicalFilePathForUrl = 'isExistPhysicalFilePathForUrl';
  static final kDirectoryPath = 'directoryPath';
  static final kFileName = 'fileName';
  static final kRedownloadIfNeeded = 'redownloadIfNeeded';
  static final kGetTask = 'getTask';
  static final kGetTasks = 'getTasks';
  static final kTasks = 'tasks';
  static final kCallTaskHandler = 'callTaskHandler';
  static final kCallTasksHandler = 'callTasksHandler';
  static final kByQueueOrder = 'byQueueOrder';
}
