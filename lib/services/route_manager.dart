import 'package:flutter/material.dart';
import '../main.dart';
import 'app_route_manager.dart';

/// 路由管理器
/// 统一管理应用的路由跳转逻辑，特别是登录相关的路由控制
class RouteManager {
  /// 跳转到登录页（委托到新方案）
  static void navigateToLogin(BuildContext context) {
    AppRouteManager.navigateToLogin(context);
  }

  /// 跳转到主页
  /// 使用pushReplacement替换当前页面，保留返回栈
  static void navigateToHome(BuildContext context) {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (context) {
        return const MyHomePage();
      }),
    );
  }

  /// 登录成功后的跳转（委托到新方案）
  static void navigateAfterLogin(BuildContext context) {
    AppRouteManager.navigateAfterLogin(context);
  }

  /// 退出登录后的跳转（委托到新方案）
  static void navigateAfterLogout(BuildContext context) {
    AppRouteManager.navigateAfterLogout(context);
  }

  /// 统一登录页面显示（委托到新方案）
  static Future<bool?> showLoginPage(
    BuildContext context, {
    VoidCallback? onLoginSuccess,
  }) async {
    return AppRouteManager.showLoginModal(
      context,
      onLoginSuccess: onLoginSuccess,
    );
  }
}