# ThinkingData SDK - ES Module Version

This document explains the conversion of the ThinkingData SDK from UMD/IIFE format to ES Module standard.

## Files

- `sdk-MIX-KS.js` - Original minified UMD/IIFE version
- `sdk-MIX-KS.esm.js` - New ES Module version
- `sdk-usage-example.js` - Usage examples for the ES Module
- `README-ES-Module.md` - This documentation

## What Changed

### 1. Module Format Conversion
- **Before**: UMD/IIFE (Immediately Invoked Function Expression)
- **After**: ES Modules with `import`/`export` statements

### 2. Code Structure
- **Before**: Minified, single-line code
- **After**: Properly formatted, readable code with comments

### 3. Exports
The ES module provides multiple export options:

```javascript
// Named exports
export { ThinkingDataAPI, PlatformProxy, PropertyChecker, Config, logger, _ };

// Default export
export default ThinkingDataAPI;

// Factory function
export function initKuaiShouSDK(config);
```

## Usage

### Basic Import and Usage

```javascript
// Import the main class
import { ThinkingDataAPI } from './sdk-MIX-KS.esm.js';

// Create instance
const ta = new ThinkingDataAPI({
  appId: "your_app_id",
  serverUrl: "https://your-server.com"
});

// Initialize and track events
ta.init();
ta.track("page_view", { page: "home" });
```

### KuaiShou Mini Game Integration

```javascript
// Import the initialization function
import { initKuaiShouSDK } from './sdk-MIX-KS.esm.js';

// Initialize for KuaiShou environment
const ta = initKuaiShouSDK({
  appId: "your_app_id",
  serverUrl: "https://your-server.com",
  enableLog: true
});

// Track events
ta.track("game_start", { level: 1 });
```

### Advanced Usage

```javascript
// Import specific utilities
import { PropertyChecker, logger, Config } from './sdk-MIX-KS.esm.js';

// Use utilities independently
if (PropertyChecker.event("my_event")) {
  logger.info("Event name is valid");
}

console.log("SDK Version:", Config.LIB_VERSION);
```

## Benefits of ES Module Version

### 1. Tree Shaking
- Bundlers can eliminate unused code
- Smaller bundle sizes in production

### 2. Better Development Experience
- IDE autocomplete and IntelliSense support
- Static analysis and type checking
- Better debugging with source maps

### 3. Modern JavaScript Support
- Compatible with modern build tools (Webpack, Rollup, Vite)
- Works with TypeScript projects
- Supports dynamic imports

### 4. Maintainability
- Readable, formatted code
- Clear module boundaries
- Easier to extend and modify

## Migration Guide

### From Original SDK

**Before (UMD/IIFE):**
```javascript
// SDK was loaded globally
var ta = new ThinkingDataAPI(config);
```

**After (ES Module):**
```javascript
// Import explicitly
import { ThinkingDataAPI } from './sdk-MIX-KS.esm.js';
const ta = new ThinkingDataAPI(config);
```

### Build Tool Configuration

#### Webpack
```javascript
// webpack.config.js
module.exports = {
  resolve: {
    alias: {
      'thinking-data': './lib/pages/privacy/sdk-MIX-KS.esm.js'
    }
  }
};
```

#### Rollup
```javascript
// rollup.config.js
export default {
  input: 'src/main.js',
  external: ['./sdk-MIX-KS.esm.js'],
  // ... other config
};
```

## API Reference

### Main Classes

- `ThinkingDataAPI` - Main SDK class
- `PlatformProxy` - Platform-specific implementations
- `PropertyChecker` - Data validation utilities

### Configuration

```javascript
const config = {
  appId: "string",           // Required: Your app ID
  serverUrl: "string",       // Required: Data collection server URL
  name: "string",            // Optional: Instance name (default: "thinkingdata")
  enableLog: boolean,        // Optional: Enable logging (default: true)
  debugMode: "string",       // Optional: "none", "debug", "debugOnly"
  maxRetries: number,        // Optional: Max retry attempts (default: 3)
  sendTimeout: number,       // Optional: Request timeout in ms (default: 3000)
  enablePersistence: boolean, // Optional: Enable local storage (default: true)
  strict: boolean            // Optional: Enable strict validation (default: false)
};
```

### Methods

#### Event Tracking
```javascript
ta.track(eventName, properties, time, callback);
ta.track({ eventName, properties, time, onComplete });
```

#### User Profile
```javascript
ta.userSet(properties, time, callback);
ta.userSetOnce(properties, time, callback);
```

#### Utility
```javascript
ta.init();           // Initialize SDK
ta.flush();          // Force send queued events
ta.getDeviceId();    // Get device identifier
```

## Browser Compatibility

The ES module version maintains the same browser compatibility as the original:
- Modern browsers with ES6+ support
- KuaiShou Mini Game environment
- Node.js environments (for server-side usage)

## Notes

1. The ES module version preserves all original functionality
2. API methods remain unchanged for backward compatibility
3. The code is now more maintainable and debuggable
4. Better integration with modern JavaScript toolchains
5. Supports both named and default imports for flexibility

For more examples, see `sdk-usage-example.js`.
