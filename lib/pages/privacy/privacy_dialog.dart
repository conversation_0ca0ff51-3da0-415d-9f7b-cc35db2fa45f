import 'package:dlyz_flutter/utils/log_util.dart';
import 'package:dlyz_flutter/utils/permission_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../common/dl_color.dart';
import '../../webview/webview_dialog.dart';
import '../../webview/webview_page.dart';
import 'privacy_manager.dart';

// 确认弹窗组件
class ConfirmExitDialog extends StatelessWidget {
  final String userAgreementUrl;
  final String privacyPolicyUrl;

  const ConfirmExitDialog({super.key, this.userAgreementUrl = '', this.privacyPolicyUrl = ''});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(horizontal: 20,vertical: 20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            Container(
              width: double.infinity,
              padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
              child: const Text(
                '温馨提示',
                style: TextStyle(
                  fontSize: 17,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            // 内容
            Padding(
              padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
              child: RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                    height: 1.4,
                  ),
                  children: [
                    const TextSpan(text: '不授权同意'),
                    WidgetSpan(
                      child: GestureDetector(
                        onTap: () => _openWebView(
                          context,
                          '用户协议',
                          userAgreementUrl.isNotEmpty
                              ? userAgreementUrl
                              : 'https://example.com/user-agreement',
                        ),
                        child: const Text(
                          '《用户协议》',
                          style: TextStyle(
                            color: Colors.blue,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ),
                    const TextSpan(text: '和'),
                    WidgetSpan(
                      child: GestureDetector(
                        onTap: () => _openWebView(
                          context,
                          '隐私政策',
                          privacyPolicyUrl.isNotEmpty
                              ? privacyPolicyUrl
                              : 'https://example.com/privacy-policy',
                        ),
                        child: const Text(
                          '《隐私政策》',
                          style: TextStyle(
                            color: Colors.blue,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ),
                    const TextSpan(text: '将无法进行游戏'),
                  ],
                ),
              ),
            ),

            // 副标题 - 合并为一行
            Padding(
              padding: const EdgeInsets.fromLTRB(15, 0, 15, 5),
              child: RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  style: const TextStyle(fontSize: 14, height: 1.4),
                  children: [
                    const TextSpan(
                      text: '是否确认',
                      style: TextStyle(color: Colors.black87),
                    ),
                    TextSpan(
                      text: '取消授权并退出游戏',
                      style: TextStyle(color: Colors.red[600]),
                    ),
                  ],
                ),
              ),
            ),

            // 底部按钮
            Container(
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFEEEEEE), width: 1),
                ),
              ),
              child: Row(
                children: [
                  // 取消授权按钮
                  Expanded(
                    child: TextButton(
                      onPressed: () {
                        Navigator.of(context).pop(false);
                        // 延迟退出应用，让弹窗先关闭
                        Future.delayed(const Duration(milliseconds: 100), () {
                          SystemNavigator.pop();
                        });
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.zero,
                        ),
                      ),
                      child: const Text(
                        '取消授权',
                        style: TextStyle(
                          color: Colors.black87,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),

                  // 中间分割线
                  Container(
                    width: 1,
                    height: 48,
                    color: const Color(0xFFEEEEEE),
                  ),

                  // 我再想想按钮
                  Expanded(
                    child: TextButton(
                      onPressed: () {
                        Navigator.of(context).pop(true); // 关闭确认弹窗，返回隐私协议弹窗
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.zero,
                        ),
                      ),
                      child: const Text(
                        '我再想想',
                        style: TextStyle(
                          color: DLColor.primary,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 打开WebView
  void _openWebView(BuildContext context, String title, String url) {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.transparent,
      builder: (context) {
        return WebViewDialog(url: url, title: title, showToolBar: false);
      },
    );
  }
}

class PrivacyDialog extends StatelessWidget {
  final String userAgreementUrl;
  final String privacyPolicyUrl;

  const PrivacyDialog({
    super.key,
    this.userAgreementUrl = '',
    this.privacyPolicyUrl = '',
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: const BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1),
                  ),
                ),
                child: const Text(
                  '个人信息保护指引',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              // 内容区域
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 欢迎信息
                      RichText(
                        text: TextSpan(
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.black87,
                            height: 1.5,
                          ),
                          children: [
                            const TextSpan(
                              text:
                                  '欢迎下载本应用,我们非常重视个人信息和隐私保护。请在使用我们的服务前,详细阅读并同意',
                            ),
                            WidgetSpan(
                              child: GestureDetector(
                                onTap:
                                    () => _openWebView(
                                      context,
                                      '用户协议',
                                      userAgreementUrl.isNotEmpty
                                          ? userAgreementUrl
                                          : 'https://example.com/user-agreement',
                                    ),
                                child: const Text(
                                  '《用户协议》',
                                  style: TextStyle(
                                    color: Colors.blue,
                                  ),
                                ),
                              ),
                            ),
                            const TextSpan(text: '和'),
                            WidgetSpan(
                              child: GestureDetector(
                                onTap:
                                    () => _openWebView(
                                      context,
                                      '隐私政策',
                                      privacyPolicyUrl.isNotEmpty
                                          ? privacyPolicyUrl
                                          : 'https://example.com/privacy-policy',
                                    ),
                                child: const Text(
                                  '《隐私政策》',
                                  style: TextStyle(
                                    color: Colors.blue,
                                  ),
                                ),
                              ),
                            ),
                            const TextSpan(text: '。'),
                          ],
                        ),
                      ),

                      const SizedBox(height: 16),

                      // 权限说明
                      const Text(
                        '为了提供完整的应用体验,我们会向您申请必要的权限和信息。您可选择同意或拒绝权限申请，如果拒绝可能会导致部分应用体验异常。',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                          height: 1.5,
                        ),
                      ),

                      const SizedBox(height: 10),

                      // // 权限列表
                      // _buildPermissionItem(
                      //   '存储权限:',
                      //   '为实现查询历史账号功能,保存玩家登录信息,及图片的保存与分享。',
                      // ),
                      //
                      // const SizedBox(height: 10),
                      //
                      // _buildPermissionItem('通知权限:', '为实现游戏攻略等信息推送。'),
                    ],
                  ),
                ),
              ),

              // 底部按钮
              Container(
                decoration: const BoxDecoration(
                  border: Border(
                    top: BorderSide(color: Color(0xFFEEEEEE), width: 1),
                  ),
                ),
                child: Row(
                  children: [
                    // 不同意按钮
                    Expanded(
                      child: TextButton(
                        onPressed: () => _disagree(context),
                        style: TextButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.zero,
                          ),
                        ),
                        child: const Text(
                          '不同意',
                          style: TextStyle(color: DLColor.textFourth, fontSize: 16),
                        ),
                      ),
                    ),

                    // 中间分割线
                    Container(
                      width: 1,
                      height: 54,
                      color: const Color(0xFFEEEEEE),
                    ),

                    // 同意按钮
                    Expanded(
                      child: TextButton(
                        onPressed: () => _agree(context),
                        style: TextButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.zero,
                          ),
                        ),
                        child: const Text(
                          '同意',
                          style: TextStyle(color: DLColor.primary, fontSize: 16),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建权限项
  Widget _buildPermissionItem(String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          content,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.black54,
            height: 1.4,
          ),
        ),
      ],
    );
  }

  // 打开WebView
  void _openWebView(BuildContext context, String title, String url) {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.transparent,
      builder: (context) {
        return WebViewDialog(url: url, title: title, showToolBar: false);
      },
    );
  }

  // 同意处理
  void _agree(BuildContext context) {
    // 保存用户同意状态并关闭弹窗
    Navigator.of(context).pop(true);
    _requestPermission();
  }

  // 不同意处理
  void _disagree(BuildContext context) async {
    // 显示确认弹窗，不关闭当前隐私弹窗
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return ConfirmExitDialog(
          userAgreementUrl: userAgreementUrl,
          privacyPolicyUrl: privacyPolicyUrl,
        );
      },
    );

    // 根据确认弹窗的结果处理
    if (result == false) {
      // 用户选择"取消授权"，关闭隐私弹窗并退出应用
      Navigator.of(context).pop(false);
    }
    // 如果用户选择"我再想想"（result == true），什么都不做，继续显示当前隐私弹窗
  }

  void _requestPermission() async {

    // 在用户同意个人信息保护指引后，请求广告追踪权限
    try {
      final status = await PermissionUtils.requestTrackingPermission();
      LogUtil.d('广告追踪权限申请结果: $status');

      // 可以根据权限状态进行相应的处理
      switch (status) {
        case "authorized":
          LogUtil.d('用户已授权广告追踪权限');
          break;
        case "denied":
          LogUtil.d('用户已拒绝广告追踪权限');
          break;
        case "restricted":
          LogUtil.d('广告追踪权限受限制');
          break;
        case "notDetermined":
          LogUtil.d('广告追踪权限未确定');
          break;
        case "not_supported":
          LogUtil.d('当前设备不支持广告追踪权限');
          break;
        case "error":
          LogUtil.d('广告追踪权限申请失败');
          break;
        default:
          LogUtil.d('未知的广告追踪权限状态: $status');
      }
    } catch (e) {
      LogUtil.e('请求广告追踪权限时发生错误: $e');
    }
  }
}
