/**
 * Example usage of the ES Module version of ThinkingData SDK for KuaiShou Mini Game
 */

// Import the SDK using ES module syntax
import { 
  ThinkingDataAPI, 
  initKuaiShouSDK, 
  DEFAULT_CONFIG,
  logger 
} from './sdk-MIX-KS.esm.js';

// Alternative import methods:

// 1. Import the default export
// import ThinkingDataAPI from './sdk-MIX-KS.esm.js';

// 2. Import specific functions
// import { createThinkingDataInstance, Config } from './sdk-MIX-KS.esm.js';

// 3. Import everything
// import * as ThinkingData from './sdk-MIX-KS.esm.js';

// Example configuration
const sdkConfig = {
  appId: "your_app_id_here",
  serverUrl: "https://your-server-url.com",
  enableLog: true,
  debugMode: "debug", // "none", "debug", "debugOnly"
  enablePersistence: true,
  maxRetries: 3,
  sendTimeout: 3000
};

// Method 1: Using the initialization function (recommended for KuaiShou)
const ta = initKuaiShouSDK(sdkConfig);

if (ta) {
  // Track events
  ta.track("page_view", {
    page_name: "home",
    page_title: "首页"
  });

  // Track with callback
  ta.track({
    eventName: "button_click",
    properties: {
      button_name: "login",
      button_position: "header"
    },
    onComplete: function(result) {
      console.log("Event tracked:", result);
    }
  });

  // User profile operations
  ta.userSet({
    properties: {
      user_name: "张三",
      user_level: 5,
      vip_status: true
    },
    onComplete: function(result) {
      console.log("User properties set:", result);
    }
  });

  ta.userSetOnce({
    properties: {
      first_login_date: new Date(),
      registration_channel: "kuaishou"
    }
  });
}

// Method 2: Direct instantiation
const ta2 = new ThinkingDataAPI(sdkConfig);
ta2.init();

// Track events with the second instance
ta2.track("app_launch", {
  launch_source: "icon",
  app_version: "1.0.0"
});

// Method 3: Using in KuaiShou Mini Game lifecycle
// In app.js or game.js
App({
  onLaunch: function(options) {
    // Initialize ThinkingData
    const ta = initKuaiShouSDK({
      appId: "your_app_id",
      serverUrl: "https://your-server.com",
      enableLog: true
    });

    // Store globally for use in other files
    this.globalData.ta = ta;

    // Track app launch
    if (ta) {
      ta.track("app_launch", {
        scene: options.scene,
        path: options.path,
        query: options.query
      });
    }
  },

  onShow: function(options) {
    // Track app show
    if (this.globalData.ta) {
      this.globalData.ta.track("app_show", {
        scene: options.scene
      });
    }
  },

  onHide: function() {
    // Track app hide and flush events
    if (this.globalData.ta) {
      this.globalData.ta.track("app_hide");
      this.globalData.ta.flush();
    }
  },

  globalData: {
    ta: null
  }
});

// Usage in other files (pages/components)
// Get the global instance
const app = getApp();
const ta = app.globalData.ta;

if (ta) {
  // Track page events
  ta.track("page_view", {
    page_name: "game_level_1"
  });

  // Track user actions
  ta.track("game_action", {
    action_type: "jump",
    level: 1,
    score: 100
  });
}

// Error handling example
try {
  const ta = initKuaiShouSDK(sdkConfig);
  ta.track("test_event", { test: true });
} catch (error) {
  console.error("ThinkingData SDK error:", error);
}

// Advanced usage with custom properties
const ta3 = new ThinkingDataAPI({
  ...sdkConfig,
  strict: true, // Enable strict mode for validation
  enableBatch: true, // Enable batch sending
  batchConfig: {
    size: 10,
    interval: 5000
  }
});

ta3.init();

// The ES module provides better tree-shaking and bundling support
// You can import only what you need:
// import { PropertyChecker, logger } from './sdk-MIX-KS.esm.js';

export { ta, ta2, ta3 };
