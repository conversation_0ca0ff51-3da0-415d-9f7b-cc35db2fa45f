"use strict";function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),e}var Config={LIB_VERSION:"3.0.3.1",LIB_NAME:"MG"},_={},ArrayProto=Array.prototype,ObjProto=Object.prototype,slice=ArrayProto.slice,nativeToString=ObjProto.toString,nativeHasOwnProperty=Object.prototype.hasOwnProperty,nativeForEach=ArrayProto.forEach,nativeIsArray=Array.isArray,breaker={},utmTypes=["utm_source","utm_medium","utm_campaign","utm_content","utm_term"];_.each=function(e,t,n){if(null==e)return!1;if(nativeForEach&&e.forEach===nativeForEach)e.forEach(t,n);else if(e.length===+e.length){for(var i=0,a=e.length;i<a;i++)if(i in e&&t.call(n,e[i],i,e)===breaker)return!1}else for(var r in e)if(nativeHasOwnProperty.call(e,r)&&t.call(n,e[r],r,e)===breaker)return!1},_.extend=function(n){return _.each(slice.call(arguments,1),function(e){for(var t in e)void 0!==e[t]&&(n[t]=e[t])}),n},_.extend2Layers=function(n){return _.each(slice.call(arguments,1),function(e){for(var t in e)void 0!==e[t]&&(_.isObject(e[t])&&_.isObject(n[t])?_.extend(n[t],e[t]):n[t]=e[t])}),n},_.isArray=nativeIsArray||function(e){return"[object Array]"===nativeToString.call(e)},_.isFunction=function(e){try{return"function"==typeof e}catch(e){return!1}},_.isPromise=function(e){return"[object Promise]"===nativeToString.call(e)&&null!=e},_.isObject=function(e){return"[object Object]"===nativeToString.call(e)&&null!=e},_.isEmptyObject=function(e){if(_.isObject(e)){for(var t in e)if(nativeHasOwnProperty.call(e,t))return!1;return!0}return!1},_.isUndefined=function(e){return void 0===e},_.isString=function(e){return"[object String]"===nativeToString.call(e)},_.isDate=function(e){return"[object Date]"===nativeToString.call(e)},_.isBoolean=function(e){return"[object Boolean]"===nativeToString.call(e)},_.isNumber=function(e){return"[object Number]"===nativeToString.call(e)&&/[\d\.]+/.test(String(e))},_.isJSONString=function(e){try{JSON.parse(e)}catch(e){return!1}return!0},_.decodeURIComponent=function(t){var n="";try{n=decodeURIComponent(t)}catch(e){n=t}return n},_.encodeURIComponent=function(t){var n="";try{n=encodeURIComponent(t)}catch(e){n=t}return n},_.utf8Encode=function(e){for(var t,n="",i=t=0,a=(e=(e+"").replace(/\r\n/g,"\n").replace(/\r/g,"\n")).length,r=0;r<a;r++){var s=e.charCodeAt(r),o=null;s<128?t++:o=127<s&&s<2048?String.fromCharCode(s>>6|192,63&s|128):String.fromCharCode(s>>12|224,s>>6&63|128,63&s|128),null!==o&&(i<t&&(n+=e.substring(i,t)),n+=o,i=t=r+1)}return i<t&&(n+=e.substring(i,e.length)),n},_.base64Encode=function(e){var t,n,i,a,r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",s=0,o=0,c="",u=[];if(!e)return e;for(e=_.utf8Encode(e);t=(a=e.charCodeAt(s++)<<16|e.charCodeAt(s++)<<8|e.charCodeAt(s++))>>18&63,n=a>>12&63,i=a>>6&63,a=63&a,u[o++]=r.charAt(t)+r.charAt(n)+r.charAt(i)+r.charAt(a),s<e.length;);switch(c=u.join(""),e.length%3){case 1:c=c.slice(0,-2)+"==";break;case 2:c=c.slice(0,-1)+"="}return c},_.encodeDates=function(i){return _.each(i,function(e,t){if(_.isDate(e))i[t]=_.formatDate(e);else if(_.isObject(e))i[t]=_.encodeDates(e);else if(_.isArray(e))for(var n=0;n<e.length;n++)_.isDate(e[n])&&(i[t][n]=_.formatDate(e[n]))}),i},_.formatDate=function(e){function t(e){return e<10?"0"+e:e}return e.getFullYear()+"-"+t(e.getMonth()+1)+"-"+t(e.getDate())+" "+t(e.getHours())+":"+t(e.getMinutes())+":"+t(e.getSeconds())+"."+((n=e.getMilliseconds())<100&&9<n?"0"+n:n<10?"00"+n:n);var n},_.formatTimeZone=function(e,t){if("number"!=typeof t)return e;var n=e.getTime(),e=6e4*e.getTimezoneOffset();return new Date(n+e+36e5*t)},_.getTimeZone=function(e,t){return"number"==typeof t?t:0-e.getTimezoneOffset()/60},_.searchObjDate=function(n,i){try{(_.isObject(n)||_.isArray(n))&&_.each(n,function(e,t){_.isObject(e)||_.isArray(e)?_.searchObjDate(n[t],i):_.isDate(e)&&(n[t]=_.formatDate(_.formatTimeZone(e,i)))})}catch(e){logger.warn(e)}},_.UUID=function(){var e=(new Date).getTime();return String(Math.random()).replace(".","").slice(1,11)+"-"+e},_.UUIDv4=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})},_.setMpPlatform=function(e){_.mpPlatform=e},_.getMpPlatform=function(){return _.mpPlatform},_.createExtraHeaders=function(){return{"TA-Integration-Type":Config.LIB_NAME,"TA-Integration-Version":Config.LIB_VERSION,"TA-Integration-Count":"1","TA-Integration-Extra":_.getMpPlatform()}},_.checkAppId=function(e){return e=e.replace(/\s*/g,"")},_.checkUrl=function(e){return e=e.replace(/\s*/g,""),e=_.url("basic",e)},_.url=function(){function a(){return new RegExp(/(.*?)\.?([^.]*?)\.(com|net|org|biz|ws|in|me|co\.uk|co|org\.uk|ltd\.uk|plc\.uk|me\.uk|edu|mil|br\.com|cn\.com|eu\.com|hu\.com|no\.com|qc\.com|sa\.com|se\.com|se\.net|us\.com|uy\.com|ac|co\.ac|gv\.ac|or\.ac|ac\.ac|af|am|as|at|ac\.at|co\.at|gv\.at|or\.at|asn\.au|com\.au|edu\.au|org\.au|net\.au|id\.au|be|ac\.be|adm\.br|adv\.br|am\.br|arq\.br|art\.br|bio\.br|cng\.br|cnt\.br|com\.br|ecn\.br|eng\.br|esp\.br|etc\.br|eti\.br|fm\.br|fot\.br|fst\.br|g12\.br|gov\.br|ind\.br|inf\.br|jor\.br|lel\.br|med\.br|mil\.br|net\.br|nom\.br|ntr\.br|odo\.br|org\.br|ppg\.br|pro\.br|psc\.br|psi\.br|rec\.br|slg\.br|tmp\.br|tur\.br|tv\.br|vet\.br|zlg\.br|br|ab\.ca|bc\.ca|mb\.ca|nb\.ca|nf\.ca|ns\.ca|nt\.ca|on\.ca|pe\.ca|qc\.ca|sk\.ca|yk\.ca|ca|cc|ac\.cn|net\.cn|com\.cn|edu\.cn|gov\.cn|org\.cn|bj\.cn|sh\.cn|tj\.cn|cq\.cn|he\.cn|nm\.cn|ln\.cn|jl\.cn|hl\.cn|js\.cn|zj\.cn|ah\.cn|gd\.cn|gx\.cn|hi\.cn|sc\.cn|gz\.cn|yn\.cn|xz\.cn|sn\.cn|gs\.cn|qh\.cn|nx\.cn|xj\.cn|tw\.cn|hk\.cn|mo\.cn|cn|cx|cz|de|dk|fo|com\.ec|tm\.fr|com\.fr|asso\.fr|presse\.fr|fr|gf|gs|co\.il|net\.il|ac\.il|k12\.il|gov\.il|muni\.il|ac\.in|co\.in|org\.in|ernet\.in|gov\.in|net\.in|res\.in|is|it|ac\.jp|co\.jp|go\.jp|or\.jp|ne\.jp|ac\.kr|co\.kr|go\.kr|ne\.kr|nm\.kr|or\.kr|li|lt|lu|asso\.mc|tm\.mc|com\.mm|org\.mm|net\.mm|edu\.mm|gov\.mm|ms|nl|no|nu|pl|ro|org\.ro|store\.ro|tm\.ro|firm\.ro|www\.ro|arts\.ro|rec\.ro|info\.ro|nom\.ro|nt\.ro|se|si|com\.sg|org\.sg|net\.sg|gov\.sg|sk|st|tf|ac\.th|co\.th|go\.th|mi\.th|net\.th|or\.th|tm|to|com\.tr|edu\.tr|gov\.tr|k12\.tr|net\.tr|org\.tr|com\.tw|org\.tw|net\.tw|ac\.uk|uk\.com|uk\.net|gb\.com|gb\.net|vg|sh|kz|ch|info|ua|gov|name|pro|ie|hk|com\.hk|org\.hk|net\.hk|edu\.hk|us|tk|cd|by|ad|lv|eu\.lv|bz|es|jp|cl|ag|mobi|eu|co\.nz|org\.nz|net\.nz|maori\.nz|iwi\.nz|io|la|md|sc|sg|vc|tw|travel|my|se|tv|pt|com\.pt|edu\.pt|asia|fi|com\.ve|net\.ve|fi|org\.ve|web\.ve|info\.ve|co\.ve|tel|im|gr|ru|net\.ru|org\.ru|hr|com\.hr|ly|xyz)$/)}function r(e,t){var n=e.charAt(0),t=t.split(n);return n===e?t:t[(e=parseInt(e.substring(1),10))<0?t.length+e:e-1]}function s(e,t){for(var n,i,a=e.charAt(0),r=t.split("&"),s=[],o={},c=e.substring(1),u=0,h=r.length;u<h;u++)if(""!==(s=(s=r[u].match(/(.*?)=(.*)/))||[r[u],r[u],""])[1].replace(/\s/g,"")){if(s[2]=(i=s[2]||"",_.decodeURIComponent(i.replace(/\+/g," "))),c===s[1])return s[2];(n=s[1].match(/(.*)\[([0-9]+)\]/))?(o[n[1]]=o[n[1]]||[],o[n[1]][n[2]]=s[2]):o[s[1]]=s[2]}return a===e?o:o[c]}return function(e,t){var n={};if("tld?"===e)return a();if(t=t||window.location.toString(),!e)return t;if(e=e.toString(),t.match(/^mailto:([^/].+)/))i=t.match(/^mailto:([^/].+)/),n.protocol="mailto",n.email=i[1];else{if(t.match(/(.*?)\/#!(.*)/)&&(t=(i=t.match(/(.*?)\/#!(.*)/))[1]+i[2]),t.match(/(.*?)#(.*)/)&&(i=t.match(/(.*?)#(.*)/),n.hash=i[2],t=i[1]),n.hash&&e.match(/^#/))return s(e,n.hash);if(t.match(/(.*?)\?(.*)/)&&(i=t.match(/(.*?)\?(.*)/),n.query=i[2],t=i[1]),n.query&&e.match(/^\?/))return s(e,n.query);if(t.match(/(.*?):?\/\/(.*)/)&&(i=t.match(/(.*?):?\/\/(.*)/),n.protocol=i[1].toLowerCase(),t=i[2]),t.match(/(.*?)(\/.*)/)&&(i=t.match(/(.*?)(\/.*)/),n.path=i[2],t=i[1]),n.path=(n.path||"").replace(/^([^/])/,"/$1").replace(/\/$/,""),e.match(/^[-0-9]+$/)&&(e=e.replace(/^([^/])/,"/$1")),e.match(/^\//))return r(e,n.path.substring(1));if((i=(i=r("/-1",n.path.substring(1)))&&i.match(/(.*?)\.(.*)/))&&(n.file=i[0],n.filename=i[1],n.fileext=i[2]),t.match(/(.*):([0-9]+)$/)&&(i=t.match(/(.*):([0-9]+)$/),n.port=i[2],t=i[1]),t.match(/(.*?)@(.*)/)&&(i=t.match(/(.*?)@(.*)/),n.auth=i[1],t=i[2]),n.auth&&(i=n.auth.match(/(.*):(.*)/),n.user=i?i[1]:n.auth,n.pass=i?i[2]:void 0),n.hostname=t.toLowerCase(),"."===e.charAt(0))return r(e,n.hostname);a()&&(i=n.hostname.match(a()))&&(n.tld=i[3],n.domain=i[2]?i[2]+"."+i[3]:void 0,n.sub=i[1]||void 0);var i=n.port?":"+n.port:"";n.protocol=n.protocol||window.location.protocol.replace(":",""),n.port=n.port||("https"===n.protocol?"443":"80"),n.protocol=n.protocol||("443"===n.port?"https":"http"),n.basic=n.protocol+"://"+n.hostname+i}return e in n?n[e]:"{}"===e?n:""}}(),_.createString=function(e){for(var t=e,n=Math.random().toString(36).substr(2);n.length<t;)n+=Math.random().toString(36).substr(2);return n=n.substr(0,e)},_.createAesKey=function(){return _.createString(16)},_.generateEncryptyData=function(e,t){if(void 0===t)return e;var n=t.publicKey,i=t.version;if(void 0===n||void 0===i)return e;if("undefined"==typeof CryptoJS||"undefined"==typeof JSEncrypt)return e;var a=_.createAesKey();try{var r=CryptoJS.enc.Utf8.parse(a),s=CryptoJS.enc.Utf8.parse(JSON.stringify(e)),o=_.isUndefined(CryptoJS.pad.Pkcs7)?CryptoJS.pad.PKCS7:CryptoJS.pad.Pkcs7,r=CryptoJS.AES.encrypt(s,r,{mode:CryptoJS.mode.ECB,padding:o}).toString(),o=new JSEncrypt;o.setPublicKey(n);o=o.encrypt(a);return!1===o?(logger.warn("Encryption failed, return the original data"),e):{pkv:i,ekey:o,payload:r}}catch(e){logger.warn("Encryption failed, return the original data: "+e)}return e},_.getUtm=function(){var n={};return _.each(utmTypes,function(e){try{var t=_.getQueryParam(location.href,e);t.length&&(n[e]=t)}catch(e){logger.warn("get utm fail: "+e)}}),JSON.stringify(n)},_.getQueryParam=function(e,t){t=t.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]"),e=_.decodeURIComponent(e);e=new RegExp("[\\?&]"+t+"=([^&#]*)").exec(e);return null===e||e&&"string"!=typeof e[1]&&e[1].length?"":_.decodeURIComponent(e[1])},_.getUtmFromQuery=function(t){var n={};return _.each(utmTypes,function(e){t[e]&&(n[e]=t[e])}),JSON.stringify(n)},_.indexOf=function(e,t){var n=e.indexOf;if(n)return n.call(e,t);for(var i=0;i<e.length;i++)if(t===e[i])return i;return-1},_.checkCalibration=function(e,t,n){return e};var logger="object"===_typeof(logger)?logger:{};logger.info=function(){if("object"===("undefined"==typeof console?"undefined":_typeof(console))&&console.log&&logger.enabled)try{return arguments[0]="[ThinkingData] Info: "+arguments[0],console.log.apply(console,arguments)}catch(e){console.log("[ThinkingData] Info: "+arguments[0])}},logger.warn=function(){if("object"===("undefined"==typeof console?"undefined":_typeof(console))&&console.log&&logger.enabled)try{return arguments[0]="[ThinkingData] Warning: "+arguments[0],console.warn.apply(console,arguments)}catch(e){console.warn("[ThinkingData] Warning: "+arguments[0])}};var KEY_NAME_MATCH_REGEX=/^[a-zA-Z][a-zA-Z0-9_]{0,49}$/,PropertyChecker=function(){function e(){_classCallCheck(this,e)}return _createClass(e,null,[{key:"stripProperties",value:function(e){return _.isObject(e)&&_.each(e,function(e,t){_.isString(e)||_.isNumber(e)||_.isDate(e)||_.isBoolean(e)||_.isArray(e)||_.isObject(e)||logger.warn("Your data -",t,e,"- format does not meet requirements and may not be stored correctly. Attribute values only support String, Number, Date, Boolean, Array, Object")}),e}},{key:"_checkPropertiesKey",value:function(e){var n=!0;return _.each(e,function(e,t){KEY_NAME_MATCH_REGEX.test(t)||(logger.warn("Invalid KEY: "+t),n=!1)}),n}},{key:"event",value:function(e){return!(!_.isString(e)||!KEY_NAME_MATCH_REGEX.test(e))||(logger.warn("Check the parameter format. The eventName must start with an English letter and contain no more than 50 characters including letters, digits, and underscores: "+e),!1)}},{key:"propertyName",value:function(e){return!(!_.isString(e)||!KEY_NAME_MATCH_REGEX.test(e))||(logger.warn("Check the parameter format. PropertyName must start with a letter and contain letters, digits, and underscores (_). The value is a string of no more than 50 characters: "+e),!1)}},{key:"properties",value:function(e){return this.stripProperties(e),!e||(_.isObject(e)?!!this._checkPropertiesKey(e)||(logger.warn("Check the parameter format. The properties key must start with a letter, contain digits, letters, and underscores (_), and contain a maximum of 50 characters"),!1):(logger.warn("properties can be none, but it must be an object"),!1))}},{key:"propertiesMust",value:function(e){return this.stripProperties(e),void 0===e||!_.isObject(e)||_.isEmptyObject(e)?(logger.warn("properties must be an object with a value"),!1):!!this._checkPropertiesKey(e)||(logger.warn("Check the parameter format. The properties key must start with a letter, contain digits, letters, and underscores (_), and contain a maximum of 50 characters"),!1)}},{key:"userId",value:function(e){return!(!_.isString(e)||!/^.{1,64}$/.test(e))||(logger.warn("The user ID must be a string of less than 64 characters and cannot be null"),!1)}},{key:"userAddProperties",value:function(e){if(!this.propertiesMust(e))return!1;for(var t in e)if(!_.isNumber(e[t]))return logger.warn("The attributes of userAdd need to be Number"),!1;return!0}},{key:"userAppendProperties",value:function(e){if(!this.propertiesMust(e))return!1;for(var t in e)if(!_.isArray(e[t]))return logger.warn("The attribute of userAppend must be Array"),!1;return!0}}]),e}(),PlatformProxy=function(){function e(){_classCallCheck(this,e),this.config={persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_mg"}}return _createClass(e,[{key:"initSdkConfig",value:function(e){this.initConfig=e}},{key:"getConfig",value:function(){return this.config}},{key:"getStorage",value:function(e,t,n){e=localStorage.getItem(e);if(!t)return _.isJSONString(e)?JSON.parse(e):{};_.isJSONString(e)?n(JSON.parse(e)):n({})}},{key:"setStorage",value:function(e,t){localStorage.setItem(e,t)}},{key:"removeStorage",value:function(e){localStorage.removeItem(e)}},{key:"_setSystemProxy",value:function(e){this._sysCallback=e}},{key:"getSystemInfo",value:function(e){var t={mp_platform:"web",system:this._getOs(),screenWidth:window.screen.width,screenHeight:window.screen.height,systemLanguage:navigator.language};this._sysCallback&&(t=_.extend(t,this._sysCallback(e))),e.success(t),e.complete()}},{key:"_getOs",value:function(){var e=navigator.userAgent;return/Windows/i.test(e)?/Phone/.test(e)||/WPDesktop/.test(e)?"Windows Phone":"Windows":/(iPhone|iPad|iPod)/.test(e)?"iOS":/Android/.test(e)?"Android":/(BlackBerry|PlayBook|BB10)/i.test(e)?"BlackBerry":/Mac/i.test(e)?"MacOS":/Linux/.test(e)?"Linux":/CrOS/.test(e)?"ChromeOS":""}},{key:"getNetworkType",value:function(e){e.complete()}},{key:"onNetworkStatusChange",value:function(){}},{key:"request",value:function(e){var t={},n=new XMLHttpRequest;if(n.open(e.method,e.url),e.header)for(var i in e.header)n.setRequestHeader(i,e.header[i]);return n.onreadystatechange=function(){4===n.readyState&&200===n.status?(t.statusCode=200,_.isJSONString(n.responseText)&&(t.data=JSON.parse(n.responseText)),e.success(t)):200!==n.status&&(t.errMsg="network error",e.fail(t))},n.ontimeout=function(){t.errMsg="timeout",e.fail(t)},n.send(e.data),n}},{key:"initAutoTrackInstance",value:function(e,t){this.instance=e,this.autoTrack=t.autoTrack;var n=this;n.onPageShow(),n.autoTrack.appHide&&n.instance.timeEvent("ta_page_hide"),"onvisibilitychange"in document&&(document.onvisibilitychange=function(){document.hidden?n.onPageHide(!0):(n.onPageShow(),n.autoTrack.appHide&&n.instance.timeEvent("ta_page_hide"))})}},{key:"setGlobal",value:function(e,t){window[t]=e}},{key:"getAppOptions",value:function(){}},{key:"showToast",value:function(){}},{key:"onPageShow",value:function(){var e;this.autoTrack.appShow&&(e={},_.extend(e,this.autoTrack.properties),_.isFunction(this.autoTrack.callback)&&_.extend(e,this.autoTrack.callback("appShow")),this.instance._internalTrack("ta_page_show",e))}},{key:"onPageHide",value:function(e){var t;this.autoTrack.appHide&&(t={},_.extend(t,this.autoTrack.properties),_.isFunction(this.autoTrack.callback)&&_.extend(t,this.autoTrack.callback("appHide")),this.instance._internalTrack("ta_page_hide",t,new Date,null,e))}}],[{key:"createInstance",value:function(){return new e}}]),e}(),AutoTrackBridge=function(){function n(e,t){_classCallCheck(this,n),this.taInstance=e,this.config=t||{},this.referrer="Directly open",this.config.isPlugin?(e.App=function(){App.apply(this,arguments)},inension(e.Page)):(e=App,App=this._initAppExtention(e),e=Page,Page=this._initPageExtension(e))}return _createClass(n,[{key:"_initPageExtension",value:function(i){var a=this;return function(e){var t=e.onShow,n=e.onShareAppMessage;return e.onShow=function(e){a.onPageShow(),"function"==typeof t&&t.call(this,e)},"function"==typeof n&&(e.onShareAppMessage=function(e){e=n.call(this,e);return a.onPageShare(e)}),i(e)}}},{key:"_initAppExtention",value:function(a){var r=this;return function(e){var t=e.onLaunch,n=e.onShow,i=e.onHide;return e.onLaunch=function(e){r.onAppLaunch(e,this),"function"==typeof t&&t.call(this,e)},e.onShow=function(e){r.onAppShow(e),"function"==typeof n&&n.call(this,e)},e.onHide=function(){r.onAppHide(),"function"==typeof i&&i.call(this)},a(e)}}},{key:"onAppLaunch",value:function(e,t){this._setAutoTrackProperties(e),_.isUndefined(t)||(t[this.taInstance.name]=this.taInstance),this.config.appLaunch&&(t={},e&&e.path&&(t["#url_path"]=this._getPath(e.path)),e&&e.query&&(t["#utm"]=_.getUtmFromQuery(e.query)),this.taInstance._internalTrack("ta_mp_launch",t))}},{key:"onAppShow",value:function(e){var t;this.config.appHide&&this.taInstance.timeEvent("ta_mp_hide"),this._setAutoTrackProperties(e),this.config.appShow&&(t={},e&&e.path&&(t["#url_path"]=this._getPath(e.path)),e&&e.query&&(t["#utm"]=_.getUtmFromQuery(e.query)),_.extend(t,this.config.properties),_.isFunction(this.config.callback)&&_.extend(t,this.config.callback("appShow")),this.taInstance._internalTrack("ta_mp_show",t))}},{key:"onAppHide",value:function(){var e;this.config.appHide&&(e={"#url_path":this._getCurrentPath()},_.extend(e,this.config.properties),_.isFunction(this.config.callback)&&_.extend(e,this.config.callback("appHide")),this.taInstance._internalTrack("ta_mp_hide",e),this.taInstance.flush())}},{key:"_getCurrentPath",value:function(){var e="Not to get";try{var t=getCurrentPages(),e=t[t.length-1].route}catch(e){logger.info(e)}return e}},{key:"_setAutoTrackProperties",value:function(e){e={"#scene":e.scene};this.taInstance._setAutoTrackProperties(e)}},{key:"_getPath",value:function(e){return"string"==typeof e?e.replace(/^\//,""):"Abnormal values"}},{key:"onPageShare",value:function(e){return this.config.pageShare&&this.taInstance._internalTrack("ta_mp_share",{"#url_path":this._getCurrentPath()}),_.isObject(e)?e:{}}},{key:"onPageShow",value:function(){var e,t;this.config.pageShow&&(t={"#url_path":(e=this._getCurrentPath())||"The system did not get a value","#referrer":this.referrer},this.referrer=e,this.taInstance._internalTrack("ta_mp_view",t))}}]),n}(),AutoTrackBridge$1=function(){function a(e,t,n){var i=this;_classCallCheck(this,a),this.taInstance=e,this.config=t||{};t=n.getLaunchOptionsSync();this._onLaunch(t),this._onShow(t),this.startTracked=!0,n.onShow(function(e){i._onShow(e)}),n.onHide(function(){var e;i.startTracked=!1,i.config.appHide&&(e={},_.extend(e,i.config.properties),_.isFunction(i.config.callback)&&_.extend(e,i.config.callback("appHide")),i.taInstance._internalTrack("ta_mg_hide",e),i.taInstance.flush())})}return _createClass(a,[{key:"_onLaunch",value:function(){var e;this.config.appLaunch&&(e={},_.extend(e,this.config.properties),_.isFunction(this.config.callback)&&_.extend(e,this.config.callback("appLaunch")),this.taInstance._internalTrack("ta_mg_launch",e))}},{key:"_onShow",value:function(e){this.startTracked||(this.config.appHide&&this.taInstance.timeEvent("ta_mg_hide"),e&&e.scene&&this.taInstance._setAutoTrackProperties({"#scene":e.scene}),this.config.appShow&&(e={},_.extend(e,this.config.properties),_.isFunction(this.config.callback)&&_.extend(e,this.config.callback("appShow")),this.taInstance._internalTrack("ta_mg_show",e)))}}]),a}(),PlatformProxy$1=function(){function i(e,t,n){_classCallCheck(this,i),this.api=e,this.config=t,this._config=n}return _createClass(i,[{key:"initSdkConfig",value:function(){}},{key:"getConfig",value:function(){return this.config}},{key:"getStorage",value:function(e,t,n){if(t)this.api.getStorage({key:e,success:function(e){e=_.isJSONString(e.data)?JSON.parse(e.data):{};n(e)},fail:function(){logger.warn("getStorage faild"),n({})}});else try{if("dd_mp"===this._config.platform||"ali_mp"===this._config.platform||"ali_mg"===this._config.platform){var i=this.api.getStorageSync({key:e});return _.isJSONString(i.data)?JSON.parse(i.data):{}}i=this.api.getStorageSync(e);return _.isJSONString(i)?JSON.parse(i):{}}catch(e){return{}}}},{key:"setStorage",value:function(e,t){try{"ali_mp"===this._config.platform||"tb_mp"===this._config.platform||"dd_mp"===this._config.platform||"ali_mg"===this._config.platform?this.api.setStorageSync({key:e,data:t}):this.api.setStorageSync(e,t)}catch(e){}}},{key:"removeStorage",value:function(e){try{_.isFunction(this.api.removeStorage)?this.api.removeStorage({key:e}):_.isFunction(this.api.deleteStorage)&&this.api.deleteStorage({key:e})}catch(e){}}},{key:"_getPlatform",value:function(){return""}},{key:"getSystemInfo",value:function(t){var n=this._config.mpPlatform;this.api.getSystemInfo({success:function(e){_.isFunction(n)?e.mp_platform=n(e):e.mp_platform=n,t.success(e),"wechat"===n&&t.complete()},complete:function(){t.complete()}})}},{key:"getNetworkType",value:function(t){_.isFunction(this.api.getNetworkType)?this.api.getNetworkType({success:function(e){t.success(e)},complete:function(){t.complete()}}):(t.success({}),t.complete())}},{key:"onNetworkStatusChange",value:function(e){_.isFunction(this.api.onNetworkStatusChange)?this.api.onNetworkStatusChange(e):e({})}},{key:"request",value:function(t){if("ali_mp"!==this._config.platform&&"dd_mp"!==this._config.platform&&"ali_mg"!==this._config.platform)return this.api.request(t);var e=_.extend({},t);return e.headers=t.header,e.header=void 0,e.success=function(e){e.statusCode=e.status,t.success(e)},e.fail=function(e){e.errMsg=e.errorMessage,t.fail(e)},"dd_mp"===this._config.platform?this.api.httpRequest(e):this.api.request(e)}},{key:"initAutoTrackInstance",value:function(e,t){return _.isObject(t.autoTrack)&&(t.autoTrack.isPlugin=t.is_plugin),new(this._config.mp?AutoTrackBridge:AutoTrackBridge$1)(e,t.autoTrack,this.api)}},{key:"setGlobal",value:function(e,t){this._config.mp?logger.warn("ThinkingAnalytics: we do not set global name for TA instance when you do not enable auto track."):"ali_mg"!==this._config.platform&&(GameGlobal[t]=e)}},{key:"getAppOptions",value:function(e){var t={};try{t=this.api.getLaunchOptionsSync()}catch(e){logger.warn("Cannot get launch options.")}if(_.isFunction(e))try{this._config.mp?this.api.onAppShow(e):this.api.onShow(e)}catch(e){logger.warn("Cannot register onShow callback.")}return t}},{key:"showToast",value:function(e){var t;_.isFunction(this.api.showToast)&&(t={title:e},"dd_mp"!==this._config.platform&&"ali_mp"!==this._config.platform||(t.content=e),this.api.showToast(t))}}],[{key:"createInstance",value:function(){return this._createInstance("kuaishou_mg")}},{key:"_createInstance",value:function(e){switch(e){case"wechat_mp":return new i(wx,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_wechat"},{mpPlatform:"wechat",mp:!0,platform:e});case"wechat_mg":return new i(wx,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_wechat_game"},{mpPlatform:"wechat",platform:e});case"qq_mp":return new i(qq,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_qq"},{mpPlatform:"qq",mp:!0,platform:e});case"qq_mg":return new i(qq,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_qq_game"},{mpPlatform:"qq",platform:e});case"baidu_mp":return new i(swan,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_swan"},{mpPlatform:function(e){return e.host},mp:!0,platform:e});case"baidu_mg":return new i(swan,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_swan_game"},{mpPlatform:function(e){return e.host},platform:e});case"tt_mg":return new i(tt,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_tt_game"},{mpPlatform:function(e){return e.appName},platform:e});case"tt_mp":return new i(tt,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_tt"},{mpPlatform:function(e){return e.appName},mp:!0,platform:e});case"ali_mp":return new i(my,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_ali"},{mpPlatform:function(e){return e.app},mp:!0,platform:e});case"ali_mg":return new i(my,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_ali_game"},{mpPlatform:function(e){return e.app},platform:e});case"dd_mp":return new i(dd,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_dd"},{mpPlatform:"dingding",mp:!0,platform:e});case"bl_mg":return new i(bl,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_mg"},{mpPlatform:"bilibili",platform:e});case"kuaishou_mp":return new i(ks,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_kuaishou"},{mpPlatform:"kuaishou",mp:!0,platform:e});case"kuaishou_mg":return new i(ks,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_kuaishou"},{mpPlatform:"kuaishou",platform:e});case"qh360_mg":return new i(qh,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_qh360"},{mpPlatform:"qh360",platform:e});case"tb_mp":return new i(my,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_tb"},{mpPlatform:"tb",mp:!0,platform:e});case"jd_mp":return new i(jd,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_jd"},{mpPlatform:"jd",mp:!0,platform:e});case"qh360_mp":return new i(qh,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_qh360"},{mpPlatform:"qh360",mp:!0,platform:e});case"WEB":return new PlatformProxy.createInstance}}}]),i}(),PlatformAPI=function(){function e(){_classCallCheck(this,e)}return _createClass(e,null,[{key:"_getCurrentPlatform",value:function(){return this.currentPlatform||(this.currentPlatform=PlatformProxy$1.createInstance())}},{key:"initConfig",value:function(e){this._getCurrentPlatform().initSdkConfig(e)}},{key:"getConfig",value:function(){return this._getCurrentPlatform().getConfig()}},{key:"getStorage",value:function(e,t,n){return this._getCurrentPlatform().getStorage(e,t,n)}},{key:"setStorage",value:function(e,t){return this._getCurrentPlatform().setStorage(e,t)}},{key:"removeStorage",value:function(e){return this._getCurrentPlatform().removeStorage(e)}},{key:"getSystemInfo",value:function(e){return this._getCurrentPlatform().getSystemInfo(e)}},{key:"getNetworkType",value:function(e){return this._getCurrentPlatform().getNetworkType(e)}},{key:"onNetworkStatusChange",value:function(e){this._getCurrentPlatform().onNetworkStatusChange(e)}},{key:"request",value:function(e){return this._getCurrentPlatform().request(e)}},{key:"initAutoTrackInstance",value:function(e,t){return this._getCurrentPlatform().initAutoTrackInstance(e,t)}},{key:"setGlobal",value:function(e,t){e&&t&&this._getCurrentPlatform().setGlobal(e,t)}},{key:"getAppOptions",value:function(e){return this._getCurrentPlatform().getAppOptions(e)}},{key:"showDebugToast",value:function(e){this._getCurrentPlatform().showToast(e)}}]),e}(),HttpTask=function(){function r(e,t,n,i,a){_classCallCheck(this,r),this.data=e,this.serverUrl=t,this.callback=a,this.tryCount=_.isNumber(n)?n:1,this.timeout=_.isNumber(i)?i:3e3,this.taClassName="HttpTask"}return _createClass(r,[{key:"run",value:function(){var t=this,e=_.createExtraHeaders();e["content-type"]="application/json",this.runTime=new Date,PlatformAPI.request({url:this.serverUrl,method:"POST",data:this.data,header:e,success:function(e){t.onSuccess(e)},fail:function(e){t.onFailed(e)}})}},{key:"onSuccess",value:function(e){if(!this.sendTimeout())if(_.isObject(e)&&200===e.statusCode){var t;switch((_.isUndefined(e.data)||_.isUndefined(e.data.code))&&(e.data={code:0}),e.data.code){case 0:t="success";break;case-1:t="invalid data";break;case-2:t="invalid APP ID";break;default:t="Unknown return code"}this.callback({code:e.data.code,msg:t})}else this.callback({code:-3,msg:_.isObject(e)?e.statusCode:"Unknown error"})}},{key:"onFailed",value:function(e){this.sendTimeout()||(0<--this.tryCount?this.run():this.callback({code:-3,msg:_.isObject(e)?e.errMsg:"Unknown error"}))}},{key:"sendTimeout",value:function(){return(new Date).getTime()-this.runTime.getTime()>this.timeout}}]),r}(),HttpTaskDebug=function(){function o(e,t,n,i,a,r,s){_classCallCheck(this,o),this.data=e,this.serverDebugUrl=t,this.callback=s,this.tryCount=_.isNumber(n)?n:1,this.timeout=_.isNumber(i)?i:3e3,this.dryrun=a,this.deviceId=r,this.taClassName="HttpTaskDebug"}return _createClass(o,[{key:"run",value:function(){var t=this,e="appid="+this.data["#app_id"]+"&source=client&dryRun="+this.dryrun+"&deviceId="+this.deviceId+"&data="+encodeURIComponent(JSON.stringify(this.data.data[0])),n=_.createExtraHeaders();n["content-type"]="application/x-www-form-urlencoded";var i=PlatformAPI.request({url:this.serverDebugUrl,method:"POST",data:e,header:n,success:function(e){t.onSuccess(e),clearTimeout(a)},fail:function(e){t.onFailed(e),clearTimeout(a)}}),a=setTimeout(function(){(_.isObject(i)||_.isPromise(i))&&_.isFunction(i.abort)&&i.abort()},this.timeout)}},{key:"onSuccess",value:function(e){if(_.isObject(e)&&200===e.statusCode){var t;if((_.isUndefined(e.data)||_.isUndefined(e.data.errorLevel))&&(e.data={errorLevel:0}),0===e.data.errorLevel)t="Verify data success.";else if(1===e.data.errorLevel){for(var n=e.data.errorProperties,i="",a=0;a<n.length;a++)var r=n[a].errorReason,i=i+" propertyName:"+n[a].propertyName+" errorReasons:"+r+"\n";t="Debug data error. errorLevel:"+e.data.errorLevel+" reason:"+i}else 2!==e.data.errorLevel&&-1!==e.data.errorLevel||(t="Debug data error. errorLevel:"+e.data.errorLevel+" reason:"+e.data.errorReasons);logger.info(t),this.callback({code:e.data.errorLevel,msg:t})}else this.callback({code:-3,msg:_.isObject(e)?e.statusCode:"Unknown error"})}},{key:"onFailed",value:function(e){0<--this.tryCount?this.run():this.callback({code:-3,msg:_.isObject(e)?e.errMsg:"Unknown error"})}}]),o}(),SenderQueue=function(){function e(){_classCallCheck(this,e),this.items=[],this.isRunning=!1,this.showDebug=!1}return _createClass(e,[{key:"enqueue",value:function(e,t,n,i){var a=this,i=!(3<arguments.length&&void 0!==i)||i,r=this,t="debug"===n.debugMode?new HttpTaskDebug(e,t,n.maxRetries,n.sendTimeout,0,n.deviceId,function(e){r.isRunning=!1,delete a.runTime,_.isFunction(n.callback)&&n.callback(e),r._runNext(),!1===r.showDebug&&(0!==e.code&&1!==e.code&&2!==e.code||(r.showDebug=!0,_.isFunction(PlatformAPI.showDebugToast)&&PlatformAPI.showDebugToast("The current mode is Debug")))}):"debugOnly"===n.debugMode?new HttpTaskDebug(e,t,n.maxRetries,n.sendTimeout,1,n.deviceId,function(e){r.isRunning=!1,delete a.runTime,_.isFunction(n.callback)&&n.callback(e),r._runNext(),!1===r.showDebug&&(0!==e.code&&1!==e.code&&2!==e.code||(r.showDebug=!0,_.isFunction(PlatformAPI.showDebugToast)&&PlatformAPI.showDebugToast("The current mode is debugOnly")))}):new HttpTask(JSON.stringify(e),t,n.maxRetries,n.sendTimeout,function(e){r.isRunning=!1,delete a.runTime,_.isFunction(n.callback)&&n.callback(e),r._runNext()});!0===i?(this.items.push(t),this._runNext()):t.run()}},{key:"_dequeue",value:function(){return this.items.shift()}},{key:"_runNext",value:function(){if(0<this.items.length&&!this.isRunning)if(this.isRunning=!0,this.runTime=new Date,"HttpTask"!==this.items[0].taClassName)this._dequeue().run();else{var e=this.items.splice(0,this.items.length),t=e[0],n=JSON.parse(t.data),i=n["#app_id"],a=[];a.push(t.callback);for(var r=1;r<e.length;r++){var s=e[r],o=JSON.parse(s.data);o["#app_id"]===i&&t.serverUrl===s.serverUrl?(n.data=n.data.concat(o.data),a.push(s.callback)):this.items.push(s)}var c=(new Date).getTime();n["#flush_time"]=c,new HttpTask(JSON.stringify(n),t.serverUrl,t.tryCount,t.timeout,function(e){for(var t in a)Object.hasOwnProperty.call(a,t)&&(0,a[t])(e)}).run()}}},{key:"runTimeout",value:function(e){if(_.isDate(this.runTime)&&(new Date).getTime()-this.runTime.getTime()>e)return!0;return!1}},{key:"resetTimeout",value:function(){this.isRunning=!1,delete this.runTime}}]),e}(),senderQueue=new SenderQueue,DEFAULT_CONFIG={name:"thinkingdata",is_plugin:!1,maxRetries:3,sendTimeout:3e3,enablePersistence:!0,asyncPersistence:!1,enableLog:!0,strict:!1,debugMode:"none",enableCalibrationTime:!1,enableBatch:!1,cloudEnv:"online"},systemInformation={properties:{"#lib":Config.LIB_NAME,"#lib_version":Config.LIB_VERSION},initDeviceId:function(e){_.isString(e)&&(this.properties["#device_id"]=e)},getSystemInfo:function(e){var n=this;PlatformAPI.onNetworkStatusChange(function(e){n.properties["#network_type"]=e.networkType}),PlatformAPI.getNetworkType({success:function(e){n.properties["#network_type"]=e.networkType},complete:function(){PlatformAPI.getSystemInfo({success:function(e){var t=e.system?e.system.replace(/\s+/g," ").split(" "):[],t={"#manufacturer":e.brand,"#device_model":e.model,"#screen_width":Number(e.screenWidth),"#screen_height":Number(e.screenHeight),"#os":t[0],"#os_version":t[1],"#mp_platform":e.mp_platform,"#system_language":e.systemLanguage};_.extend(n.properties,t),_.setMpPlatform(e.mp_platform)},complete:function(){e()}})}})}},ThinkingDataPersistence=function(){function e(t,n){var i=this;_classCallCheck(this,e),this.enabled=t.enablePersistence,this.enabled?(t.isChildInstance?(this.name=t.persistenceName+"_"+t.name,this.nameOld=t.persistenceNameOld+"_"+t.name):(this.name=t.persistenceName,this.nameOld=t.persistenceNameOld),t.asyncPersistence?(this._state={},PlatformAPI.getStorage(this.name,!0,function(e){_.isEmptyObject(e)?PlatformAPI.getStorage(i.nameOld,!0,function(e){i._state=_.extend2Layers({},e,i._state),i._init(t,n),i._save()}):(i._state=_.extend2Layers({},e,i._state),i._init(t,n),i._save())})):(this._state=PlatformAPI.getStorage(this.name)||{},_.isEmptyObject(this._state)&&(this._state=PlatformAPI.getStorage(this.nameOld)||{}),this._init(t,n))):(this._state={},this._init(t,n))}return _createClass(e,[{key:"_init",value:function(e,t){this.getDistinctId()||this.setDistinctId(_.UUID()),e.isChildInstance||(this.getDeviceId()||this._setDeviceId(_.UUID()),systemInformation.initDeviceId(this.getDeviceId())),this.initComplete=!0,"function"==typeof t&&t(),this._save()}},{key:"_save",value:function(){this.enabled&&this.initComplete&&PlatformAPI.setStorage(this.name,JSON.stringify(this._state))}},{key:"_set",value:function(e,t){var n,i=this;"string"==typeof e?(n={})[e]=t:"object"===_typeof(e)&&(n=e),_.each(n,function(e,t){i._state[t]=e}),this._save()}},{key:"_get",value:function(e){return this._state[e]}},{key:"setEventTimer",value:function(e,t){var n=this._state.event_timers||{};n[e]=t,this._set("event_timers",n)}},{key:"removeEventTimer",value:function(e){var t=(this._state.event_timers||{})[e];return _.isUndefined(t)||(delete this._state.event_timers[e],this._save()),t}},{key:"getDeviceId",value:function(){return this._state.device_id}},{key:"_setDeviceId",value:function(e){this.getDeviceId()?logger.warn("cannot modify the device id."):this._set("device_id",e)}},{key:"getDistinctId",value:function(){return this._state.distinct_id}},{key:"setDistinctId",value:function(e){this._set("distinct_id",e)}},{key:"getAccountId",value:function(){return this._state.account_id}},{key:"setAccountId",value:function(e){this._set("account_id",e)}},{key:"getSuperProperties",value:function(){return this._state.props||{}}},{key:"setSuperProperties",value:function(e,t){e=t?e:_.extend(this.getSuperProperties(),e);this._set("props",e)}}]),e}(),dataStoragePrefix="ta_mpsdk_",tabStoragePrefix="tab_tampsdk_",BatchConsumer=function(){function r(e,t){_classCallCheck(this,r),this.config=e,this.ta=t,this.timer=null,this.batchConfig=_.extend({size:6,interval:6e3,maxLimit:500},this.config.batchConfig),this.batchConfig.size<1&&(this.batchConfig.size=1),30<this.batchConfig.size&&(this.batchConfig.size=30),this.storageKey=dataStoragePrefix+this.config.appId,this.maxLimit=this.batchConfig.maxLimit,this.batchList=[];t=PlatformAPI.getStorage(this.storageKey);_.isArray(t)&&(this.batchList=t);var t=tabStoragePrefix+this.config.appId,n=PlatformAPI.getStorage(t);if(_.isArray(n)){for(var i=0;i<n.length;i++){var a=PlatformAPI.getStorage(n[i]);this.batchList.push(a),PlatformAPI.removeStorage(n[i])}PlatformAPI.removeStorage(t)}this.dataHasChange=!1,this.dataSendTimeStamp=0}return _createClass(r,[{key:"batchInterval",value:function(){this.loopWrite(),this.loopSend()}},{key:"loopWrite",value:function(){var e=this;setTimeout(function(){e.batchWrite(),e.loopWrite()},500)}},{key:"batchWrite",value:function(){this.dataHasChange&&(this.dataHasChange=!1,PlatformAPI.setStorage(this.storageKey,JSON.stringify(this.batchList)))}},{key:"loopSend",value:function(){var e=this;e.timer=setTimeout(function(){e.batchSend(),clearTimeout(e.timer),e.loopSend()},this.batchConfig.interval)}},{key:"add",value:function(e){this.batchList.length>this.maxLimit&&this.batchList.shift(),this.batchList.push(e),this.dataHasChange=!0,this.batchList.length>this.batchConfig.size&&this.batchSend()}},{key:"flush",value:function(){clearTimeout(this.timer),this.batchSend(),this.loopSend()}},{key:"batchSend",value:function(){var t,n,i,e=new Date;0!==this.dataSendTimeStamp&&e.getTime()-this.dataSendTimeStamp<this.config.sendTimeout+500||(this.dataSendTimeStamp=e.getTime(),0<(t=(e=30<this.batchList.length?this.batchList.slice(0,30):this.batchList).length)&&((n={}).data=e,n["#app_id"]=this.config.appId,n["#flush_time"]=(new Date).getTime(),i=this,senderQueue.enqueue(n,this.ta.serverUrl,{maxRetries:1,sendTimeout:this.config.sendTimeout,callback:function(e){0===e.code&&(logger.info("Flush success: "+JSON.stringify(n,null,4)),i.batchRemove(t))},debugMode:this.config.debugMode,deviceId:this.ta.getDeviceId()},!1)))}},{key:"batchRemove",value:function(e){this.dataSendTimeStamp=0,this.batchList.splice(0,e),this.dataHasChange=!0,this.batchWrite()}}]),r}(),ThinkingDataAPI=function(){function n(e){_classCallCheck(this,n),e.appId=e.appId?_.checkAppId(e.appId):_.checkAppId(e.appid),e.serverUrl=e.serverUrl?_.checkUrl(e.serverUrl):_.checkUrl(e.server_url);var t=_.extend({},DEFAULT_CONFIG,PlatformAPI.getConfig());_.isObject(e)?this.config=_.extend(t,e):this.config=t,this._init(this.config)}return _createClass(n,[{key:"_init",value:function(e){var t=this;this.name=e.name,this.appId=e.appId||e.appid;var n=e.serverUrl||e.server_url;this.serverUrl=n+"/sync_xcx",this.serverDebugUrl=n+"/data_debug",this.configUrl=n+"/config",this.autoTrackProperties={},PlatformAPI.initConfig(e),this._queue=[],this.updateConfig(this.configUrl,this.appId),e.isChildInstance?this._state={}:(logger.enabled=e.enableLog,this.instances=[],this._state={getSystemInfo:!1,initComplete:!1},PlatformAPI.setGlobal(this,this.name)),this.store=new ThinkingDataPersistence(e,function(){t.config.asyncPersistence&&_.isFunction(t.config.persistenceComplete)&&t.config.persistenceComplete(t),t._updateState()}),this.enabled=!_.isBoolean(this.store._get("ta_enabled"))||this.store._get("ta_enabled"),this.isOptOut=!!_.isBoolean(this.store._get("ta_isOptOut"))&&this.store._get("ta_isOptOut"),!e.isChildInstance&&e.autoTrack&&(this.autoTrack=PlatformAPI.initAutoTrackInstance(this,e)),void 0!==this.config.enableBatch&&!1!==this.config.enableBatch&&(this.batchConsumer=new BatchConsumer(this.config,this),this.batchConsumer.batchInterval())}},{key:"initSystemInfo",value:function(){var e=this;this.config.isChildInstance||systemInformation.getSystemInfo(function(){e._updateState({getSystemInfo:!0})})}},{key:"updateConfig",value:function(e,t){var n=this,i=_.createExtraHeaders();i["content-type"]="application/json";var a=PlatformAPI.request({url:e+"?appid="+t,method:"GET",header:i,success:function(e){_.isUndefined(e)||_.isUndefined(e.data)||(logger.info("Get remote config success("+t+") :"+JSON.stringify(e.data)),_.isUndefined(e.data.data)||(n.config.syncBatchSize=e.data.data.sync_batch_size,n.config.syncInterval=e.data.data.sync_interval,n.config.disableEventList=e.data.data.disable_event_list,_.isUndefined(e.data.data.secret_key)||(e=e.data.data.secret_key,n.config.secretKey={publicKey:e.key,version:e.version})))},fail:function(e){logger.info("Get remote config fail("+t+") :"+e.errMsg)}});setTimeout(function(){(_.isObject(a)||_.isPromise(a))&&_.isFunction(a.abort)&&a.abort()},3e3)}},{key:"initInstance",value:function(e,t){if(this.config.isChildInstance)logger.warn("initInstance() cannot be called on child instance");else{if(_.isString(e)&&e!==this.name&&_.isUndefined(this[e])){t=new n(_.extend({},this.config,{enablePersistence:!1,isChildInstance:!0,name:e},t));return this[e]=t,this.instances.push(e),this[e]._state=this._state,t}logger.warn("initInstance() failed due to the name is invalid: "+e)}}},{key:"lightInstance",value:function(e){return this[e]}},{key:"_setAutoTrackProperties",value:function(e){_.extend(this.autoTrackProperties,e)}},{key:"init",value:function(){if(this.initSystemInfo(),this._state.initComplete)return!1;this._updateState({initComplete:!0}),logger.info("TDAnalytics SDK initialize success, AppId = "+this.config.appId+", ServerUrl = "+this.config.serverUrl+", Mode = "+this.config.model+", DeviceId = "+this.getDeviceId()+", Lib = "+Config.LIB_NAME+", LibVersion = "+Config.LIB_VERSION)}},{key:"_isReady",value:function(){return this._state.getSystemInfo&&this._state.initComplete&&this.store.initComplete&&this.getDeviceId()}},{key:"_updateState",value:function(e){var t=this;_.isObject(e)&&_.extend(this._state,e),this._onStateChange(),_.each(this.instances,function(e){t[e]._onStateChange()})}},{key:"_onStateChange",value:function(){var t=this;this._isReady()&&this._queue&&0<this._queue.length&&(_.each(this._queue,function(e){t[e[0]].apply(t,slice.call(e[1]))}),this._queue=[])}},{key:"_hasDisabled",value:function(){var e=!this.enabled||this.isOptOut;return e&&logger.info("SDK is Pause or Stop!"),e}},{key:"_sendRequest",value:function(e,t,n){if(!this._hasDisabled())if(_.isUndefined(this.config.disableEventList)||!this.config.disableEventList.includes(e.eventName)){t=_.isDate(t)?t:new Date;var i={data:[{"#type":e.type,"#time":_.formatDate(_.formatTimeZone(t,this.config.zoneOffset)),"#distinct_id":this.store.getDistinctId()}]};this.store.getAccountId()&&(i.data[0]["#account_id"]=this.store.getAccountId()),"track"===e.type||"track_update"===e.type||"track_overwrite"===e.type?(i.data[0]["#event_name"]=e.eventName,"track_update"===e.type||"track_overwrite"===e.type?i.data[0]["#event_id"]=e.extraId:e.firstCheckId&&(i.data[0]["#first_check_id"]=e.firstCheckId),i.data[0].properties=_.extend({"#zone_offset":_.getTimeZone(t,this.config.zoneOffset)},systemInformation.properties,this.autoTrackProperties,this.store.getSuperProperties(),this.dynamicProperties?this.dynamicProperties():{}),t=this.store.removeEventTimer(e.eventName),_.isUndefined(t)||(a=(new Date).getTime()-t,86400<(a=parseFloat((a/1e3).toFixed(3)))?a=86400:a<0&&(a=0),i.data[0].properties["#duration"]=a)):i.data[0].properties={},_.isObject(e.properties)&&!_.isEmptyObject(e.properties)&&_.extend(i.data[0].properties,e.properties),_.searchObjDate(i.data[0],this.config.zoneOffset),1<this.config.maxRetries&&(i.data[0]["#uuid"]=_.UUIDv4()),i["#app_id"]=this.appId,logger.info("Tracking data, "+JSON.stringify(i,null,4));var a="debug"===this.config.debugMode||"debugOnly"===this.config.debugMode?this.serverDebugUrl:this.serverUrl;if(_.isBoolean(this.config.enableEncrypt)&&!0===this.config.enableEncrypt&&(i.data[0]=_.generateEncryptyData(i.data[0],this.config.secretKey)),this.batchConsumer&&"none"===this.config.debugMode&&!n)return this.batchConsumer.add(i.data[0]),void(_.isFunction(e.onComplete)&&e.onComplete({code:0,msg:"success"}));n?(n=new FormData,"debug"===this.config.debugMode||"debugOnly"===this.config.debugMode?(n.append("source","client"),n.append("appid",this.appId),n.append("dryRun","debugOnly"===this.config.debugMode?1:0),n.append("deviceId",this.getDeviceId()),n.append("data",JSON.stringify(i.data[0])),navigator.sendBeacon(a,n)):(n=(new Date).getTime(),i["#flush_time"]=n,navigator.sendBeacon(a,JSON.stringify(i))),_.isFunction(e.onComplete)&&e.onComplete({statusCode:200})):(senderQueue.runTimeout(this.config.sendTimeout)&&senderQueue.resetTimeout(),senderQueue.enqueue(i,a,{maxRetries:this.config.maxRetries,sendTimeout:this.config.sendTimeout,callback:e.onComplete,debugMode:this.config.debugMode,deviceId:this.getDeviceId()}))}else logger.info("Disabled Event : "+e.eventName)}},{key:"_isObjectParams",value:function(e){return _.isObject(e)&&_.isFunction(e.onComplete)}},{key:"track",value:function(e,t,n,i){var a;this._hasDisabled()||(this._isObjectParams(e)&&(e=(a=e).eventName,t=a.properties,n=a.time,i=a.onComplete),PropertyChecker.event(e)&&PropertyChecker.properties(t)||!this.config.strict?this._internalTrack(e,t,n,i):_.isFunction(i)&&i({code:-1,msg:"invalid parameters"}))}},{key:"trackUpdate",value:function(e){var t,n;this._hasDisabled()||(e&&e.eventId&&(PropertyChecker.event(e.eventName)&&PropertyChecker.properties(e.properties)||!this.config.strict)?this._isReady()?(t=_.checkCalibration(e.properties,e.time,this.config.enableCalibrationTime),n=_.isDate(e.time)?e.time:new Date,this._sendRequest({type:"track_update",eventName:e.eventName,properties:t,onComplete:e.onComplete,extraId:e.eventId},n)):this._queue.push(["trackUpdate",[e]]):(logger.warn("Invalide parameter for trackUpdate: you should pass an object contains eventId to trackUpdate()"),_.isFunction(e.onComplete)&&e.onComplete({code:-1,msg:"invalid parameters"})))}},{key:"trackOverwrite",value:function(e){var t,n;this._hasDisabled()||(e&&e.eventId&&(PropertyChecker.event(e.eventName)&&PropertyChecker.properties(e.properties)||!this.config.strict)?this._isReady()?(t=_.checkCalibration(e.properties,e.time,this.config.enableCalibrationTime),n=_.isDate(e.time)?e.time:new Date,this._sendRequest({type:"track_overwrite",eventName:e.eventName,properties:t,onComplete:e.onComplete,extraId:e.eventId},n)):this._queue.push(["trackOverwrite",[e]]):(logger.warn("Invalide parameter for trackOverwrite: you should pass an object contains eventId to trackOverwrite()"),_.isFunction(e.onComplete)&&e.onComplete({code:-1,msg:"invalid parameters"})))}},{key:"trackFirstEvent",value:function(e){var t,n;this._hasDisabled()||(e&&e.eventName&&(PropertyChecker.event(e.eventName)&&PropertyChecker.properties(e.properties)||!this.config.strict)?this._isReady()?(t=_.checkCalibration(e.properties,e.time,this.config.enableCalibrationTime),n=_.isDate(e.time)?e.time:new Date,this._sendRequest({type:"track",eventName:e.eventName,properties:t,onComplete:e.onComplete,firstCheckId:e.firstCheckId||this.getDeviceId()},n)):this._queue.push(["trackFirstEvent",[e]]):(logger.warn("Invalide parameter for trackFirstEvent: you should pass an object contains eventName to trackFirstEvent()"),_.isFunction(e.onComplete)&&e.onComplete({code:-1,msg:"invalid parameters"})))}},{key:"_internalTrack",value:function(e,t,n,i,a){var r;this._hasDisabled()||(r=_.checkCalibration(t,n,this.config.enableCalibrationTime),n=_.isDate(n)?n:new Date,this._isReady()?this._sendRequest({type:"track",eventName:e,properties:r,onComplete:i},n,a):this._queue.push(["_internalTrack",[e,t,n,i]]))}},{key:"userSet",value:function(e,t,n){var i;this._hasDisabled()||(this._isObjectParams(e)&&(e=(i=e).properties,t=i.time,n=i.onComplete),PropertyChecker.propertiesMust(e)||!this.config.strict?(t=_.isDate(t)?t:new Date,this._isReady()?this._sendRequest({type:"user_set",properties:e,onComplete:n},t):this._queue.push(["userSet",[e,t,n]])):(logger.warn("calling userSet failed due to invalid arguments"),_.isFunction(n)&&n({code:-1,msg:"invalid parameters"})))}},{key:"userSetOnce",value:function(e,t,n){var i;this._hasDisabled()||(this._isObjectParams(e)&&(e=(i=e).properties,t=i.time,n=i.onComplete),PropertyChecker.propertiesMust(e)||!this.config.strict?(t=_.isDate(t)?t:new Date,this._isReady()?this._sendRequest({type:"user_setOnce",properties:e,onComplete:n},t):this._queue.push(["userSetOnce",[e,t,n]])):(logger.warn("calling userSetOnce failed due to invalid arguments"),_.isFunction(n)&&n({code:-1,msg:"invalid parameters"})))}},{key:"userUnset",value:function(e,t,n){var i;this._hasDisabled()||(this._isObjectParams(i)&&(e=i.property,t=i.time,n=i.onComplete),PropertyChecker.propertyName(e)||!this.config.strict?(t=_.isDate(t)?t:new Date,this._isReady()?((i={})[e]=0,this._sendRequest({type:"user_unset",properties:i,onComplete:n},t)):this._queue.push(["userUnset",[e,n,t]])):(logger.warn("calling userUnset failed due to invalid arguments"),_.isFunction(n)&&n({code:-1,msg:"invalid parameters"})))}},{key:"userDel",value:function(e,t){var n;this._hasDisabled()||(this._isObjectParams(e)&&(e=(n=e).time,t=n.onComplete),e=_.isDate(e)?e:new Date,this._isReady()?this._sendRequest({type:"user_del",onComplete:t},e):this._queue.push(["userDel",[e,t]]))}},{key:"userAdd",value:function(e,t,n){var i;this._hasDisabled()||(this._isObjectParams(e)&&(e=(i=e).properties,t=i.time,n=i.onComplete),PropertyChecker.userAddProperties(e)||!this.config.strict?(t=_.isDate(t)?t:new Date,this._isReady()?this._sendRequest({type:"user_add",properties:e,onComplete:n},t):this._queue.push(["userAdd",[e,t,n]])):(logger.warn("calling userAdd failed due to invalid arguments"),_.isFunction(n)&&n({code:-1,msg:"invalid parameters"})))}},{key:"userAppend",value:function(e,t,n){var i;this._hasDisabled()||(this._isObjectParams(e)&&(e=(i=e).properties,t=i.time,n=i.onComplete),PropertyChecker.userAppendProperties(e)||!this.config.strict?(t=_.isDate(t)?t:new Date,this._isReady()?this._sendRequest({type:"user_append",properties:e,onComplete:n},t):this._queue.push(["userAppend",[e,t,n]])):(logger.warn("calling userAppend failed due to invalid arguments"),_.isFunction(n)&&n({code:-1,msg:"invalid parameters"})))}},{key:"userUniqAppend",value:function(e,t,n){var i;this._hasDisabled()||(this._isObjectParams(e)&&(e=(i=e).properties,t=i.time,n=i.onComplete),PropertyChecker.userAppendProperties(e)||!this.config.strict?(t=_.isDate(t)?t:new Date,this._isReady()?this._sendRequest({type:"user_uniq_append",properties:e,onComplete:n},t):this._queue.push(["userUniqAppend",[e,t,n]])):(logger.warn("calling userAppend failed due to invalid arguments"),_.isFunction(n)&&n({code:-1,msg:"invalid parameters"})))}},{key:"flush",value:function(){this.batchConsumer&&"none"===this.config.debugMode&&this.batchConsumer.flush()}},{key:"authorizeOpenID",value:function(e){this.identify(e)}},{key:"identify",value:function(e){if(!this._hasDisabled()){if("number"==typeof e)e=String(e);else if("string"!=typeof e)return!1;this.store.setDistinctId(e),logger.info("Setting distinct ID, DistinctId = "+e)}}},{key:"getDistinctId",value:function(){return this.store.getDistinctId()}},{key:"login",value:function(e){if(!this._hasDisabled()){if("number"==typeof e)e=String(e);else if("string"!=typeof e)return!1;this.store.setAccountId(e),logger.info("Login SDK, AccountId = "+e)}}},{key:"getAccountId",value:function(){return this.store.getAccountId()}},{key:"logout",value:function(){this._hasDisabled()||(this.store.setAccountId(null),logger.info("Logout SDK"))}},{key:"setSuperProperties",value:function(e){this._hasDisabled()||(PropertyChecker.propertiesMust(e)||!this.config.strict?this.store.setSuperProperties(e):logger.warn("setSuperProperties parameter must be a valid property value"))}},{key:"clearSuperProperties",value:function(){this._hasDisabled()||this.store.setSuperProperties({},!0)}},{key:"unsetSuperProperty",value:function(e){var t;this._hasDisabled()||_.isString(e)&&(delete(t=this.getSuperProperties())[e],this.store.setSuperProperties(t,!0))}},{key:"getSuperProperties",value:function(){return this.store.getSuperProperties()}},{key:"getPresetProperties",value:function(){var e=systemInformation.properties,t={},n=e["#os"];t.os=_.isUndefined(n)?"":n;n=e["#screen_width"];t.screenWidth=_.isUndefined(n)?0:n;n=e["#screen_height"];t.screenHeight=_.isUndefined(n)?0:n;n=e["#network_type"];t.networkType=_.isUndefined(n)?"":n;n=e["#device_model"];t.deviceModel=_.isUndefined(n)?"":n;n=e["#os_version"];t.osVersion=_.isUndefined(n)?"":n,t.deviceId=this.getDeviceId();var i=_.getTimeZone(new Date,this.config.zoneOffset);t.zoneOffset=i;e=e["#manufacturer"];return t.manufacturer=_.isUndefined(e)?"":e,t.toEventPresetProperties=function(){return{"#device_model":t.deviceModel,"#device_id":t.deviceId,"#screen_width":t.screenWidth,"#screen_height":t.screenHeight,"#os":t.os,"#os_version":t.osVersion,"#network_type":t.networkType,"#zone_offset":i,"#manufacturer":t.manufacturer}},t}},{key:"setDynamicSuperProperties",value:function(e){this._hasDisabled()||("function"==typeof e?PropertyChecker.properties(e())||!this.config.strict?this.dynamicProperties=e:logger.warn("A dynamic public property must return a valid property value"):logger.warn("setDynamicSuperProperties parameter must be a function type"))}},{key:"timeEvent",value:function(e,t){this._hasDisabled()||(t=_.isDate(t)?t:new Date,this._isReady()?PropertyChecker.event(e)||!this.config.strict?this.store.setEventTimer(e,t.getTime()):logger.warn("calling timeEvent failed due to invalid eventName: "+e):this._queue.push(["timeEvent",[e,t]]))}},{key:"getDeviceId",value:function(){return systemInformation.properties["#device_id"]}},{key:"enableTracking",value:function(e){this.enabled=e,this.store._set("ta_enabled",e)}},{key:"optOutTracking",value:function(){this.store.setSuperProperties({},!0),this.store.setDistinctId(_.UUID()),this.store.setAccountId(null),this._queue.splice(0,this._queue.length),this.isOptOut=!0,this.store._set("ta_isOptOut",!0)}},{key:"optOutTrackingAndDeleteUser",value:function(){var e=new Date;this._sendRequest({type:"user_del"},e),this.optOutTracking()}},{key:"optInTracking",value:function(){this.isOptOut=!1,this.store._set("ta_isOptOut",!1)}},{key:"setTrackStatus",value:function(e){switch(e){case"PAUSE":this.eventSaveOnly=!1,this.optInTracking(),this.enableTracking(!1);break;case"STOP":this.eventSaveOnly=!1,this.optOutTracking(!0);break;case"SAVE_ONLY":break;case"NORMAL":default:this.eventSaveOnly=!1,this.optInTracking(),this.enableTracking(!0)}logger.info("Change Status to "+e)}}]),n}(),TDAnalytics=function(){function e(){_classCallCheck(this,e)}return _createClass(e,null,[{key:"_shareInstance",value:function(e){return void 0!==this._instanceMaps[e]?this._instanceMaps[e]:void 0!==this._defaultInstance?this._defaultInstance:void 0}},{key:"init",value:function(e){var t=new ThinkingDataAPI(e);t.init(),void 0!==t&&(void 0===this._defaultInstance&&(this._defaultInstance=t,this._instanceMaps={}),this._instanceMaps[e.appId]=t)}},{key:"lightInstance",value:function(e){return this._shareInstance(e).lightInstance()}},{key:"track",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).track(e.eventName,e.properties,e.time,e.onComplete)}},{key:"trackFirst",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).trackFirstEvent(e)}},{key:"trackUpdate",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).trackUpdate(e)}},{key:"trackOverwrite",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).trackOverwrite(e)}},{key:"timeEvent",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).timeEvent(e.eventName,e.time)}},{key:"userSet",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).userSet(e.properties,e.time,e.onComplete)}},{key:"userSetOnce",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).userSetOnce(e.properties,e.time,e.onComplete)}},{key:"userUnset",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).userUnset(e.property,e.time,e.onComplete)}},{key:"userAdd",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).userAdd(e.properties,e.time,e.onComplete)}},{key:"userAppend",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).userAppend(e.properties,e.time,e.onComplete)}},{key:"userUniqAppend",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).userUniqAppend(e.properties,e.time,e.onComplete)}},{key:"userDelete",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).userDel(e.time,e.onComplete)}},{key:"setSuperProperties",value:function(e,t){t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).setSuperProperties(e)}},{key:"unsetSuperProperty",value:function(e,t){t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).unsetSuperProperty(e)}},{key:"clearSuperProperties",value:function(e){this._shareInstance(e).clearSuperProperties()}},{key:"getSuperProperties",value:function(e){return this._shareInstance(e).getSuperProperties()}},{key:"setDynamicSuperProperties",value:function(e,t){t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).setDynamicSuperProperties(e)}},{key:"getPresetProperties",value:function(e){return this._shareInstance(e).getPresetProperties()}},{key:"login",value:function(e,t){t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).login(e)}},{key:"logout",value:function(e){this._shareInstance(e).logout()}},{key:"setDistinctId",value:function(e,t){t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).identify(e)}},{key:"getDistinctId",value:function(e){return this._shareInstance(e).getDistinctId()}},{key:"getAccountId",value:function(e){return this._shareInstance(e).getAccountId()}},{key:"getSDKVersion",value:function(){return Config.LIB_VERSION}},{key:"getDeviceId",value:function(e){return this._shareInstance(e).getDeviceId()}},{key:"flush",value:function(e){this._shareInstance(e).flush()}},{key:"setTrackStatus",value:function(e,t){t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).setTrackStatus(e)}},{key:"ThinkingDataAPI",value:function(){return ThinkingDataAPI}}]),e}();

var config = {
    appId: "dfd26fbf6bf64241bd50345daa2de030", // 项目 APP ID
    serverUrl: "https://ta.shan-yu-tech.com", // 上报地址
    autoTrack: {
        appShow: true, // 自动采集 ta_mg_show
        appHide: true // 自动采集 ta_mg_hide
    }
};
TDAnalytics.init(config);
TDAnalytics.track({
    eventName: "sdk_init", // 事件名称
    properties: {
    }
});
!function (e, t) {
    "object" == typeof exports && "undefined" != typeof module ? module.exports = t() : "function" == typeof define && define.amd ? define(t) : (e = "undefined" != typeof globalThis ? globalThis : e || self).SQSDK = t()
}(this, function () {
    return {
        isOpenLog: false,
        sversion: "1.0.0",
        signKey: "Jp*4Y8vQOYck2*&Z",
        devInfo: {},
        originApi: {
            ptoken:"https://pvt-api.37.com.cn/h5verify/ptoken",
            enter: "https://m-api.37.com.cn/h5sdk/enter",
            order: "https://mpay-api.37.com.cn/h5sdk/order",
            role_report: "https://report-api.39ej7e.com/createrole"
        },
        api: {},
        pid: "",
        gid: "",
        uid: "",
        openid: "",
        refer: "",
        token: "",
        rewardedVideoAd: null,
        active(params, successCallback, failCallback) {
            this.sendTTEvent("activate", this.getCommonParams({}))
            this.log("SDK激活:" + JSON.stringify(params))
            params = this.getCommonParams(params)
            params.sign = this.getSign(params)
            this.post("https://m-api.37.com.cn/h5sdk/active", params, (res) => {
                this.sendTTEvent("activate_succ", this.getCommonParams({}))
                this.log("SDK激活成功:" + JSON.stringify(res))
                this.api = res.api
                successCallback(params)
            }, (res) => {
                this.sendTTEvent("activate_fail", this.getCommonParams({}))
                this.log("SDK激活失败:" + JSON.stringify(res))
                failCallback(res)
            })
        },
        login(params, successCallback, failCallback) {
            this.isOpenLog = params.closeLog
            this.pid = params.pid
            this.gid = params.gid
            let result = ks.getLaunchOptionsSync()
            this.sendTTEvent("ad_data", this.getCommonParams({pt_ad: JSON.stringify(result)}))
            if(result.query != undefined){
                var cid = result.query.cid
                var no = result.query.num
                if(cid != undefined && no != undefined){
                    this.refer = this.pid + "_" + this.gid + "_"+cid+"_"+no
                }else{
                    this.refer = this.pid + "_" + this.gid + "_0_0"
                }
            }else{
                this.refer = this.pid + "_" + this.gid + "_0_0"
            }
            
            this.sendTTEvent("login_invoke", this.getCommonParams({}))
            this.active(params, () => {
                this.log("SQSDK 调起登录:" + JSON.stringify(params))
                this.sendTTEvent("login", this.getCommonParams({}))
                ks.login({
                    success: (res) => {
                        console.log("SQSDK 快手登录成功:", res)
                        res.pt_ad = this.encode(JSON.stringify(result));
                        this.tokenVerify(res, (data) => {
                            this.log("SQSDK 检验成功:" + JSON.stringify(data))
                            this.token = data.token
                            this.uid = data.uid
                            this.openid = data.puid
                            TDAnalytics.login(this.uid);
                            this.sendTTEvent("login_succ", this.getCommonParams({}))
                            successCallback({
                                pid: this.pid,
                                gid: this.gid,
                                uid: this.uid,
                                sigh: "",
                                openid: this.openid,
                                token: this.token,
                                time: data.time,
                                refer: this.refer,
                                user: {
                                    disname:"",
                                    nurl:"",
                                    openid:this.openid,
                                    puid:this.openid,
                                    puname:"",
                                    pwd:"",
                                    ratio: "10",
                                    session_key: "",
                                    sign:"",
                                    time:data.time,
                                    token: this.token,
                                    uid:this.uid,
                                    uname:"",
                                    utype:1
                                }
                            })
                        }, (data) => {
                            this.log("检验失败:"+JSON.stringify(data))
                            this.sendTTEvent("login_fail", this.getCommonParams(data))
                            this.failCallbackReturn(failCallback,-1,JSON.stringify(data))
                        })
                    },
                    fail: (res) => {
                        console.log("SQSDK 快手登录失败:", res)
                        this.sendTTEvent("login_fail", this.getCommonParams(res))
                        this.failCallbackReturn(failCallback,-1,JSON.stringify(res))
                    }
                });
            }, (res) => {
                this.log("SQSDK 激活失败")
                this.failCallbackReturn(failCallback,-1,JSON.stringify(res))
            })
        },
        tokenVerify(params, successCallback, failCallback) {
            console.log("开始校验token")
            var data = {
                pdata: JSON.stringify(params),
                ptoken: "null",
                refer: this.refer
            }
            data = this.getCommonParams(data)
            data.sign = this.getSign(data)
            this.post(this.getApiValue("ptoken"), data, (res) => {
                successCallback(res)
            }, (res) => {
                failCallback(res)
            })
        },
        addShortcut(params, callback){
            ks.addShortcut({
                success:()=> {
                    this.log("添加桌面成功");
                    callback({state:1,msg:"添加桌面成功"})
                },
                fail:(err)=> {
                    this.log("添加桌面失败"+JSON.stringify(err))
                    if (err.code === -10005) {
                        callback({state:err.code,msg:"暂不支持该功能"})
                    } else {
                        callback({state:err.code,msg:err.msg})
                    }
                }
            });
        },
        share(params, callback){
            ks.shareAppMessage({
                success:()=> {
                    this.log("分享成功");
                    callback({state:1,msg:"分享成功"})
                },
                fail:(err)=> {
                    this.log("分享失败"+JSON.stringify(err))
                    callback({state:err.code,msg:err.msg})
                },
            })
        },
        pay(params, successCallback, failCallback) {
            this.log("SQSDK 发起支付:" + JSON.stringify(params))
            this.sendTTEvent("pay_invoke", this.getCommonParams({}))
            this.sendTTEvent("pay_order", this.getCommonParams({}))
            this.createOrder(params, (res) => {
                this.sendTTEvent("pay_order_succ", this.getCommonParams({}))
                this.sendTTEvent("pay_official", this.getCommonParams({}))
                this.channelPay(res.pparam, (res) => {
                    console.log("支付成功" + JSON.stringify(res))
                    successCallback({
                        code: -1,
                        msg: "支付成功"
                    })
                }, (res) => {
                    console.log("支付失败" + JSON.stringify(res))
                    this.failCallbackReturn(failCallback,-1,JSON.stringify(res))
                })
            }, (res) => {
                this.sendTTEvent("pay_order_fail", this.getCommonParams({}))
                this.failCallbackReturn(failCallback,-1,JSON.stringify(res))
            })
        },
        createOrder(params, successCallback, failCallback) {
            this.log("SQSDK 创建订单:" + JSON.stringify(params))
            var pdata = {
                platform: this.devInfo.platform
            }
            params.pdata = JSON.stringify(pdata)
            params.token = this.token
            params.uid = this.uid
            params = this.getCommonParams(params)
            params.sign = this.getSign(params)
            this.post(this.getApiValue("order"), params, (res) => {
                successCallback(res)
            }, (res) => {
                failCallback(res)
            })
        },
        channelPay(params, successCallback, failCallback) {
            this.log("开始快手支付:" + JSON.stringify(params))
            params.success = (result) => {
                this.log("快手支付成功:" + JSON.stringify(result))
                successCallback(result)
            }
            params.fail = (result) => {
                this.log("快手支付失败:" + JSON.stringify(result))
                failCallback(result)
            }
            ks.requestGamePayment(params);
        },
        createRole(params) {
            this.log("创角上报")
            params = this.getCommonParams(params)
            params.sign = this.getSign(params)
            this.sendTTEvent("create_role_complete",params)
            this.get(this.getApiValue("role_report"),params,(res)=>{
                this.log("创角上报成功",res)
            },(res)=>{
                this.log("创角上报失败:"+JSON.stringify(res))
            })
        },
        enterGame(params) {
            console.log("进服上报")
            params = this.getCommonParams(params)
            params.token = this.token
            params.sign = this.getSign(params)
            this.sendTTEvent("enter_game",params)
                this.get(this.getApiValue("enter"),params,(res)=>{
                this.log("进服上报成功")
            },(res)=>{
                this.log("进服上报失败:"+JSON.stringify(res))
            })
        },
        dataReport(params, callback) {
            params = this.getCommonParams(params)
            this.sendTTEvent(params.event_name,params)
            if(callback != null || callback !== undefined){
                callback({
                    state : 1,
                    msg: "成功"
                })
            }
        },
        payCallbackReport(params, callback) {
            params = this.getCommonParams(params)
            this.sendTTEvent("payCallbackReport",params) 
            if(callback != null || callback !== undefined){
                callback({
                    state : 1,
                    msg: "成功"
                })
            }
        },
        createRewardedVideoAd(params, callback){
            this.log("研发调用创建激励广告:"+JSON.stringify(params))
            if(!params.adUnitId){
                this.log("广告id为null")
                callback({state:-1,msg:"广告id为null"})
            }
            this.rewardedVideoAd = ks.createRewardedVideoAd({ adUnitId: params.adUnitId })
            if (this.rewardedVideoAd) {
                this.rewardedVideoAd.onClose(res => {
                    // 用户点击了【关闭广告】按钮
                    if (res && res.isEnded || res === undefined) {
                        params.onClose({isEnded:res.isEnded})
                    }
                    else {
                        params.onClose({isEnded:false})
                    }
                })
                callback({state: 1, msg: "创建激励广告成功", data: this.rewardedVideoAd})
            } else {
                callback({state:-1, msg:"创建激励广告失败"})
            }
        },
        showRewardedVideoAd(params, callback){
            this.log("研发调用展示激励广告:"+JSON.stringify(params))
            if(this.rewardedVideoAd == null){
                this.log("请先创建广告")
                callback({state:-1,msg:"请先创建广告"})
            }
            this.rewardedVideoAd.show()
            .then(() => callback({state:1,msg:"激励视频 广告显示"}))            
        },
        toast(msg) {
            ks.showToast({
                title: msg,
                duration: 2000
            })

        },
        saveData(key, value) {
            try {
                ks.setStorageSync(key, value)
            } catch (e) { }

        },
        readData(key) {
            try {
                var value = ks.getStorageSync(key)
                if (value) {
                    return value
                }
                return ""
            } catch (e) {
                this.log(e)
                return ""
            }
        },
        failCallbackReturn(failCallback,code,msg){
            failCallback({
                code: code,
                msg: msg
            })
        },
        //获取当前设备号
        getDev() {
            var dev = this.readData("dev")
            if(dev == ""){
                dev = this.fn_Guid()
                this.saveData("dev",dev)
            }
            return dev
        },
        fn_Guid() {
            return 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                var r = Math.random() * 16 | 0,v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);});
        },
        //获取当前系统信息
        getCurrentSystemInfo() {
            try {
                this.devInfo = ks.getSystemInfoSync()
            } catch (e) {
                // Do something when catch error
            }
        },
        //添加公共参数
        getCommonParams(params) {
            // 获取当前的毫秒级时间戳
            const currentTimeInMilliseconds = Date.now();
            // 将毫秒级时间戳转换为秒级时间戳
            const currentTimeInSeconds = Math.floor(currentTimeInMilliseconds / 1000);
            params.pid = this.pid
            params.gid = this.gid
            params.sversion = this.sversion
            params.time = currentTimeInSeconds
            params.dev = this.getDev()
            return params
        },
        setTTCommonParams(){
            var superProperties = {
                uid : this.uid, 
                refer : this.refer,
                openid : this.openid,
                brand : this.devInfo.brand,
                model : this.devInfo.model,
                os_version : this.devInfo.platform + " " + this.devInfo.system,
                platform : this.devInfo.platform,
                pixelRatio: this.devInfo.pixelRatio,
                screen_height: this.devInfo.screenHeight,
                screen_width : this.devInfo.screenWidth,
            };
            TDAnalytics.setSuperProperties(superProperties);//设置公共事件属性
            
        },
        //获取签名
        getSign(params) {
            return this.getObjectMD5(params)
        },
        getApiValue(apiName) {
            if (this.api[apiName] !== null && this.api[apiName] !== "") {
                this.log("服务端返回的api存在:"+apiName)
                return this.api[apiName];
            } else {
                this.log("服务端返回的api不存在:"+apiName)
                return this.originApi[apiName] || ""; // 如果originApi中也没有，则返回“”
            }
        },
        post(postUrl, params, successCallback, failCallback) {
            this.log("网络请求:" + postUrl + "\n" + JSON.stringify(params))
            ks.request({
                url: postUrl, //仅为示例，并非真实的接口地址
                data: params,
                method: "POST",
                header: {
                    'content-type': 'application/x-www-form-urlencoded' // 默认值
                },
                success:(res)=>{
                    this.log("SQSDK 网络返回值:",res)
                    if (res.data.state == 1) {
                        successCallback(res.data.data)
                    } else {
                        failCallback(res.data)
                    }
                },
                fail:(res)=> {
                    this.log("SQSDK 网络返回值:",res)
                    failCallback(res.data)
                }
            })
        },
        get(postUrl, params, successCallback, failCallback){
            this.log("网络请求:" + postUrl + "\n" + JSON.stringify(params))
            ks.request({
                url: postUrl, //仅为示例，并非真实的接口地址
                data: params,
                method: "GET",
                header: {
                    'content-type': 'application/x-www-form-urlencoded' // 默认值
                },
                success:(res)=>{
                    this.log("SQSDK 网络返回值:",res)
                    if (res.data.state == 1) {
                        successCallback(res.data.data)
                    } else {
                        failCallback(res.data)
                    }
                },
                fail(res) {
                    this.log("SQSDK 网络返回值:",res)
                    failCallback(res.data)
                }
            })
        },
        log(params) {
            if (!this.isOpenLog) {
                console.log(params)
            }
        },
        getObjectMD5(obj) {
            const sortedKeys = Object.keys(obj).sort();
            const keyValuePairs = [];
            sortedKeys.forEach(key => {
                const value = obj[key];
                if (value != undefined) {
                    const pair = `${key}=${typeof value === 'object' ? JSON.stringify(value) : value.toString()}`;
                    // 将这个键值对字符串添加到数组中
                    keyValuePairs.push(pair);
                } else {
                    this.log("数据异常:" + key + " : " + value)
                }
            });
            const valuesString = keyValuePairs.join('') + this.signKey;
            console.log("md5:" + valuesString);
            return md5(valuesString);
        },
        sendTTEvent(name, data) {
            this.getCurrentSystemInfo()
            this.setTTCommonParams()
            TDAnalytics.track({
                eventName: name, // 事件名称
                properties: data
            });
        },

  _keyStr: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",
  encode (str) { // 加密
    var output = "";
    var chr1, chr2, chr3, enc1, enc2, enc3, enc4;
    var i = 0;
    str = this._utf16to8(str);
    while (i < str.length) {
      chr1 = str.charCodeAt(i++);
      chr2 = str.charCodeAt(i++);
      chr3 = str.charCodeAt(i++);
      enc1 = chr1 >> 2;
      enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
      enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
      enc4 = chr3 & 63;
      if (isNaN(chr2)) {
        enc3 = enc4 = 64;
      } else if (isNaN(chr3)) {
        enc4 = 64;
      }
      output = output + this._keyStr.charAt(enc1) + this._keyStr.charAt(enc2) + this._keyStr.charAt(enc3) + this._keyStr.charAt(enc4);
    } return output;
  },
  decode (input) { // 解密
    var output = "";
    var chr1, chr2, chr3;
    var enc1, enc2, enc3, enc4;
    var i = 0;
    input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");
    while (i < input.length) {
        enc1 = this._keyStr.indexOf(input.charAt(i++));
        enc2 = this._keyStr.indexOf(input.charAt(i++));
        enc3 = this._keyStr.indexOf(input.charAt(i++));
        enc4 = this._keyStr.indexOf(input.charAt(i++));
        chr1 = (enc1 << 2) | (enc2 >> 4);
        chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
        chr3 = ((enc3 & 3) << 6) | enc4;
        output = output + String.fromCharCode(chr1);
        if (enc3 != 64) {
            output = output + String.fromCharCode(chr2);
        }
        if (enc4 != 64) {
            output = output + String.fromCharCode(chr3);
        }
    } return this._utf8to16(output);
  },
  _utf16to8: function(str) {
    var out, i, len, c;
    out = "";
    len = str.length;
    for(i = 0; i < len; i++) {
        c = str.charCodeAt(i);
        if ((c >= 0x0001) && (c <= 0x007F)) {
            out += str.charAt(i);
        } else if (c > 0x07FF) {
            out += String.fromCharCode(0xE0 | ((c >> 12) & 0x0F));
            out += String.fromCharCode(0x80 | ((c >> 6) & 0x3F));
            out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
        } else {
            out += String.fromCharCode(0xC0 | ((c >> 6) & 0x1F));
            out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
        }
    }
    return out;
  },
  _utf8to16 (str) { 
    var out, i, len, c;
    var char2, char3;
    out = "";
    len = str.length;
    i = 0;
    while(i < len) {
        c = str.charCodeAt(i++);
        switch(c >> 4)
        {
            case 0: case 1: case 2: case 3: case 4: case 5: case 6:case7:
              out += str.charAt(i-1);
            break;
            case 12: case 13:
              char2 = str.charCodeAt(i++);
              out += String.fromCharCode(((c & 0x1F) << 6) | (char2&0x3F));
            break;
            case 14:
              char2 = str.charCodeAt(i++);
              char3 = str.charCodeAt(i++);
              out += String.fromCharCode(((c & 0x0F) << 12) |
                ((char2 & 0x3F) << 6) |
                ((char3 & 0x3F) << 0));
            break;
        }
    } return out;
  }
    };
});

// md5.js
function md5(string) {
    function md5_RotateLeft(lValue, iShiftBits) {
        return (lValue << iShiftBits) | (lValue >>> (32 - iShiftBits));
    }

    function md5_AddUnsigned(lX, lY) {
        var lX4, lY4, lX8, lY8, lResult;
        lX8 = (lX & 0x80000000);
        lY8 = (lY & 0x80000000);
        lX4 = (lX & 0x40000000);
        lY4 = (lY & 0x40000000);
        lResult = (lX & 0x3FFFFFFF) + (lY & 0x3FFFFFFF);
        if (lX4 & lY4) {
            return (lResult ^ 0x80000000 ^ lX8 ^ lY8);
        }
        if (lX4 | lY4) {
            if (lResult & 0x40000000) {
                return (lResult ^ 0xC0000000 ^ lX8 ^ lY8);
            } else {
                return (lResult ^ 0x40000000 ^ lX8 ^ lY8);
            }
        } else {
            return (lResult ^ lX8 ^ lY8);
        }
    }

    function md5_F(x, y, z) {
        return (x & y) | ((~x) & z);
    }

    function md5_G(x, y, z) {
        return (x & z) | (y & (~z));
    }

    function md5_H(x, y, z) {
        return (x ^ y ^ z);
    }

    function md5_I(x, y, z) {
        return (y ^ (x | (~z)));
    }

    function md5_FF(a, b, c, d, x, s, ac) {
        a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_F(b, c, d), x), ac));
        return md5_AddUnsigned(md5_RotateLeft(a, s), b);
    };

    function md5_GG(a, b, c, d, x, s, ac) {
        a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_G(b, c, d), x), ac));
        return md5_AddUnsigned(md5_RotateLeft(a, s), b);
    };

    function md5_HH(a, b, c, d, x, s, ac) {
        a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_H(b, c, d), x), ac));
        return md5_AddUnsigned(md5_RotateLeft(a, s), b);
    };

    function md5_II(a, b, c, d, x, s, ac) {
        a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_I(b, c, d), x), ac));
        return md5_AddUnsigned(md5_RotateLeft(a, s), b);
    };

    function md5_ConvertToWordArray(string) {
        var lWordCount;
        var lMessageLength = string.length;
        var lNumberOfWords_temp1 = lMessageLength + 8;
        var lNumberOfWords_temp2 = (lNumberOfWords_temp1 - (lNumberOfWords_temp1 % 64)) / 64;
        var lNumberOfWords = (lNumberOfWords_temp2 + 1) * 16;
        var lWordArray = Array(lNumberOfWords - 1);
        var lBytePosition = 0;
        var lByteCount = 0;
        while (lByteCount < lMessageLength) {
            lWordCount = (lByteCount - (lByteCount % 4)) / 4;
            lBytePosition = (lByteCount % 4) * 8;
            lWordArray[lWordCount] = (lWordArray[lWordCount] | (string.charCodeAt(lByteCount) << lBytePosition));
            lByteCount++;
        }
        lWordCount = (lByteCount - (lByteCount % 4)) / 4;
        lBytePosition = (lByteCount % 4) * 8;
        lWordArray[lWordCount] = lWordArray[lWordCount] | (0x80 << lBytePosition);
        lWordArray[lNumberOfWords - 2] = lMessageLength << 3;
        lWordArray[lNumberOfWords - 1] = lMessageLength >>> 29;
        return lWordArray;
    };

    function md5_WordToHex(lValue) {
        var WordToHexValue = "",
            WordToHexValue_temp = "",
            lByte, lCount;
        for (lCount = 0; lCount <= 3; lCount++) {
            lByte = (lValue >>> (lCount * 8)) & 255;
            WordToHexValue_temp = "0" + lByte.toString(16);
            WordToHexValue = WordToHexValue + WordToHexValue_temp.substr(WordToHexValue_temp.length - 2, 2);
        }
        return WordToHexValue;
    };

    function md5_Utf8Encode(string) {
        string = string.replace(/\r\n/g, "\n");
        var utftext = "";
        for (var n = 0; n < string.length; n++) {
            var c = string.charCodeAt(n);
            if (c < 128) {
                utftext += String.fromCharCode(c);
            } else if ((c > 127) && (c < 2048)) {
                utftext += String.fromCharCode((c >> 6) | 192);
                utftext += String.fromCharCode((c & 63) | 128);
            } else {
                utftext += String.fromCharCode((c >> 12) | 224);
                utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                utftext += String.fromCharCode((c & 63) | 128);
            }
        }
        return utftext;
    };
    var x = Array();
    var k, AA, BB, CC, DD, a, b, c, d;
    var S11 = 7,
        S12 = 12,
        S13 = 17,
        S14 = 22;
    var S21 = 5,
        S22 = 9,
        S23 = 14,
        S24 = 20;
    var S31 = 4,
        S32 = 11,
        S33 = 16,
        S34 = 23;
    var S41 = 6,
        S42 = 10,
        S43 = 15,
        S44 = 21;
    string = md5_Utf8Encode(string);
    x = md5_ConvertToWordArray(string);
    a = 0x67452301;
    b = 0xEFCDAB89;
    c = 0x98BADCFE;
    d = 0x10325476;
    for (k = 0; k < x.length; k += 16) {
        AA = a;
        BB = b;
        CC = c;
        DD = d;
        a = md5_FF(a, b, c, d, x[k + 0], S11, 0xD76AA478);
        d = md5_FF(d, a, b, c, x[k + 1], S12, 0xE8C7B756);
        c = md5_FF(c, d, a, b, x[k + 2], S13, 0x242070DB);
        b = md5_FF(b, c, d, a, x[k + 3], S14, 0xC1BDCEEE);
        a = md5_FF(a, b, c, d, x[k + 4], S11, 0xF57C0FAF);
        d = md5_FF(d, a, b, c, x[k + 5], S12, 0x4787C62A);
        c = md5_FF(c, d, a, b, x[k + 6], S13, 0xA8304613);
        b = md5_FF(b, c, d, a, x[k + 7], S14, 0xFD469501);
        a = md5_FF(a, b, c, d, x[k + 8], S11, 0x698098D8);
        d = md5_FF(d, a, b, c, x[k + 9], S12, 0x8B44F7AF);
        c = md5_FF(c, d, a, b, x[k + 10], S13, 0xFFFF5BB1);
        b = md5_FF(b, c, d, a, x[k + 11], S14, 0x895CD7BE);
        a = md5_FF(a, b, c, d, x[k + 12], S11, 0x6B901122);
        d = md5_FF(d, a, b, c, x[k + 13], S12, 0xFD987193);
        c = md5_FF(c, d, a, b, x[k + 14], S13, 0xA679438E);
        b = md5_FF(b, c, d, a, x[k + 15], S14, 0x49B40821);
        a = md5_GG(a, b, c, d, x[k + 1], S21, 0xF61E2562);
        d = md5_GG(d, a, b, c, x[k + 6], S22, 0xC040B340);
        c = md5_GG(c, d, a, b, x[k + 11], S23, 0x265E5A51);
        b = md5_GG(b, c, d, a, x[k + 0], S24, 0xE9B6C7AA);
        a = md5_GG(a, b, c, d, x[k + 5], S21, 0xD62F105D);
        d = md5_GG(d, a, b, c, x[k + 10], S22, 0x2441453);
        c = md5_GG(c, d, a, b, x[k + 15], S23, 0xD8A1E681);
        b = md5_GG(b, c, d, a, x[k + 4], S24, 0xE7D3FBC8);
        a = md5_GG(a, b, c, d, x[k + 9], S21, 0x21E1CDE6);
        d = md5_GG(d, a, b, c, x[k + 14], S22, 0xC33707D6);
        c = md5_GG(c, d, a, b, x[k + 3], S23, 0xF4D50D87);
        b = md5_GG(b, c, d, a, x[k + 8], S24, 0x455A14ED);
        a = md5_GG(a, b, c, d, x[k + 13], S21, 0xA9E3E905);
        d = md5_GG(d, a, b, c, x[k + 2], S22, 0xFCEFA3F8);
        c = md5_GG(c, d, a, b, x[k + 7], S23, 0x676F02D9);
        b = md5_GG(b, c, d, a, x[k + 12], S24, 0x8D2A4C8A);
        a = md5_HH(a, b, c, d, x[k + 5], S31, 0xFFFA3942);
        d = md5_HH(d, a, b, c, x[k + 8], S32, 0x8771F681);
        c = md5_HH(c, d, a, b, x[k + 11], S33, 0x6D9D6122);
        b = md5_HH(b, c, d, a, x[k + 14], S34, 0xFDE5380C);
        a = md5_HH(a, b, c, d, x[k + 1], S31, 0xA4BEEA44);
        d = md5_HH(d, a, b, c, x[k + 4], S32, 0x4BDECFA9);
        c = md5_HH(c, d, a, b, x[k + 7], S33, 0xF6BB4B60);
        b = md5_HH(b, c, d, a, x[k + 10], S34, 0xBEBFBC70);
        a = md5_HH(a, b, c, d, x[k + 13], S31, 0x289B7EC6);
        d = md5_HH(d, a, b, c, x[k + 0], S32, 0xEAA127FA);
        c = md5_HH(c, d, a, b, x[k + 3], S33, 0xD4EF3085);
        b = md5_HH(b, c, d, a, x[k + 6], S34, 0x4881D05);
        a = md5_HH(a, b, c, d, x[k + 9], S31, 0xD9D4D039);
        d = md5_HH(d, a, b, c, x[k + 12], S32, 0xE6DB99E5);
        c = md5_HH(c, d, a, b, x[k + 15], S33, 0x1FA27CF8);
        b = md5_HH(b, c, d, a, x[k + 2], S34, 0xC4AC5665);
        a = md5_II(a, b, c, d, x[k + 0], S41, 0xF4292244);
        d = md5_II(d, a, b, c, x[k + 7], S42, 0x432AFF97);
        c = md5_II(c, d, a, b, x[k + 14], S43, 0xAB9423A7);
        b = md5_II(b, c, d, a, x[k + 5], S44, 0xFC93A039);
        a = md5_II(a, b, c, d, x[k + 12], S41, 0x655B59C3);
        d = md5_II(d, a, b, c, x[k + 3], S42, 0x8F0CCC92);
        c = md5_II(c, d, a, b, x[k + 10], S43, 0xFFEFF47D);
        b = md5_II(b, c, d, a, x[k + 1], S44, 0x85845DD1);
        a = md5_II(a, b, c, d, x[k + 8], S41, 0x6FA87E4F);
        d = md5_II(d, a, b, c, x[k + 15], S42, 0xFE2CE6E0);
        c = md5_II(c, d, a, b, x[k + 6], S43, 0xA3014314);
        b = md5_II(b, c, d, a, x[k + 13], S44, 0x4E0811A1);
        a = md5_II(a, b, c, d, x[k + 4], S41, 0xF7537E82);
        d = md5_II(d, a, b, c, x[k + 11], S42, 0xBD3AF235);
        c = md5_II(c, d, a, b, x[k + 2], S43, 0x2AD7D2BB);
        b = md5_II(b, c, d, a, x[k + 9], S44, 0xEB86D391);
        a = md5_AddUnsigned(a, AA);
        b = md5_AddUnsigned(b, BB);
        c = md5_AddUnsigned(c, CC);
        d = md5_AddUnsigned(d, DD);
    }
    return (md5_WordToHex(a) + md5_WordToHex(b) + md5_WordToHex(c) + md5_WordToHex(d)).toLowerCase();
}
