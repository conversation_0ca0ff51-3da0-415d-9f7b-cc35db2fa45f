import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../privacy/privacy_manager.dart';

class TestPrivacyPage extends StatelessWidget {
  const TestPrivacyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('隐私政策测试'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '隐私政策测试页面',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () async {
                // 显示隐私弹窗
                final result = await PrivacyManager.showPrivacyDialog(context);
                if (result != null) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(result ? '用户同意' : '用户不同意'),
                    ),
                  );
                }
              },
              child: const Text('显示隐私弹窗'),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: () async {
                // 检查隐私政策状态
                final isAgreed = await PrivacyManager.isPrivacyAgreed();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('隐私政策状态: ${isAgreed ? "已同意" : "未同意"}'),
                  ),
                );
              },
              child: const Text('检查隐私政策状态'),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: () async {
                // 重置隐私政策状态
                await PrivacyManager.setPrivacyAgreed(false);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('已重置隐私政策状态'),
                  ),
                );
              },
              child: const Text('重置隐私政策状态'),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: () async {
                // 测试完整的隐私检查流程
                final result = await PrivacyManager.checkAndHandlePrivacy(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('隐私检查结果: ${result ? "通过" : "拒绝"}'),
                  ),
                );
              },
              child: const Text('测试完整隐私检查流程'),
            ),
          ],
        ),
      ),
    );
  }
} 