import 'package:flutter/material.dart';
import '../../common/dl_color.dart';
import '../../components/bottom_sheet_dialog.dart';

class BottomSheetDemoPage extends StatelessWidget {
  const BottomSheetDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('底部弹窗演示'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: () {
                _showGameSelectionDialog(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              ),
              child: const Text(
                '显示游戏选择弹窗',
                style: TextStyle(fontSize: 16),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              '点击按钮查看从下往上的抽拉弹窗效果',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showGameSelectionDialog(BuildContext context) {
    final gameItems = [
      const GameItem(
        name: '王者荣耀',
        icon: Icons.sports_esports,
        color: DLColor.primary,
      ),
      const GameItem(
        name: '和平精英',
        icon: Icons.gps_fixed,
        color: Colors.green,
      ),
      const GameItem(
        name: '原神',
        icon: Icons.landscape,
        color: Colors.blue,
      ),
      const GameItem(
        name: '英雄联盟',
        icon: Icons.emoji_events,
        color: Colors.purple,
      ),
      const GameItem(
        name: '阴阳师',
        icon: Icons.auto_awesome,
        color: Colors.red,
      ),
      const GameItem(
        name: '明日方舟',
        icon: Icons.security,
        color: Colors.teal,
      ),
      const GameItem(
        name: '崩坏星穹铁道',
        icon: Icons.rocket_launch,
        color: Colors.indigo,
      ),
      const GameItem(
        name: '第五人格',
        icon: Icons.psychology,
        color: Colors.brown,
      ),
      const GameItem(
        name: '光遇',
        icon: Icons.flight,
        color: Colors.pink,
      ),
    ];

    BottomSheetHelper.showGameSelectionDialog(
      context,
      title: '选择游戏',
      gameItems: gameItems,
      onClose: () {
        // 弹窗关闭回调
      },
    );
  }
} 