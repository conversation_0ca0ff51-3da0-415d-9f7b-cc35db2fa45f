import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import '../../net/gateway_encrypt_interceptor.dart';

/// 网关加密拦截器演示页面
class GateWayEncryptDemo extends StatefulWidget {
  const GateWayEncryptDemo({super.key});

  @override
  State<GateWayEncryptDemo> createState() => _GateWayEncryptDemoState();
}

class _GateWayEncryptDemoState extends State<GateWayEncryptDemo> {
  final List<String> _logs = [];
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('网关加密拦截器演示'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // 测试按钮区域
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testActivationApi,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('测试初始化接口'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 清空日志按钮
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _clearLogs,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('清空日志'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 日志显示区域
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListView.builder(
                  itemCount: _logs.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Text(
                        _logs[index],
                        style: const TextStyle(
                          fontSize: 12,
                          fontFamily: 'monospace',
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }



  /// 测试初始化接口
  Future<void> _testActivationApi() async {
    setState(() {
      _isLoading = true;
      _addLog('开始测试初始化接口...');
    });

    try {
      // 创建网关提供者
      final provider = DefaultGateWayProvider(
        key: 'soC2GAr8jN2fsbry',
        version: '',
      );

      // 创建Dio实例
      final dio = Dio(
        BaseOptions(
          baseUrl: 'https://m-api-secure.37.com.cn',
          connectTimeout: const Duration(seconds: 10),
          receiveTimeout: const Duration(seconds: 10),
          responseType: ResponseType.plain, // 改为plain类型，避免JSON解析错误
          validateStatus: (status) {
            // 接受所有状态码，让拦截器处理
            return status != null && status < 500;
          },
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        ),
      );

      // 添加网关加密拦截器
      dio.interceptors.add(
        GateWayEncryptInterceptor(
          provider: provider,
          exceptionReporter: (title, code, exception, data) {
            _addLog('网关加密异常: $title - $exception');
          },
        ),
      );

      // 添加日志拦截器
      dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (obj) => _addLog('Dio Log: $obj'),
      ));

      // 构建初始化接口参数（基于Android实际请求参数）
      final activationParams = {
        'gwversion': '4.6.4',
        'nwk': 'WIFI',
        'gid': '1000000',
        'bssid': '02:00:00:00:00:00',
        'pid': '1',
        'trans_info': '',
        'is_root': '0',
        'ssid': '',
        'mac': '020000000000',
        'mode': 'Redmi Note 8 Pro',
        'dev2': 'ededd6da713b66c46cf0777aa198c759',
        'dev': 'ededd6da713b66c46cf0777aa198c759',
        'sversion': '*******',
        'dpgn': 'com.sy.yxjun',
        'brand': 'Redmi',
        'hpi': '2220',
        'over': 'android11',
        'host_sdk_version': '*******',
        'os': 'android',
        'battery_level': '100',
        'display_name': 'sq-union:*******.2',
        'version': '*******',
        'versionCode': '24',
        'is_simulator': '0',
        'battery_status': '5',
        'wpi': '1080',
        'pluginVersion': '264',
        'phone': '',
        'refer': 'sy_00001',
        'os_desc': 'android',
        'imei': '99934665528722452',
        'time': (DateTime.now().millisecondsSinceEpoch / 1000).toString(),
        'android_id': '100a059eb38a2d9d',
        'sua': '1',
      };

      // 添加时间戳参数到URL
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final requestUrl = '/sdk/active?t=$timestamp';
      
      _addLog('发送初始化请求到: https://m-api-secure.37.com.cn$requestUrl');
      _addLog('请求参数: $activationParams');

      // 发送初始化请求
      final response = await dio.post(
        requestUrl,
        data: activationParams,
      );

      _addLog('初始化请求成功: 状态码 ${response.statusCode}');
      _addLog('响应数据: ${response.data}');

    } catch (e) {
      _addLog('初始化请求失败: $e');
      if (e is DioException) {
        _addLog('错误类型: ${e.type}');
        _addLog('错误响应: ${e.response?.data}');
        _addLog('错误状态码: ${e.response?.statusCode}');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 添加日志
  void _addLog(String message) {
    final timestamp = DateTime.now().toString().substring(11, 19);
    final log = '[$timestamp] $message';
    
    setState(() {
      _logs.add(log);
    });
  }

  /// 清空日志
  void _clearLogs() {
    setState(() {
      _logs.clear();
    });
  }
} 