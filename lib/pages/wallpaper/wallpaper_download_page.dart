import 'package:flutter/material.dart';
import '../../components/cache_image.dart';

class WallpaperDownloadPage extends StatelessWidget {
  const WallpaperDownloadPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.bottomCenter,
            end: Alignment.topCenter,
            colors: [
              Color(0xFFFFFFFF), // 粉色
              Color(0xFFFFFFFF), // 浅紫色
              Color(0xFFFFFFFF), // 浅蓝色
            ],
          ),
        ),
        child: Stack(
          children: [
            // 背景星星装饰
            _buildStarBackground(),
            
            // 主要内容
            SafeArea(
              child: Column(
                children: [
                  // 标题栏
                  // _buildHeader(context),
                  
                  // 壁纸网格
                  Expanded(
                    child: _buildWallpaperGrid(context),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStarBackground() {
    return Positioned.fill(
      child: CustomPaint(
        painter: <PERSON><PERSON>ain<PERSON>(),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          const Expanded(
            child: Text(
              '游戏壁纸',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(width: 48), // 平衡布局
        ],
      ),
    );
  }

  Widget _buildWallpaperGrid(BuildContext context) {
    final wallpapers = [
      {
        'name': '小莉莉莉',
        'image': 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        'character': '金发蓝眼少女，蓝色骑士装',
      },
      {
        'name': '剑剑剑剑',
        'image': 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        'character': '黑发红眼少女，持剑战士',
      },
      {
        'name': '莉莉莉莉',
        'image': 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        'character': '粉发蓝眼少女，粉色连衣裙',
      },
      {
        'name': '雷莉挂芙',
        'image': 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        'character': '蓝发少女，魔法师装',
      },
    ];

    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 0.75,
        ),
        itemCount: wallpapers.length,
        itemBuilder: (context, index) {
          return _buildWallpaperCard(wallpapers[index], context);
        },
      ),
    );
  }

  Widget _buildWallpaperCard(Map<String, String> wallpaper, BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // 角色图片区域
          Expanded(
            flex: 4,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
                color: Colors.grey[200],
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
                child: Stack(
                  children: [
                    // 使用CachedImage组件
                    CachedImage(
                      imageUrl: wallpaper['image']!,
                      placeholder: Container(
                        width: double.infinity,
                        height: double.infinity,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Colors.blue[100]!,
                              Colors.purple[100]!,
                            ],
                          ),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.image,
                            size: 50,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                      errorWidget: Container(
                        width: double.infinity,
                        height: double.infinity,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Colors.grey[300]!,
                              Colors.grey[400]!,
                            ],
                          ),
                        ),
                        child: const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.error_outline,
                                size: 50,
                                color: Colors.grey,
                              ),
                              SizedBox(height: 8),
                              Text(
                                '图片加载失败',
                                style: TextStyle(
                                  color: Colors.grey,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    
                    // 角色描述（模拟）
                    Positioned(
                      bottom: 8,
                      left: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.6),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          wallpaper['character']!,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // 底部信息区域
          Container(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    wallpaper['name']!,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () {
                    _downloadWallpaper(context, wallpaper['name']!);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                  ),
                  child: const Text(
                    '下载',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _downloadWallpaper(BuildContext context, String wallpaperName) {
    // 显示下载进度
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('下载中...'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text('正在下载 $wallpaperName'),
            ],
          ),
        );
      },
    );

    // 模拟下载过程
    Future.delayed(const Duration(seconds: 2), () {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('$wallpaperName 下载完成！'),
          backgroundColor: Colors.green,
        ),
      );
    });
  }
}

// 背景星星绘制器
class StarPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.3)
      ..style = PaintingStyle.fill;

    // 绘制随机星星
    for (int i = 0; i < 20; i++) {
      final x = (i * 37) % size.width;
      final y = (i * 73) % size.height;
      final radius = 1.0 + (i % 3) * 0.5;
      
      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
} 