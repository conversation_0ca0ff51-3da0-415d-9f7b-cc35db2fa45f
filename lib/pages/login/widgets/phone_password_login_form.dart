import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../../providers/login_provider.dart';
import 'wechat_mini_game_sheet.dart';
import 'user_agreement_dialog.dart';
import '../../privacy/privacy_manager.dart';
import '../../../webview/webview_dialog.dart';

class PhonePasswordLoginForm extends StatefulWidget {
  final VoidCallback? onSwitchToPhoneCode;
  final VoidCallback? onSwitchToAccount;
  final VoidCallback? onLoginSuccess;

  const PhonePasswordLoginForm({
    super.key,
    this.onSwitchToPhoneCode,
    this.onSwitchToAccount,
    this.onLoginSuccess,
  });

  @override
  State<PhonePasswordLoginForm> createState() => _PhonePasswordLoginFormState();
}

class _PhonePasswordLoginFormState extends State<PhonePasswordLoginForm> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _phoneController = TextEditingController(text: '***********');
  final TextEditingController _passwordController = TextEditingController(text: 'frq0000');
  bool _obscurePassword = true;

  @override
  Widget build(BuildContext context) {
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    final loginProvider = Provider.of<LoginStateProvider>(context);

    return Stack(
      children: [
        SingleChildScrollView(
          padding: const EdgeInsets.only(bottom: 180),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.fromLTRB(30, 20, 30, 0),
                alignment: Alignment.centerLeft,
                child: const Text(
                  '手机号密码登录',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF333333),
                  ),
                ),
              ),

              Container(
                padding: const EdgeInsets.fromLTRB(30, 20, 30, 0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      // 手机号输入框（样式与账号一致，仅限制为数字与长度）
                      Container(
                        height: 50,
                        decoration: BoxDecoration(
                          color: const Color(0xFFF5F5F5),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 15),
                              child: const Text(
                                '+86',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Color(0xFF333333),
                                ),
                              ),
                            ),
                            Container(
                              width: 1,
                              height: 20,
                              color: const Color(0xFFE0E0E0),
                            ),
                            Expanded(
                              child: TextFormField(
                                controller: _phoneController,
                                keyboardType: TextInputType.phone,
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly,
                                  LengthLimitingTextInputFormatter(11),
                                ],
                                decoration: const InputDecoration(
                                  hintText: '请输入手机号',
                                  hintStyle: TextStyle(
                                    fontSize: 16,
                                    color: Color(0xFFCCCCCC),
                                  ),
                                  border: InputBorder.none,
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: 15,
                                    vertical: 15,
                                  ),
                                ),
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: Color(0xFF333333),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return '请输入手机号';
                                  }
                                  if (value.length != 11) {
                                    return '请输入11位手机号';
                                  }
                                  if (!RegExp(r'^1[3-9]\d{9}$').hasMatch(value)) {
                                    return '请输入有效的手机号';
                                  }
                                  return null;
                                },
                                onChanged: (value) {
                                  setState(() {});
                                },
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 16),

                      // 密码输入框
                      Container(
                        height: 50,
                        decoration: BoxDecoration(
                          color: const Color(0xFFF5F5F5),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 15),
                              child: const Icon(
                                Icons.lock_outline,
                                color: Color(0xFF666666),
                                size: 20,
                              ),
                            ),
                            Expanded(
                              child: TextFormField(
                                controller: _passwordController,
                                obscureText: _obscurePassword,
                                decoration: const InputDecoration(
                                  hintText: '请输入密码',
                                  hintStyle: TextStyle(
                                    fontSize: 16,
                                    color: Color(0xFFCCCCCC),
                                  ),
                                  border: InputBorder.none,
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: 15,
                                    vertical: 15,
                                  ),
                                ),
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: Color(0xFF333333),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return '请输入密码';
                                  }
                                  if (value.length < 6) {
                                    return '密码至少6位';
                                  }
                                  return null;
                                },
                                onChanged: (value) {
                                  setState(() {});
                                },
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 15),
                              child: GestureDetector(
                                onTap: () {
                                  setState(() {
                                    _obscurePassword = !_obscurePassword;
                                  });
                                },
                                child: Icon(
                                  _obscurePassword
                                      ? Icons.visibility_off_outlined
                                      : Icons.visibility_outlined,
                                  color: const Color(0xFF666666),
                                  size: 20,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 24),

                      // 登录按钮
                      Consumer<LoginStateProvider>(
                        builder: (context, loginProvider, child) {
                          final bool hasPhoneNumber = _phoneController.text.trim().isNotEmpty;
                          final bool hasPassword = _passwordController.text.trim().isNotEmpty;
                          final bool isButtonEnabled = hasPhoneNumber && hasPassword;
                          return Container(
                            width: double.infinity,
                            height: 50,
                            decoration: BoxDecoration(
                              color: isButtonEnabled ? const Color(0xFF4571FB) : const Color(0x994571FB),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: ElevatedButton(
                              onPressed: isButtonEnabled && !loginProvider.isLoading ? _handleLogin : null,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.transparent,
                                foregroundColor: Colors.white,
                                shadowColor: Colors.transparent,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                elevation: 0,
                              ),
                              child: loginProvider.isLoading
                                  ? const SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                      ),
                                    )
                                  : const Text(
                                      '登录',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                        color: Colors.white,
                                      ),
                                    ),
                            ),
                          );
                        },
                      ),

                      const SizedBox(height: 16),

                      // 切换到手机账号验证码登录
                      Align(
                        alignment: Alignment.centerLeft,
                        child: GestureDetector(
                          onTap: widget.onSwitchToPhoneCode,
                          child: const Text(
                            '手机账号验证码登录',
                            style: TextStyle(
                              fontSize: 14,
                              color: Color(0xFF606266),
                            ),
                          ),
                        ),
                      ),

                    ],
                  ),
                ),
              ),
            ],
          ),
        ),

        // 底部固定区域
        Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          child: Container(
            padding: EdgeInsets.only(
              left: 30,
              right: 30,
              top: 20,
              bottom: bottomPadding > 0 ? bottomPadding + 10 : 30,
            ),
            decoration: const BoxDecoration(
              color: Colors.white,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Consumer<LoginStateProvider>(
                  builder: (context, loginProvider, child) {
                    // 根据authAppList是否为空来决定显示的内容
                    final hasWeChatLogin = loginProvider.authAppList.isNotEmpty;
                    
                    if (hasWeChatLogin) {
                      // 有微信登录应用，显示微信登录和账号登录
                      return Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          // 微信登录
                          GestureDetector(
                            onTap: () async {
                              // 检查是否同意用户协议
                              if (!loginProvider.agreementAccepted) {
                                // 显示用户协议弹窗
                                final agreed = await UserAgreementDialog.show(context);
                                if (agreed == true) {
                                  // 用户点击了"同意并继续"，自动勾选协议
                                  setState(() {
                                    loginProvider.setAgreementAccepted(true);
                                  });
                                } else {
                                  // 用户点击了"不同意"或关闭弹窗，不执行微信登录
                                  return;
                                }
                              }
                              
                              await WechatMiniGameSheet.show(context, authAppList: loginProvider.authAppList);
                            },
                            child: Column(
                              children: [
                                Container(
                                  width: 60,
                                  height: 60,
                                  decoration: const BoxDecoration(
                                    shape: BoxShape.circle,
                                  ),
                                  child: ClipOval(
                                    child: Image.asset(
                                      'assets/images/login_wechat.png',
                                      width: 60,
                                      height: 60,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 8),
                                const Text(
                                  '微信登录',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Color(0xFF666666),
                                  ),
                                ),
                              ],
                            ),
                          ),
      
                          // 账号密码登录
                          GestureDetector(
                            onTap: widget.onSwitchToAccount,
                            child: Column(
                              children: [
                                Container(
                                  width: 60,
                                  height: 60,
                                  decoration: const BoxDecoration(
                                    shape: BoxShape.circle,
                                  ),
                                  child: ClipOval(
                                    child: Image.asset(
                                      'assets/images/login_account.png',
                                      width: 60,
                                      height: 60,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 8),
                                const Text(
                                  '账号密码登录',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Color(0xFF666666),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      );
                    } else {
                      // 没有微信登录应用，只显示账号登录（居中显示）
                      return Center(
                        child: GestureDetector(
                          onTap: widget.onSwitchToAccount,
                          child: Column(
                            children: [
                              Container(
                                width: 60,
                                height: 60,
                                decoration: const BoxDecoration(
                                  shape: BoxShape.circle,
                                ),
                                child: ClipOval(
                                  child: Image.asset(
                                    'assets/images/login_account.png',
                                    width: 60,
                                    height: 60,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                '账号密码登录',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Color(0xFF666666),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }
                  },
                ),

                const SizedBox(height: 24),

                // 用户协议
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          loginProvider.setAgreementAccepted(!loginProvider.agreementAccepted);
                        });
                      },
                      child: Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: loginProvider.agreementAccepted
                                ? const Color(0xFF6B73FF)
                                : const Color(0xFFCCCCCC),
                            width: 1,
                          ),
                          color: loginProvider.agreementAccepted
                              ? const Color(0xFF6B73FF)
                              : Colors.transparent,
                        ),
                        child: loginProvider.agreementAccepted
                            ? const Icon(
                                Icons.check,
                                size: 12,
                                color: Colors.white,
                              )
                            : null,
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      '已阅读并同意',
                      style: TextStyle(
                        fontSize: 12,
                        color: Color(0xFF999999),
                      ),
                    ),
                    GestureDetector(
                      onTap: () async {
                        final userUrl = await PrivacyManager.userAgreementUrl;
                        _openWebView(
                          context,
                          '用户协议',
                          userUrl.isNotEmpty
                              ? '${userUrl}&isAgree=true&env=dl'
                              : 'https://example.com/user-agreement',
                        );
                      },
                      child: const Text(
                        '《用户协议》',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF6B73FF),
                        ),
                      ),
                    ),
                    const Text(
                      '及',
                      style: TextStyle(
                        fontSize: 12,
                        color: Color(0xFF999999),
                      ),
                    ),
                    GestureDetector(
                      onTap: () async {
                        final privacyUrl = await PrivacyManager.privacyPolicyUrl;
                        _openWebView(
                          context,
                          '隐私政策',
                          privacyUrl.isNotEmpty
                              ? '${privacyUrl}&isAgree=true&env=dl'
                              : 'https://example.com/privacy-policy',
                        );
                      },
                      child: const Text(
                        '《隐私政策》',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF6B73FF),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;
    
    // 检查是否同意用户协议
    final loginProvider = Provider.of<LoginStateProvider>(context, listen: false);
    if (!loginProvider.agreementAccepted) {
      // 显示用户协议弹窗
      final agreed = await UserAgreementDialog.show(context);
      if (agreed == true) {
        // 用户点击了"同意并继续"，自动勾选协议
        setState(() {
          loginProvider.setAgreementAccepted(true);
        });
      } else {
        // 用户点击了"不同意"或关闭弹窗，不执行登录
        return;
      }
    }

    final loginStateProvider =
        Provider.of<LoginStateProvider>(context, listen: false);
    final success = await loginStateProvider.performMobilePasswordLogin(
      context: context,
      mobile: _phoneController.text.trim(),
      password: _passwordController.text,
    );

    if (success) {
      widget.onLoginSuccess?.call();
    }
  }

  /// 打开WebView显示协议内容
  void _openWebView(BuildContext context, String title, String url) {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.transparent,
      builder: (context) {
        return WebViewDialog(url: url, title: title, showToolBar: false);
      },
    );
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
}


