import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/user_provider.dart';
import '../../components/cache_image.dart';
import '../settings/SettingsPage.dart';
import '../settings/AccountManagementPage.dart';
import '../settings/AddressManagementPage.dart';
import '../../webview/webview_dialog.dart';

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        final user = userProvider.currentUser;
        final isLoggedIn = userProvider.isLoggedIn;

        return Scaffold(
          backgroundColor: const Color.fromRGBO(246, 247, 249, 1),
          body: Stack(
            children: [
              // 固定的游戏背景图 - 在最底层，不会移动
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 300,
                  decoration: const BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage('assets/images/main_bg.png'),
                      fit: BoxFit.cover,
                      alignment: Alignment.topCenter,
                    ),
                  ),
                ),
              ),

              // Transform.translate(
              //   offset: const Offset(0, 100),
              //   child:
              // ),
              // 可滚动的内容 - 在顶层，可以向上滑动
              SingleChildScrollView(
                child: Column(
                  children: [
                    Row(
                      children: [
                        // 中间空白，自动撑开
                        const Spacer(),
                        // 右侧图标，带点击事件
                        Padding(
                          padding: const EdgeInsets.only(right: 30, top: 40),
                          child: GestureDetector(
                            onTap: () {
                              Navigator.push(context, MaterialPageRoute(builder: (context) => const SettingsPage()));
                            },
                            child: Image.asset('assets/images/setting.png', width: 30, height: 30, fit: BoxFit.contain),
                          ),
                        ),
                      ],
                    ),

                    // 顶部留白空间，显示背景图和为头像预留空间
                    const SizedBox(height: 0),

                    // 用户信息区域
                    Transform.translate(
                      offset: const Offset(0, 75), // 向上移动40px，使容器顶部对齐头像中间
                      child: Stack(
                        children: [
                          // 白色用户信息容器
                          Container(
                            margin: const EdgeInsets.symmetric(horizontal: 0),
                            padding: const EdgeInsets.only(left: 30, right: 16, top: 50, bottom: 36),
                            decoration: BoxDecoration(
                              color: const Color.fromRGBO(246, 247, 249, 1),
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(color: Colors.black.withValues(alpha: 0.1), blurRadius: 10, offset: const Offset(0, 5)),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  isLoggedIn && user?.userDetail.alias != null ? '${user!.userDetail.alias}' : '斗罗宇宙公民',
                                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.black87),
                                ),
                                const SizedBox(height: 8),
                                Row(
                                  children: [
                                    const Text('UID: ', style: TextStyle(fontSize: 14, color: Colors.grey)),
                                    Text(
                                      isLoggedIn && user?.muid != null ? user!.muid! : '未登录',
                                      style: const TextStyle(fontSize: 14, color: Colors.grey),
                                    ),
                                    const SizedBox(width: 20),
                                    const Text('属地: ', style: TextStyle(fontSize: 14, color: Colors.grey)),
                                    Text(
                                      isLoggedIn && user?.userDetail.area != null ? user!.userDetail.area! : '未知',
                                      style: const TextStyle(fontSize: 14, color: Colors.grey),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // 用户头像（重叠在白色容器上）
                    Transform.translate(
                      offset: const Offset(-140, -105),
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(shape: BoxShape.circle, border: Border.all(color: Colors.white, width: 4)),
                        child: ClipOval(
                          child:
                              isLoggedIn && user?.userDetail.avatar != null && user!.userDetail.avatar!.isNotEmpty
                                  ? CachedImage(
                                    imageUrl: user.userDetail.avatar!,
                                    width: 80,
                                    height: 80,
                                    fit: BoxFit.cover,
                                    errorWidget: Image.asset('assets/images/avatar.png', fit: BoxFit.cover),
                                  )
                                  : Image.asset('assets/images/avatar.png', fit: BoxFit.cover),
                        ),
                      ),
                    ),

                    // const SizedBox(height: 20),
                    Transform.translate(
                      offset: const Offset(0, -25), // 向下移动20px
                      child: Container(
                        color: const Color.fromRGBO(246, 247, 249, 1),
                        child: Column(
                          children: [
                            const SizedBox(height: 0),
                            // 我的游戏圈子
                            Container(
                              margin: const EdgeInsets.symmetric(horizontal: 16),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey.withValues(alpha: 0.1),
                                    spreadRadius: 1,
                                    blurRadius: 5,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SizedBox(height: 16),
                                  Padding(
                                    padding: const EdgeInsets.only(left: 15),
                                    child: const Text(
                                      '我的游戏圈子',
                                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.black87),
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(left: 15),
                                    child: SizedBox(
                                      height: 120,
                                      child: ListView.builder(
                                        scrollDirection: Axis.horizontal,
                                        itemCount: 4,
                                        itemBuilder: (context, index) {
                                          return _buildGameItem();
                                        },
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            const SizedBox(height: 20),

                            // 菜单项
                            Container(
                              margin: const EdgeInsets.symmetric(horizontal: 16),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey.withValues(alpha: 0.1),
                                    spreadRadius: 1,
                                    blurRadius: 5,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                children: [
                                  _buildMenuItem('assets/images/account_manage_icon.png', '账号管理', () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(builder: (context) => const AccountManagementPage()),
                                    );
                                  }),
                                  // _buildDivider(),
                                  _buildMenuItem('assets/images/phone_icon.png', '绑定登录手机', () {}),
                                  // _buildDivider(),
                                  _buildMenuItem('assets/images/password_icon.png', '绑定密保手机', () {}),
                                  // _buildDivider(),
                                  _buildMenuItem('assets/images/address_manage_icon.png', '地址管理', () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => const AddressManagementPage(),
                                      ),
                                    );
                                  }),
                                  // _buildDivider(),
                                  _buildMenuItem('assets/images/advice_icon.png', '建议与反馈', () {
                                    Navigator.of(context).push(
                                      PageRouteBuilder(
                                        opaque: false,
                                        barrierDismissible: true,
                                        barrierColor: Colors.transparent,
                                        pageBuilder: (context, animation, secondaryAnimation) {
                                          return WebViewDialog(
                                            url: "https://user.37.com.cn/sdkv1/service/home",
                                            title: '官网',
                                            showToolBar: true,
                                          );
                                        },
                                      ),
                                    );
                                  }),
                                ],
                              ),
                            ),
                            const SizedBox(height: 25),
                          ],
                        ),
                      ),
                    ),
                    // 白色背景区域开始 - 确保后续内容有白色背景
                    // const SizedBox(height: 20),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 游戏项构建方法
  Widget _buildGameItem() {
    return Container(
      margin: const EdgeInsets.only(right: 12),
      child: Column(
        children: [
          const SizedBox(height: 15), // 添加10px的顶部间距
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              image: const DecorationImage(image: AssetImage('assets/images/game_icon.png'), fit: BoxFit.cover),
            ),
            child: Stack(
              children: [
                Positioned(
                  top: 8,
                  left: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                    decoration: BoxDecoration(color: Colors.blue, borderRadius: BorderRadius.circular(4)),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          const Text('斗罗大陆：猎魂世界', style: TextStyle(fontSize: 12, color: Colors.black87), textAlign: TextAlign.center),
        ],
      ),
    );
  }

  // 菜单项构建方法
  Widget _buildMenuItem(String imageString, String title, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Row(
          children: [
            Image.asset(imageString, width: 24, height: 24, fit: BoxFit.contain),
            const SizedBox(width: 12),
            Expanded(child: Text(title, style: const TextStyle(fontSize: 16, color: Colors.black87))),
            Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400]),
          ],
        ),
      ),
    );
  }

  // 分割线构建方法
  Widget _buildDivider() {
    return Divider(height: 1, color: Colors.grey[200], indent: 52);
  }
}
