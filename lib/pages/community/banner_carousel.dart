import 'package:dlyz_flutter/components/cache_image.dart';
import 'package:flutter/material.dart';
import 'dart:async';

/// 轮播图组件
/// 
/// 使用 PageView 实现自动轮播效果
/// 支持自定义轮播数据、轮播间隔时间、指示器等
class BannerCarousel extends StatefulWidget {
  final List<BannerItem> banners;
  final double height;
  final Duration autoPlayInterval;
  final bool showIndicator;
  final EdgeInsets margin;
  final BorderRadius borderRadius;

  const BannerCarousel({
    super.key,
    required this.banners,
    this.height = 120,
    this.autoPlayInterval = const Duration(seconds: 3),
    this.showIndicator = true,
    this.margin = const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    this.borderRadius = const BorderRadius.all(Radius.circular(12)),
  });

  @override
  State<BannerCarousel> createState() => _BannerCarouselState();
}

class _BannerCarouselState extends State<BannerCarousel> {
  late PageController _pageController;
  late Timer _timer;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _startAutoPlay();
  }

  @override
  void dispose() {
    _timer.cancel();
    _pageController.dispose();
    super.dispose();
  }

  void _startAutoPlay() {
    if (widget.banners.length > 1) {
      _timer = Timer.periodic(widget.autoPlayInterval, (timer) {
        if (_currentIndex < widget.banners.length - 1) {
          _currentIndex++;
        } else {
          _currentIndex = 0;
        }
        _pageController.animateToPage(
          _currentIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.banners.isEmpty) {
      return Container(
        margin: widget.margin,
        height: widget.height,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: widget.borderRadius,
        ),
        child: const Center(
          child: Text(
            '暂无轮播图',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ),
      );
    }

    return Container(
      margin: widget.margin,
      height: widget.height,
      child: Stack(
        children: [
          // 轮播图内容
          PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            itemCount: widget.banners.length,
            itemBuilder: (context, index) {
              final banner = widget.banners[index];
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 2),
                decoration: BoxDecoration(
                  borderRadius: widget.borderRadius,
                  color: banner.backgroundColor ?? Colors.blue[100],
                ),
                child: ClipRRect(
                  borderRadius: widget.borderRadius,
                  child: Stack(
                    children: [
                      // 背景图片或颜色
                      if (banner.imageUrl != null)
                        CachedImage(imageUrl: banner.imageUrl!),
                      // 内容
                      Container(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            if (banner.title != null)
                              Text(
                                banner.title!,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            if (banner.subtitle != null) ...[
                              const SizedBox(height: 8),
                              Text(
                                banner.subtitle!,
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.white70,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
          // 指示器
          if (widget.showIndicator && widget.banners.length > 1)
            Positioned(
              bottom: 12,
              right: 16,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: widget.banners.asMap().entries.map((entry) {
                  int index = entry.key;
                  return Container(
                    width: 8,
                    height: 8,
                    margin: const EdgeInsets.symmetric(horizontal: 2),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _currentIndex == index
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.5),
                    ),
                  );
                }).toList(),
              ),
            ),
        ],
      ),
    );
  }
}

/// 轮播图数据模型
class BannerItem {
  final String? imageUrl;
  final String? title;
  final String? subtitle;
  final Color? backgroundColor;
  final VoidCallback? onTap;

  const BannerItem({
    this.imageUrl,
    this.title,
    this.subtitle,
    this.backgroundColor,
    this.onTap,
  });
} 