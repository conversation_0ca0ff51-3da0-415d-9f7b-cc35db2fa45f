import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:fluttertoast/fluttertoast.dart';

import '../../common/dl_color.dart';
import '../../components/refresh_header.dart';
import '../../model/forum_post_list.dart';
import '../../net/config/http_base_config.dart';
import '../../net/api/forum_service.dart';
import '../../components/cache_image.dart';
import 'community_detail_page.dart';
import 'user_profile_page.dart';
import 'topic_page.dart';
import 'dart:convert';

/// 帖子搜索页（UI 实现，数据接入可后续扩展）
class PostSearchPage extends StatefulWidget {
  final String initialKeyword;

  const PostSearchPage({super.key, this.initialKeyword = ''});

  @override
  State<PostSearchPage> createState() => _PostSearchPageState();
}

class _PostSearchPageState extends State<PostSearchPage> with SingleTickerProviderStateMixin {
  late final TextEditingController _searchController;
  late final TabController _tabController;
  List<ForumPost> _searchResults = [];
  int _totalCount = 0;
  bool _isSearching = false;
  bool _hasSearched = false;
  // 预缓存的指示器图片，参考 CommunityPage 实现
  final AssetImage _tabIndicatorAsset = const AssetImage('assets/images/tab_indicator.png');
  // 分页与滚动
  final ScrollController _scrollController = ScrollController();
  int _currentPage = 1;
  int _totalPage = 1;
  bool _hasMore = true;
  bool _isLoadingMore = false;
  String _lastKeyword = '';

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(text: widget.initialKeyword);
    _tabController = TabController(length: 1, vsync: this);
    _scrollController.addListener(_handleScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 预加载Tab指示器图片，保证首次展示时即可绘制
    precacheImage(_tabIndicatorAsset, context);
  }

  void _onSearch() {
    FocusScope.of(context).unfocus();
    final keyword = _searchController.text.trim();
    if (keyword.isEmpty) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
        _hasSearched = false;
      });
      Fluttertoast.showToast(msg: '请输入需要搜索的内容');
      return;
    }
    
    setState(() {
      _isSearching = true;
      _hasSearched = false;
      _searchResults.clear();
      _currentPage = 1;
      _hasMore = true;
      _lastKeyword = keyword;
    });

    _searchPosts(keyword, page: 1, append: false);
  }

  void _handleScroll() {
    if (!_hasSearched || !_hasMore || _isLoadingMore || _isSearching) return;
    if (!_scrollController.hasClients) return;
    final position = _scrollController.position;
    if (position.extentAfter < 300) {
      setState(() {
        _isLoadingMore = true;
      });
      _searchPosts(_lastKeyword, page: _currentPage + 1, append: true);
    }
  }

  Future<void> _searchPosts(String keyword, {int page = 1, bool append = false}) async {
    try {
      final forumService = ForumService();
      final resp = await forumService.getPostList(
        baseUrl: HttpBaseConfig.forumBaseUrl,
        page: page,
        pageSize: 20,
        scope: 2,
        filterSearch: keyword,
      );
      if (!mounted) return;
      setState(() {
        _isSearching = false;
        _hasSearched = true;
        if (resp.code == 0 && resp.data != null) {
          final data = resp.data!;
          if (append) {
            _searchResults.addAll(data.pageData);
          } else {
            _searchResults = data.pageData;
          }
          _currentPage = data.currentPage;
          _totalPage = data.totalPage;
          _totalCount = data.totalCount;
          _hasMore = _currentPage < _totalPage && data.pageData.isNotEmpty;
        } else {
          if (!append) {
            _searchResults = [];
          }
          _totalCount = 0;
          _hasMore = false;
        }
        _isLoadingMore = false;
      });
      if (resp.code != 0) {
        Fluttertoast.showToast(msg: resp.message ?? '搜索失败');
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isSearching = false;
        _hasSearched = true;
        if (!append) {
          _searchResults = [];
        }
        _isLoadingMore = false;
      });
      Fluttertoast.showToast(msg: '网络错误: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            _buildSearchBar(context),
            _buildTabs(),
            const Divider(height: 1, thickness: 1, color: DLColor.divider),
            Expanded(child: _buildSearchContent()),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(12, 8, 12, 8),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back_ios_new, size: 18, color: Colors.black87),
            onPressed: () => Navigator.of(context).pop(),
          ),
          Expanded(
            child: Container(
              height: 36,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                color: const Color(0xFFF5F6F8),
                borderRadius: BorderRadius.circular(18),
              ),
              child: Row(
                children: [
                  const Icon(Icons.search, size: 18, color: Colors.grey),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: _searchController,
                      textInputAction: TextInputAction.search,
                      decoration: const InputDecoration(
                        hintText: '请输入想要搜索的内容',
                        isDense: true,
                        border: InputBorder.none,
                      ),
                      onSubmitted: (_) => _onSearch(),
                    ),
                  ),
                  if (_searchController.text.isNotEmpty)
                    GestureDetector(
                      onTap: () {
                        _searchController.clear();
                        setState(() {});
                      },
                      child: const Icon(Icons.close, size: 18, color: Colors.grey),
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 8),
          GestureDetector(
            onTap: _onSearch,
            child: Container(
              height: 34,
              padding: const EdgeInsets.symmetric(horizontal: 14),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: const Color(0xFF4571FB),
                borderRadius: BorderRadius.circular(17),
              ),
              child: const Text('搜索', style: TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.w600)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabs() {
    return Transform.translate(
      offset: const Offset(-22, 0),
      child: SizedBox(
        height: 40,
        child: TabBar(
          controller: _tabController,
          isScrollable: true,
          labelPadding: const EdgeInsets.only(right: 16),
          padding: const EdgeInsets.only(left: 0),
          labelColor: Colors.black,
          unselectedLabelColor: Colors.black54,
          labelStyle: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
          unselectedLabelStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w400),
          // 使用与 CommunityPage 相同的自定义图片指示器
          indicator: ImageTabIndicator(
            image: _tabIndicatorAsset,
            imageSize: const Size(24, 8),
            bottomPadding: 4,
          ),
          dividerColor: Colors.transparent,
          splashFactory: NoSplash.splashFactory,
          overlayColor: MaterialStateProperty.all(Colors.transparent),
          tabs: const [
            Tab(text: '帖子'),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchContent() {
    if (_isSearching) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }
    
    if (!_hasSearched) {
      return _buildEmptyState();
    }
    
    if (_searchResults.isEmpty) {
      return _buildNoResultsState();
    }
    
    return _buildSearchResults();
  }
  
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: const Color(0xFFF2F4F7),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(Icons.search, size: 60, color: Colors.grey[400]),
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            '搜索你感兴趣的内容',
            style: TextStyle(
              color: Color(0xFF8C8C8C),
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildNoResultsState() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: const Color(0xFFF2F4F7),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(Icons.search_off, size: 60, color: Colors.grey[400]),
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            '暂无相关搜索结果',
            style: TextStyle(
              color: Color(0xFF8C8C8C),
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '换个关键词试试吧',
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSearchResults() {

    EasyRefreshController controller = EasyRefreshController(
        controlFinishRefresh: true,
        controlFinishLoad: true
    );
    return EasyRefresh(
        controller: controller,
        header: const CustomerHeader(),
        onRefresh: () async {
          if (_lastKeyword.isEmpty) return;
          setState(() {
            _currentPage = 1;
            _hasMore = true;
            _isSearching = true;
          });
          await _searchPosts(_lastKeyword, page: 1, append: false);
          controller.finishRefresh();
          controller.resetFooter();
        },
        child: ListView.builder(
          controller: _scrollController,
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          itemCount: (_searchResults.isEmpty ? 0 : 1) + _searchResults.length + 1,
          itemBuilder: (context, index) {
            // 顶部统计行
            if (_searchResults.isNotEmpty && index == 0) {
              // 与上方 Tab 的左边对齐：Tab 有一个 -22 的左移；列表有 16 的左内边距
              // 因此这里整体左移 38 像素（16 + 22）
              return Transform.translate(
                offset: const Offset(14, 0),
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Text(
                    '搜索结果（$_totalCount）',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: DLColor.textFifth,
                    ),
                  ),
                ),
              );
            }

            final listIndex = _searchResults.isNotEmpty ? index - 1 : index;

            if (listIndex == _searchResults.length) {
              if (_isLoadingMore) {
                return const Padding(
                  padding: EdgeInsets.symmetric(vertical: 12),
                  child: Center(
                    child: SizedBox(
                      height: 24,
                      width: 24,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  ),
                );
              }
              if (!_hasMore) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  child: Center(
                    child: Text(
                      '没有更多了',
                      style: TextStyle(color: Colors.grey[500], fontSize: 12),
                    ),
                  ),
                );
              }
              return const SizedBox.shrink();
            }
            final post = _searchResults[listIndex];
            return _buildPostItem(post);
          },
        )
    );
  }
  
  String _generateRandomContent(int index) {
    final contents = [
      '分享一些游戏心得和攻略技巧，希望对大家有帮助。游戏中遇到困难时，记得多和队友沟通协作。',
      '最近更新的版本有很多新功能，体验还不错。特别是新增的副本玩法，难度适中，奖励丰富。',
      '讨论一下新英雄的技能搭配和出装思路。经过实战测试，推荐优先升级技能1和技能3。',
      '组队活动开始了，欢迎大家一起参与！活动期间完成任务可以获得限定皮肤和珍稀道具。',
      '分享截图展示游戏精彩瞬间。这次团战配合得特别好，成功完成了翻盘。',
    ];
    return contents[index % contents.length];
  }
  
  List<dynamic> _generateMockImages(int index) {
    return [
      {
        'url': 'https://picsum.photos/400/300?random=${index + 200}',
        'thumbUrl': 'https://picsum.photos/200/150?random=${index + 200}',
        'attachment': 'https://picsum.photos/400/300?random=${index + 200}',
      },
      if (index % 2 == 0)
        {
          'url': 'https://picsum.photos/400/300?random=${index + 300}',
          'thumbUrl': 'https://picsum.photos/200/150?random=${index + 300}',
          'attachment': 'https://picsum.photos/400/300?random=${index + 300}',
        },
    ];
  }
  
  Map<String, dynamic> _generateMockImageIndexes(int index) {
    return {
      '101': {
        'body': _generateMockImages(index),
      },
    };
  }

  /// 构建帖子列表项（复制自 ForumPostListPage）
  Widget _buildPostItem(ForumPost post) {
    return Column(
      children: [
        // 灰色分割线
        Container(
          height: 8,
          color: Colors.grey[200],
          width: double.infinity,
        ),
        GestureDetector(
          onTap: () => _navigateToPostDetail(post),
          child: Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            width: double.infinity,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 顶部用户信息区域
                _buildUserHeader(post),
                
                const SizedBox(height: 12),
                
                // 帖子标题
                if (post.title.isNotEmpty)
                  Text(
                    post.title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                      height: 1.3,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                
                const SizedBox(height: 8),
                
                // 帖子内容预览
                _buildContentPreview(post),
                
                const SizedBox(height: 12),
                
                // 话题标签
                _buildTopicTags(post),
                
                // 底部信息栏
                _buildBottomInfo(post),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUserHeader(ForumPost post) {
    return Row(
      children: [
        // 用户头像
        GestureDetector(
          onTap: () => _navigateToUserProfile(post.user),
          child: CircleAvatar(
            radius: 20,
            backgroundColor: Colors.grey[200],
            backgroundImage: post.user.avatar.isNotEmpty 
                ? NetworkImage(post.user.avatar) 
                : null,
            child: post.user.avatar.isEmpty 
                ? Icon(
                    Icons.person,
                    size: 20,
                    color: Colors.grey[600],
                  )
                : null,
          ),
        ),
        
        const SizedBox(width: 12),
        
        // 用户信息
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 用户名
              Text(
                post.user.nickname.isNotEmpty ? post.user.nickname : '匿名用户',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: 2),
              
              // 官方账号标识或其他标签
              if (post.user.badge.isNotEmpty)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.blue[200]!, width: 0.5),
                  ),
                  child: Text(
                    '官方账号',
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.blue[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ),
        ),

        // 关注按钮（与 forum_post_list_page.dart 一致）
        GestureDetector(
          onTap: () => {},
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFF4571FB), width: 1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: const Text(
              '+关注',
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF4571FB),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ],
    );
  }
  
  Widget _buildContentPreview(ForumPost post) {
    final String cleanText = _stripHtmlTags(post.content.text);
    final bool needsTruncation = cleanText.length > 102;
    final String displayText = needsTruncation 
        ? '${cleanText.substring(0, 102)}...' 
        : cleanText;

    // 获取所有图片（包括content.images和图文分离的图片）
    List<dynamic> allImages = _getAllImages(post);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 显示文本内容
        if (displayText.isNotEmpty)
          Text(
            displayText,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
              height: 1.4,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        
        // 显示全文按钮（只在文本被截断时显示）
        if (needsTruncation) ...[
          const SizedBox(height: 8),
          Text(
            '全文',
            style: TextStyle(
              fontSize: 14,
              color: Colors.blue[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
        
        // 显示图片
        if (allImages.isNotEmpty) ...[
          const SizedBox(height: 8),
          _buildContentImages(allImages),
        ],
      ],
    );
  }
  
  /// 获取帖子中所有图片（包括content.images和图文分离的图片）
  List<dynamic> _getAllImages(ForumPost post) {
    List<dynamic> allImages = [];
    
    // 先添加content.images中的图片
    if (post.content.images.isNotEmpty) {
      allImages.addAll(post.content.images);
    }
    
    // 判断是否是图文分离的帖子
    final bool isSeparatedContent = post.from != 2 && post.isMixThread != 1;
    
    if (isSeparatedContent) {
      // 从indexes[101]获取图文分离的图片
      final indexes = post.content.indexes;
      
      if (indexes.containsKey('101')) {
        final imageIndex = indexes['101'];
        
        if (imageIndex is Map<String, dynamic> && imageIndex['body'] is List) {
          final bodyList = imageIndex['body'] as List;
          
          for (var item in bodyList) {
            if (item is Map<String, dynamic>) {
              final imageUrl = item['url'] ?? item['thumbUrl'] ?? item['attachment'];
              if (imageUrl != null && imageUrl.isNotEmpty) {
                allImages.add(item);
              }
            }
          }
        }
      }
    }
    
    return allImages;
  }
  
  Widget _buildContentImages(List<dynamic> images) {
    if (images.isEmpty) return const SizedBox.shrink();
    
    // 最多显示2张图片
    final int displayCount = images.length > 2 ? 2 : images.length;
    
    if (displayCount == 1) {
      // 单张图片
      return _buildSingleImage(images[0], allImages: images);
    } else {
      // 多张图片网格
      return _buildImageGrid(images, displayCount);
    }
  }

  Widget _buildSingleImage(dynamic imageData, {List<dynamic>? allImages}) {
    String? imageUrl;
    
    // 尝试从不同字段获取图片URL
    if (imageData is Map<String, dynamic>) {
      imageUrl = imageData['url'] ?? imageData['thumbUrl'] ?? imageData['attachment'];
    } else if (imageData is String) {
      imageUrl = imageData;
    }
    
    if (imageUrl == null || imageUrl.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey[200],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: CachedImage(
          imageUrl: imageUrl,
          width: double.infinity,
          height: 200,
          fit: BoxFit.cover,
          onTap: () => _showImageGallery(context, allImages ?? [imageData], 0),
        ),
      ),
    );
  }
  
  Widget _buildImageGrid(List<dynamic> images, int displayCount) {
    return SizedBox(
      height: 120,
      child: Row(
        children: List.generate(displayCount, (index) {
          final imageData = images[index];
          String? imageUrl;
          
          if (imageData is Map<String, dynamic>) {
            imageUrl = imageData['url'] ?? imageData['thumbUrl'] ?? imageData['attachment'];
          } else if (imageData is String) {
            imageUrl = imageData;
          }
          
          return Expanded(
            child: GestureDetector(
              onTap: () => _showImageGallery(context, images, index),
              child: Container(
                margin: EdgeInsets.only(right: index < (displayCount - 1) ? 4 : 0),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  color: Colors.grey[200],
                ),
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: imageUrl != null && imageUrl.isNotEmpty
                          ? CachedImage(
                              imageUrl: imageUrl,
                              width: double.infinity,
                              height: double.infinity,
                              fit: BoxFit.cover,
                            )
                          : Container(
                              color: Colors.grey[200],
                              child: Icon(
                                Icons.image,
                                color: Colors.grey[600],
                              ),
                            ),
                    ),
                    
                    // 如果是最后一张图片且还有更多图片，显示"+N"标识
                    if (index == (displayCount - 1) && images.length > displayCount)
                      Container(
                        width: double.infinity,
                        height: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6),
                          color: Colors.black.withValues(alpha: 0.5),
                        ),
                        child: Center(
                          child: Text(
                            '+${images.length - displayCount}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildTopicTags(ForumPost post) {
    // 如果没有话题标签，返回空Widget
    if (post.topics.trim().isEmpty) {
      return const SizedBox.shrink();
    }
    
    List<Map<String, String>> topicList = _parseTopics(post.topics);
    
    if (topicList.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Wrap(
          spacing: 8.0,
          runSpacing: 4.0,
          children: topicList.map((topicData) {
            return GestureDetector(
              onTap: () => _navigateToTopicPage(topicData['id'] ?? topicData['content'] ?? ''),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '#${topicData['content'] ?? topicData['id'] ?? ''}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  List<Map<String, String>> _parseTopics(String topicsStr) {
    List<Map<String, String>> result = [];
    
    try {
      // 尝试解析为JSON
      dynamic parsed = json.decode(topicsStr);
      
      if (parsed is List) {
        // JSON数组格式: [{"id": "1", "content": "游戏"}, {"id": "2", "content": "攻略"}]
        for (var item in parsed) {
          if (item is Map<String, dynamic>) {
            result.add({
              'id': item['id']?.toString() ?? '',
              'content': item['content']?.toString() ?? '',
            });
          }
        }
      } else if (parsed is Map<String, dynamic>) {
        // 单个JSON对象格式: {"id": "1", "content": "游戏"}
        result.add({
          'id': parsed['id']?.toString() ?? '',
          'content': parsed['content']?.toString() ?? '',
        });
      }
    } catch (e) {
      // JSON解析失败，尝试作为简单字符串处理
      List<String> simpleTopics = topicsStr
          .split(RegExp(r'[,，\s]+'))
          .where((topic) => topic.trim().isNotEmpty)
          .map((topic) => topic.trim())
          .toList();
      
      for (String topic in simpleTopics) {
        result.add({
          'id': topic,
          'content': topic,
        });
      }
    }
    
    return result;
  }

  void _navigateToTopicPage(String topicIdStr) {
    final int? topicId = int.tryParse(topicIdStr);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TopicPage(
          topicName: topicIdStr,
          topicId: topicId,
          description: null,
          avatarUrl: null,
          viewCount: 0,
          likeCount: 0,
          threadCount: 0,
        ),
      ),
    );
  }
  
  Widget _buildBottomInfo(ForumPost post) {
    return Row(
      children: [
        // 发布时间
        Text(
          _formatTimeAgo(post.createdAt),
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[500],
          ),
        ),
        
        const Spacer(),
        
        // 浏览量
        Icon(
          Icons.visibility_outlined,
          size: 16,
          color: Colors.grey[500],
        ),
        const SizedBox(width: 4),
        Text(
          _formatCount(post.viewCount),
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[500],
          ),
        ),
      ],
    );
  }
  
  String _formatTimeAgo(String createdAt) {
    try {
      final DateTime postTime = DateTime.parse(createdAt);
      final DateTime now = DateTime.now();
      final Duration diff = now.difference(postTime);
      
      if (diff.inMinutes < 60) {
        return '${diff.inMinutes}分钟前';
      } else if (diff.inHours < 24) {
        return '${diff.inHours}小时前';
      } else {
        // 超过1天直接显示日期时间
        return '${postTime.year}-${postTime.month.toString().padLeft(2, '0')}-${postTime.day.toString().padLeft(2, '0')} ${postTime.hour.toString().padLeft(2, '0')}:${postTime.minute.toString().padLeft(2, '0')}';
      }
    } catch (e) {
      return '刚刚';
    }
  }
  
  String _formatCount(int count) {
    if (count > 10000) {
      return '${(count / 10000).toStringAsFixed(1)}w';
    } else if (count > 1000) {
      return '${(count / 1000).toStringAsFixed(1)}k';
    } else {
      return count.toString();
    }
  }
  
  String _stripHtmlTags(String html) {
    if (html.isEmpty) return '';
    
    // 移除HTML标签
    String result = html.replaceAll(RegExp(r'<[^>]*>', multiLine: true, caseSensitive: false), '');
    
    // 替换HTML实体
    result = result
        .replaceAll('&nbsp;', ' ')
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .replaceAll('&amp;', '&')
        .replaceAll('&quot;', '"')
        .replaceAll('&#39;', "'")
        .replaceAll('&mdash;', '—')
        .replaceAll('&ldquo;', '"')
        .replaceAll('&rdquo;', '"');
    
    // 移除多余的空白字符和换行
    result = result
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
    
    return result;
  }
  
  /// 跳转到帖子详情页
  void _navigateToPostDetail(ForumPost post) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CommunityDetailPage(post: post),
      ),
    );
  }
  
  /// 跳转到用户个人资料页面
  void _navigateToUserProfile(ForumUser user) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UserProfilePage(user: user),
      ),
    );
  }
  
  /// 显示图片画廊（支持左右滑动）
  void _showImageGallery(BuildContext context, List<dynamic> images, int initialIndex) {
    showDialog(
      context: context,
      barrierColor: Colors.black87,
      builder: (BuildContext context) {
        return _ImageGalleryDialog(images: images, initialIndex: initialIndex);
      },
    );
  }
}

/// 一个小箭头形状的指示器，绘制在选中 Tab 的下方居中
class _SmallArrowIndicator extends Decoration {
  const _SmallArrowIndicator();

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _SmallArrowPainter();
  }
}

class _SmallArrowPainter extends BoxPainter {
  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    final Rect rect = offset & configuration.size!;
    final double arrowWidth = 18;
    final double arrowHeight = 6;
    final double centerX = rect.left + rect.width / 2;
    final double bottomY = rect.bottom;

    final Path path = Path()
      ..moveTo(centerX - arrowWidth / 2, bottomY)
      ..lineTo(centerX, bottomY - arrowHeight)
      ..lineTo(centerX + arrowWidth / 2, bottomY)
      ..close();

    final Paint paint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.fill;

    canvas.drawPath(path, paint);
  }
}

/// 图片画廊弹窗组件
class _ImageGalleryDialog extends StatefulWidget {
  final List<dynamic> images;
  final int initialIndex;

  const _ImageGalleryDialog({
    required this.images,
    required this.initialIndex,
  });

  @override
  State<_ImageGalleryDialog> createState() => _ImageGalleryDialogState();
}

class _ImageGalleryDialogState extends State<_ImageGalleryDialog> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  String? _getImageUrl(dynamic imageData) {
    if (imageData is Map<String, dynamic>) {
      return imageData['url'] ?? imageData['thumbUrl'] ?? imageData['attachment'];
    } else if (imageData is String) {
      return imageData;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Navigator.of(context).pop(),
      child: Dialog.fullscreen(
        backgroundColor: Colors.transparent,
        child: Stack(
          children: [
            // 背景可点击区域
            Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.transparent,
            ),
            // 图片轮播
            PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              itemCount: widget.images.length,
              itemBuilder: (context, index) {
                final imageUrl = _getImageUrl(widget.images[index]);
                if (imageUrl == null || imageUrl.isEmpty) {
                  return const Center(
                    child: Icon(
                      Icons.broken_image,
                      size: 64,
                      color: Colors.white54,
                    ),
                  );
                }

                return Center(
                  child: InteractiveViewer(
                    panEnabled: true,
                    scaleEnabled: true,
                    minScale: 0.5,
                    maxScale: 3.0,
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      fit: BoxFit.contain,
                      placeholder: (context, url) => const Center(
                        child: CircularProgressIndicator(
                          color: Colors.white,
                        ),
                      ),
                      errorWidget: (context, url, error) => const Center(
                        child: Icon(
                          Icons.broken_image,
                          size: 64,
                          color: Colors.white54,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
            // 页面指示器
            if (widget.images.length > 1)
              Positioned(
                bottom: 100,
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '${_currentIndex + 1} / ${widget.images.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            // 关闭按钮
            Positioned(
              top: 50,
              right: 20,
              child: GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: const BoxDecoration(
                    color: Colors.black54,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}





/// 使用图片的Tab指示器，图片会绘制在选中Tab的下方并水平居中
class ImageTabIndicator extends Decoration {
  final ImageProvider image;
  final Size imageSize;
  final double bottomPadding;

  const ImageTabIndicator({
    required this.image,
    required this.imageSize,
    this.bottomPadding = 4,
  });

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _ImageTabIndicatorPainter(
      image: image,
      imageSize: imageSize,
      bottomPadding: bottomPadding,
      onChanged: onChanged,
    );
  }
}

class _ImageTabIndicatorPainter extends BoxPainter {
  final ImageProvider image;
  final Size imageSize;
  final double bottomPadding;

  ImageStream? _imageStream;
  ImageInfo? _imageInfo;
  ImageStreamListener? _imageStreamListener;

  _ImageTabIndicatorPainter({
    required this.image,
    required this.imageSize,
    required this.bottomPadding,
    VoidCallback? onChanged,
  }) : super(onChanged);

  void _resolveImage(ImageConfiguration configuration) {
    final ImageStream newStream = image.resolve(configuration);
    if (_imageStream?.key == newStream.key) {
      return;
    }
    if (_imageStream != null && _imageStreamListener != null) {
      _imageStream!.removeListener(_imageStreamListener!);
    }
    _imageStream = newStream;
    _imageStreamListener ??= ImageStreamListener(_handleImage, onError: _handleError);
    _imageStream!.addListener(_imageStreamListener!);
  }

  void _handleImage(ImageInfo imageInfo, bool synchronousCall) {
    _imageInfo = imageInfo;
    onChanged?.call();
  }

  void _handleError(Object exception, StackTrace? stackTrace) {
    // 忽略加载失败，指示器不绘制
  }

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    if (configuration.size == null) return;

    _resolveImage(configuration);

    final Rect rect = offset & configuration.size!;
    final double dx = rect.left + (rect.width - imageSize.width) / 2;
    final double dy = rect.bottom - bottomPadding - imageSize.height;
    final Rect dstRect = Rect.fromLTWH(dx, dy, imageSize.width, imageSize.height);

    final ImageInfo? info = _imageInfo;
    if (info == null) {
      return; // 图片尚未加载完成
    }

    final Size naturalSize = Size(
      info.image.width.toDouble(),
      info.image.height.toDouble(),
    );
    final Rect srcRect = Offset.zero & naturalSize;

    final Paint paint = Paint()..isAntiAlias = true;
    canvas.drawImageRect(info.image, srcRect, dstRect, paint);
  }

  @override
  void dispose() {
    if (_imageStream != null && _imageStreamListener != null) {
      _imageStream!.removeListener(_imageStreamListener!);
    }
    _imageStream = null;
    _imageInfo = null;
    super.dispose();
  }
}