import 'package:flutter/material.dart';
import '../../components/cache_image.dart';
import '../../components/card_list.dart';

class InformationContent extends StatefulWidget {
  const InformationContent({super.key});

  @override
  State<InformationContent> createState() => _InformationContentState();
}

class _InformationContentState extends State<InformationContent> {
  int _selectedCategoryIndex = 0;

  // 资讯列表数据
  final InformationList _informationList1 = InformationList(
    title: "玩家访谈",
    subTitle: "玩家访谈|魂师对决玩家专访第一期",
    tap: "资讯",
    list: [
      InformationItem(
        imageUrl:
            'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        content: '',
      ),
      InformationItem(
        imageUrl:
            'https://imgcs.s98s2.com/image/webSite/article/1752072193000/MMO%E4%BB%A3%E8%A8%80%E4%BA%BA%E6%88%90%E9%BE%99.jpg',
        content: '',
      ),
      InformationItem(
        imageUrl:
            'https://imgcs.s98s2.com/image/webSite/article/1752195478000/%E6%88%90%E5%9B%BE.jpg',
        content: '',
      ),
      InformationItem(
        imageUrl:
            'https://imgcs.s98s2.com/image/webSite/article/1752195478000/%E6%88%90%E5%9B%BE.jpg',
        content: '',
      ),
    ],
  );
  final InformationList _informationList2 = InformationList(
    title: "活动预告",
    subTitle: "",
    tap: "",
    list: [
      InformationItem(
        imageUrl:
            'https://imgcs.s98s2.com/image/webSite/article/1752195478000/%E6%88%90%E5%9B%BE.jpg',
        content: '修罗应援【7.25-7.30】',
      ),
      InformationItem(
        imageUrl:
            'https://imgcs.s98s2.com/image/webSite/article/1752072193000/MMO%E4%BB%A3%E8%A8%80%E4%BA%BA%E6%88%90%E9%BE%99.jpg',
        content: '兽浪狂袭【7.22-7.28】',
      ),
      InformationItem(
        imageUrl:
            'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        content: '斗魂奇石【7.21-7.24】',
      ),
      InformationItem(
        imageUrl:
            'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        content: '双神降临卡池-双神唐三',
      ),
    ],
  );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 资讯列表
                ClipRRect(
                  borderRadius: const BorderRadius.all(Radius.circular(8)),
                  child: CachedImage(height: 100, imageUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg'),
                ),
                SizedBox(height: 6),
                Text(
                    '签到功能上线｜来论坛免费薅羊毛，福利礼包每日派送！',
                ),
                SizedBox(height: 8),
                InformationCardList(informationList: _informationList1),
                SizedBox(height: 6),
                InformationCardList(informationList: _informationList2),
              ],
            ),
        ),
      ),
    );
  }
}
