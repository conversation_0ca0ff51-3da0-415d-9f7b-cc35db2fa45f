import 'package:flutter/material.dart';

/// 空页面组件
/// 
/// 用于显示各个Tab的空白状态页面
/// 后续其他Tab（攻略、游戏资讯、游戏IP内容、游戏壁纸）可以按照这个模板
/// 创建对应的内容组件，例如：
/// - StrategyContent (攻略内容)
/// - GameNewsContent (游戏资讯内容)  
/// - GameIPContent (游戏IP内容)
/// - GameWallpaperContent (游戏壁纸内容)
/// 
/// 然后在 CommunityPage 的 _buildContentArea 方法中替换对应的 EmptyPage
class EmptyPage extends StatelessWidget {
  final String tabName;
  
  const EmptyPage({
    super.key,
    required this.tabName,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '$tabName页面',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '暂无内容',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }
} 