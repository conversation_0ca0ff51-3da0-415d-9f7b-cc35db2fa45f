import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'voucher_tab_bar.dart';
import 'voucher_list_receive.dart';
import 'voucher_list_usable.dart';
import 'voucher_list_used.dart';
import 'voucher_footer_bar.dart';

///代金券页面状态管理
class VoucherPageState extends ChangeNotifier {
  int _currentIndex = 0;
  late List<String> _tabs;
  late PageController _pageController;

  // 数据成员变量
  late List<Map<String, dynamic>> _receiveVouchers;
  late List<Map<String, dynamic>> _usableVouchers;
  late List<Map<String, dynamic>> _usedVouchers;

  // Getters
  int get currentIndex => _currentIndex;

  List<String> get tabs => _tabs;

  PageController get pageController => _pageController;

  List<Map<String, dynamic>> get receiveVouchers => _receiveVouchers;

  List<Map<String, dynamic>> get usableVouchers => _usableVouchers;

  List<Map<String, dynamic>> get usedVouchers => _usedVouchers;

  VoucherPageState() {
    _initData();
    _initTabs();
    _pageController = PageController();
  }

  void _initData() {
    // 示例数据
    _receiveVouchers = [
      // {
      //   'amount': '6.00',
      //   'limit': '30.00',
      //   'time': '2025/07/24 14:46:08 - 2025/08/23 23:59:59',
      //   'title': 'VIP晋级券',
      //   'desc': '适用游戏：37手游部分游戏可用，不支持《灵魂序章》等',
      //   'rule': '1、在指定游戏内充值时，满足特定充值金额门槛，可直接抵扣使用\n2、限一次性使用\n3、每笔支付订单仅限使用1张券\n4、iOS仅限部分游戏公众号特惠商城使用\n5、最终解释权归37手游所有',
      // },
      // {
      //   'amount': '10.00',
      //   'limit': '50.00',
      //   'time': '2025/08/01 00:00:00 - 2025/08/31 23:59:59',
      //   'title': '暑期专享券',
      //   'desc': '适用游戏：37手游全部游戏',
      //   'rule': '1、仅限暑期活动期间使用\n2、每笔订单限用1张\n3、最终解释权归37手游所有',
      // },
    ];
    _usableVouchers = [
      {
        'amount': '6.00',
        'limit': '30.00',
        'time': '2025/07/24 14:46:08 - 2025/08/23 23:59:59',
        'title': 'VIP晋级券',
        'desc': '适用游戏：37手游部分游戏可用，不支持《灵魂序章》等',
        'rule':
            '1、在指定游戏内充值时，满足特定充值金额门槛，可直接抵扣使用\n2、限一次性使用\n3、每笔支付订单仅限使用1张券\n4、iOS仅限部分游戏公众号特惠商城使用\n5、最终解释权归37手游所有',
      },
      {
        'amount': '8.00',
        'limit': '40.00',
        'time': '2025/08/05 00:00:00 - 2025/09/05 23:59:59',
        'title': '充值满减券',
        'desc': '适用游戏：37手游热门游戏',
        'rule': '1、限热门游戏使用\n2、每笔订单限用1张\n3、最终解释权归37手游所有',
      },
    ];
    _usedVouchers = [
      {
        'amount': '4.00',
        'limit': '6.00',
        'time': '2025/07/25 10:28:11 - 2025/07/28 23:59:59',
        'title': 'VIP关怀券',
        'desc': '适用游戏：凡人修仙传：人界篇',
        'rule':
            '1、在指定游戏内充值时，满足特定充值金额门槛，可直接抵扣使用\n2、限一次性使用\n3、每笔支付订单仅限使用1张券\n4、iOS仅限部分游戏公众号特惠商城使用\n5、最终解释权归37手游所有',
      },
      {
        'amount': '2.00',
        'limit': '10.00',
        'time': '2025/07/01 00:00:00 - 2025/07/15 23:59:59',
        'title': '新手礼包券',
        'desc': '适用游戏：全部新注册用户',
        'rule': '1、仅限新用户使用\n2、每笔订单限用1张\n3、最终解释权归37手游所有',
      },
    ];
  }

  void _initTabs() {
    // 根据数据动态生成tabs
    _tabs = [
      '可领取(${_receiveVouchers.length})',
      '可使用(${_usableVouchers.length})',
      '已用完/已失效(${_usedVouchers.length})',
    ];
  }

  void updateTabs() {
    _tabs = [
      '可领取(${_receiveVouchers.length})',
      '可使用(${_usableVouchers.length})',
      '已用完/已失效(${_usedVouchers.length})',
    ];
    notifyListeners();
  }

  // 刷新可领取数据
  void refreshReceiveVouchers(List<Map<String, dynamic>> newVouchers) {
    _receiveVouchers = newVouchers;
    updateTabs(); // 更新tabs显示
    notifyListeners(); // 通知所有监听者重建
  }

  // 刷新可使用数据
  void refreshUsableVouchers(List<Map<String, dynamic>> newVouchers) {
    _usableVouchers = newVouchers;
    updateTabs(); // 更新tabs显示
    notifyListeners(); // 通知所有监听者重建
  }

  // 刷新已用完数据
  void refreshUsedVouchers(List<Map<String, dynamic>> newVouchers) {
    _usedVouchers = newVouchers;
    updateTabs(); // 更新tabs显示
    notifyListeners(); // 通知所有监听者重建
  }

  // 模拟网络请求刷新数据
  Future<void> refreshAllData() async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 500));

    // 更新数据
    _receiveVouchers = [
      {
        'amount': '5.00',
        'limit': '25.00',
        'time': '2025/08/01 00:00:00 - 2025/08/31 23:59:59',
        'title': '新用户专享券',
        'desc': '适用游戏：37手游全部游戏',
        'rule': '1、仅限新用户使用\n2、每笔订单限用1张\n3、最终解释权归37手游所有',
      },
    ];

    _usableVouchers = [
      {
        'amount': '10.00',
        'limit': '50.00',
        'time': '2025/08/01 00:00:00 - 2025/08/31 23:59:59',
        'title': '暑期大促券',
        'desc': '适用游戏：37手游热门游戏',
        'rule': '1、限热门游戏使用\n2、每笔订单限用1张\n3、最终解释权归37手游所有',
      },
      {
        'amount': '15.00',
        'limit': '75.00',
        'time': '2025/08/05 00:00:00 - 2025/09/05 23:59:59',
        'title': 'VIP专享券',
        'desc': '适用游戏：37手游VIP游戏',
        'rule': '1、限VIP游戏使用\n2、每笔订单限用1张\n3、最终解释权归37手游所有',
      },
    ];

    _usedVouchers = [
      {
        'amount': '3.00',
        'limit': '15.00',
        'time': '2025/07/20 10:28:11 - 2025/07/25 23:59:59',
        'title': '新手体验券',
        'desc': '适用游戏：凡人修仙传：人界篇',
        'rule': '1、限新手体验使用\n2、每笔订单限用1张\n3、最终解释权归37手游所有',
      },
    ];

    updateTabs();
    notifyListeners();
  }

  void onTabChanged(int index) {
    if (_currentIndex != index) {
      _currentIndex = index;
      notifyListeners();
      // 使用jumpToPage直接跳转，避免中间页面的闪烁
      _pageController.jumpToPage(index);
    }
  }

  void onPageChanged(int index) {
    if (_currentIndex != index) {
      _currentIndex = index;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
}

///代金券页面
class VoucherPage extends StatelessWidget {
  const VoucherPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => VoucherPageState(),
      child: const _VoucherPageContent(),
    );
  }
}

class _VoucherPageContent extends StatelessWidget {
  const _VoucherPageContent();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('代金券'),
        centerTitle: true,
        backgroundColor: const Color(0xFFF5F5F5),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(48),
          child: Selector<VoucherPageState, Map<String, dynamic>>(
            selector:
                (context, state) => {
                  'tabs': state.tabs,
                  'currentIndex': state.currentIndex,
                },
            builder: (context, data, child) {
              return VoucherTabBar(
                tabs: data['tabs'] as List<String>,
                currentIndex: data['currentIndex'] as int,
                onTabChanged: context.read<VoucherPageState>().onTabChanged,
              );
            },
          ),
        ),
      ),
      body: Container(
        color: Colors.white,
        child: Selector<VoucherPageState, PageController>(
          selector: (context, state) => state.pageController,
          builder: (context, pageController, child) {
            return PageView(
              controller: pageController,
              onPageChanged: context.read<VoucherPageState>().onPageChanged,
              children: [
                // 可领取列表
                Selector<VoucherPageState, List<Map<String, dynamic>>>(
                  selector: (context, state) => state.receiveVouchers,
                  builder: (context, vouchers, child) {
                    return VoucherListReceive(vouchers: vouchers);
                  },
                ),
                // 可使用列表
                Selector<VoucherPageState, List<Map<String, dynamic>>>(
                  selector: (context, state) => state.usableVouchers,
                  builder: (context, vouchers, child) {
                    return VoucherListUsable(vouchers: vouchers);
                  },
                ),
                // 已用完列表
                Selector<VoucherPageState, List<Map<String, dynamic>>>(
                  selector: (context, state) => state.usedVouchers,
                  builder: (context, vouchers, child) {
                    return VoucherListUsed(vouchers: vouchers);
                  },
                ),
              ],
            );
          },
        ),
      ),
      bottomNavigationBar: const VoucherFooterBar(userId: '1154385258'),
    );
  }
}
