import 'package:dlyz_flutter/pages/voucher/voucher_list_empty_item.dart';
import 'package:flutter/material.dart';
import 'voucher_card.dart';

class VoucherListUsable extends StatelessWidget {
  final List<Map<String, dynamic>> vouchers;
  const VoucherListUsable({Key? key, required this.vouchers}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (vouchers.isEmpty) {
      return VoucherListEmptyItem();
    }
    return CustomScrollView(
      key: const PageStorageKey('VoucherListUsable'),
      physics: const BouncingScrollPhysics(),
      slivers: [
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) => VoucherCard(
              key: ValueKey('usable_${index}_${vouchers[index]['amount']}'),
              data: vouchers[index],
              type: VoucherCardType.usable,
            ),
            childCount: vouchers.length,
          ),
        ),
      ],
    );
  }
} 