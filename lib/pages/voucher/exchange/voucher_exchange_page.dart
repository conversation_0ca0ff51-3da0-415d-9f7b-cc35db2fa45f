import 'package:flutter/material.dart';

/// 兑换代金券页面
class VoucherExchangePage extends StatefulWidget {
  const VoucherExchangePage({super.key});

  @override
  State<VoucherExchangePage> createState() => _VoucherExchangePageState();
}

class _VoucherExchangePageState extends State<VoucherExchangePage> {
  final TextEditingController _codeController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          '兑换代金券',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const SizedBox(height: 40),

            // 输入框
            Container(
              decoration: BoxDecoration(
                color: const Color(0xFFF5F5F5),
                borderRadius: BorderRadius.circular(8),
              ),
              child: TextField(
                controller: _codeController,
                textAlign: TextAlign.center,
                decoration: const InputDecoration(
                  hintText: '请粘贴或输入兑换码',
                  hintStyle: TextStyle(
                    color: Color(0xFF999999),
                    fontSize: 16,
                  ),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                ),
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.black,
                ),
              ),
            ),

            const SizedBox(height: 24),

            // 兑换按钮
            SizedBox(
              height: 48,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _onExchangePressed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFF6B35),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24),
                  ),
                  elevation: 0,
                ),
                child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text(
                        '兑换',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
              ),
            ),

            const SizedBox(height: 40),

            // 兑换说明
            const Text(
              '兑换说明：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),

            const SizedBox(height: 12),

            // 说明内容
            const Text(
              '1、兑换码核销方法:在订单中复制兑换码，在此页面粘贴兑换',
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF666666),
                height: 1.5,
              ),
            ),

            const SizedBox(height: 8),

            const Text(
              '2、兑换成功后，可在左侧"我的代金券""可使用"栏目查看代金券详情',
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF666666),
                height: 1.5,
              ),
            ),

            const SizedBox(height: 8),

            const Text(
              '3、代金券存在有效期，逾期自动失效，不退款不补发不延期，请在有效期内及时使用',
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF666666),
                height: 1.5,
              ),
            ),

            const SizedBox(height: 8),

            const Text(
              '4、每个兑换码仅可兑换一次，成功兑换后兑换码失效，不可重复兑换使用',
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF666666),
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 处理兑换按钮点击
  void _onExchangePressed() async {
    final code = _codeController.text.trim();

    if (code.isEmpty) {
      _showMessage('请输入兑换码');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // 模拟网络请求
      await Future.delayed(const Duration(seconds: 2));

      // 这里应该调用实际的兑换API
      // final result = await exchangeVoucher(code);

      // 模拟兑换成功
      if (mounted) {
        _showSuccessDialog();
      }
    } catch (e) {
      if (mounted) {
        _showMessage('兑换失败，请检查兑换码是否正确');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 显示消息提示
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 显示兑换成功对话框
  void _showSuccessDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('兑换成功'),
        content: const Text('代金券已成功兑换到您的账户，请在"我的代金券"中查看'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // 关闭对话框
              Navigator.of(context).pop(); // 返回上一页
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}