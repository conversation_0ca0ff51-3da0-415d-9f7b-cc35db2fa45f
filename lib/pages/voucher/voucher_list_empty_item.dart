import 'package:flutter/material.dart';

/// 代金券空状态组件
class VoucherListEmptyItem extends StatelessWidget {
  /// 点击"去看看"按钮的回调
  final VoidCallback? onGoToShop;

  /// 自定义空状态文案
  final String? emptyText;

  /// 自定义推荐标题
  final String? recommendTitle;

  /// 自定义推荐描述
  final String? recommendDesc;

  /// 自定义推荐特色
  final String? recommendFeature;

  /// 自定义按钮文案
  final String? buttonText;

  const VoucherListEmptyItem({
    super.key,
    this.onGoToShop,
    this.emptyText,
    this.recommendTitle,
    this.recommendDesc,
    this.recommendFeature,
    this.buttonText,
  });

  // 默认文案常量
  static const String _defaultEmptyText = '没有代金券';
  static const String _defaultRecommendTitle = '为你推荐';
  static const String _defaultRecommendDesc = '官方充值「特惠商城」';
  static const String _defaultRecommendFeature = '•充值秒到账•百分百返利';
  static const String _defaultButtonText = '去看看';

  // 样式常量
  static const double _iconSize = 60.0;
  static const double _emptyTextSize = 18.0;
  static const double _titleTextSize = 20.0;
  static const double _descTextSize = 16.0;
  static const double _buttonTextSize = 20.0;
  static const double _buttonHeight = 50.0;
  static const double _buttonRadius = 60.0;
  static const double _topMargin = 35.0;
  static const double _smallMargin = 8.0;
  static const double _buttonMargin = 20.0;
  static const double _horizontalPadding = 80.0;

  // 颜色常量
  static const Color _emptyTextColor = Color(0xFF99999A);
  static const Color _titleTextColor = Color(0xFF000000);
  static const Color _descTextColor = Color(0xFF3E3E3E);
  static const Color _buttonColor = Color(0xFFBC873B);
  static const Color _buttonTextColor = Color(0xFFFFFFFF);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          _buildEmptyIcon(),
          _buildEmptyText(),
          _buildRecommendSection(),
          _buildActionButton(),
        ],
      ),
    );
  }

  /// 构建空状态图标
  Widget _buildEmptyIcon() {
    return Container(
      width: _iconSize,
      height: _iconSize,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(0),
        image: const DecorationImage(
          image: AssetImage('assets/images/img_voucher_empty.png'),
          fit: BoxFit.cover,
        ),
      ),
    );
  }

  /// 构建空状态文案
  Widget _buildEmptyText() {
    return Text(
      emptyText ?? _defaultEmptyText,
      style: const TextStyle(
        fontSize: _emptyTextSize,
        color: _emptyTextColor,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  /// 构建推荐区域
  Widget _buildRecommendSection() {
    return Column(
      children: [
        _buildRecommendTitle(),
        _buildRecommendDesc(),
        _buildRecommendFeature(),
      ],
    );
  }

  /// 构建推荐标题
  Widget _buildRecommendTitle() {
    return Container(
      margin: const EdgeInsets.only(top: _topMargin),
      child: Text(
        recommendTitle ?? _defaultRecommendTitle,
        style: const TextStyle(
          fontSize: _titleTextSize,
          color: _titleTextColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// 构建推荐描述
  Widget _buildRecommendDesc() {
    return Container(
      margin: const EdgeInsets.only(top: _smallMargin),
      child: Text(
        recommendDesc ?? _defaultRecommendDesc,
        style: const TextStyle(
          fontSize: _descTextSize,
          color: _descTextColor,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  /// 构建推荐特色
  Widget _buildRecommendFeature() {
    return Container(
      margin: const EdgeInsets.only(top: _smallMargin),
      child: Text(
        recommendFeature ?? _defaultRecommendFeature,
        style: const TextStyle(
          fontSize: _descTextSize,
          color: _descTextColor,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton() {
    return Container(
      margin: const EdgeInsets.only(
        top: _buttonMargin,
        left: _horizontalPadding,
        right: _horizontalPadding,
      ),
      width: double.infinity,
      height: _buttonHeight,
      child: ElevatedButton(
        onPressed: onGoToShop,
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.all(_buttonColor),
          shape: MaterialStateProperty.all(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(_buttonRadius),
            ),
          ),
        ),
        child: Text(
          buttonText ?? _defaultButtonText,
          style: const TextStyle(
            fontSize: _buttonTextSize,
            color: _buttonTextColor,
          ),
        ),
      ),
    );
  }
}

/*
使用示例：

// 基本使用
VoucherListEmptyItem(
  onGoToShop: () {
    // 处理跳转逻辑
  },
)

// 自定义文案
VoucherListEmptyItem(
  onGoToShop: () {
    // 处理跳转逻辑
  },
  emptyText: '暂无可用代金券',
  recommendTitle: '推荐活动',
  recommendDesc: '限时特惠活动',
  recommendFeature: '•限时优惠•专属福利',
  buttonText: '立即查看',
)

// 完全自定义
VoucherListEmptyItem(
  onGoToShop: () {
    // 处理跳转逻辑
  },
  emptyText: '暂无代金券',
  recommendTitle: '推荐游戏',
  recommendDesc: '热门游戏推荐',
  recommendFeature: '•新游上线•限时优惠',
  buttonText: '去游戏',
)
*/
