import 'package:dlyz_flutter/pages/voucher/voucher_list_empty_item.dart';
import 'package:flutter/material.dart';
import 'voucher_card.dart';

class VoucherListReceive extends StatelessWidget {
  final List<Map<String, dynamic>> vouchers;

  const VoucherListReceive({Key? key, required this.vouchers})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (vouchers.isEmpty) {
      return VoucherListEmptyItem();
    }
    return CustomScrollView(
      key: const PageStorageKey('VoucherListReceive'),
      physics: const BouncingScrollPhysics(),
      slivers: [
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) => VoucherCard(
              key: ValueKey('receive_${index}_${vouchers[index]['amount']}'),
              data: vouchers[index],
              type: VoucherCardType.receive,
            ),
            childCount: vouchers.length,
          ),
        ),
      ],
    );
  }
}
