import 'package:dlyz_flutter/pages/voucher/voucher_list_empty_item.dart';
import 'package:flutter/material.dart';
import 'voucher_card.dart';

class VoucherListUsed extends StatelessWidget {
  final List<Map<String, dynamic>> vouchers;
  const VoucherListUsed({Key? key, required this.vouchers}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (vouchers.isEmpty) {
      return VoucherListEmptyItem();
    }
    return CustomScrollView(
      key: const PageStorageKey('VoucherListUsed'),
      physics: const BouncingScrollPhysics(),
      slivers: [
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) => VoucherCard(
              key: ValueKey('used_${index}_${vouchers[index]['amount']}'),
              data: vouchers[index],
              type: VoucherCardType.used,
            ),
            childCount: vouchers.length,
          ),
        ),
      ],
    );
  }
} 