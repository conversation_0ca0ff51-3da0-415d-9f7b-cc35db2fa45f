import 'package:dlyz_flutter/pages/voucher/problem/voucher_problem_page.dart';
import 'package:dlyz_flutter/pages/voucher/exchange/voucher_exchange_page.dart';
import 'package:flutter/material.dart';

class VoucherFooterBar extends StatelessWidget {
  final String userId;

  const VoucherFooterBar({super.key, required this.userId});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFF5F5F5),
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('用户ID: $userId', style: const TextStyle(fontSize: 15)),
          const SizedBox(width: 16),
          Container(width: 1, height: 16, color: Colors.grey[300]),
          const SizedBox(width: 16),
          GestureDetector(
            onTap:
                () => {
                  //跳转到常见问题页
                  _jumpToProblemPage(context)
                },
            child: const Text(
              '常见问题',
              style: TextStyle(fontSize: 15, color: Colors.black),
            ),
          ),
          const SizedBox(width: 16),
          Container(width: 1, height: 16, color: Colors.grey[300]),
          const SizedBox(width: 16),
          GestureDetector(
            onTap: () => _jumpToExchangePage(context),
            child: const Text(
              '兑换',
              style: TextStyle(fontSize: 15, color: Colors.black),
            ),
          ),
        ],
      ),
    );
  }

  /// 跳转到常见问题页
  void _jumpToProblemPage(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const VoucherProblemPage(),
      ),
    );
  }

  /// 跳转到兑换页面
  void _jumpToExchangePage(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const VoucherExchangePage(),
      ),
    );
  }
}
