import 'package:flutter/material.dart';

// 角色信息数据类
class GitClaimInfo {
  final String name;
  final String serverInfo;
  final bool canReceive;
  final String statusText;
  final String statusDetail;

  const GitClaimInfo({
    required this.name,
    required this.serverInfo,
    required this.canReceive,
    this.statusText = '',
    this.statusDetail = '',
  });
}

class CharacterSelectDialog extends StatefulWidget {
  final List<GitClaimInfo> characters;
  final VoidCallback? onConfirm;
  final VoidCallback? onSwitchAccount;

  const CharacterSelectDialog({
    super.key,
    required this.characters,
    this.onConfirm,
    this.onSwitchAccount,
  });

  @override
  State<CharacterSelectDialog> createState() => _CharacterSelectDialogState();

  // 静态方法用于显示底部弹窗
  static Future<void> show(
    BuildContext context, {
    required List<GitClaimInfo> characters,
    VoidCallback? onConfirm,
    VoidCallback? onSwitchAccount,
  }) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        builder: (context, scrollController) => CharacterSelectDialog(
          characters: characters,
          onConfirm: onConfirm,
          onSwitchAccount: onSwitchAccount,
        ),
      ),
    );
  }
}

class _CharacterSelectDialogState extends State<CharacterSelectDialog> {
  int selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 拖拽指示器
          Container(
            margin: const EdgeInsets.only(top: 8, bottom: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // 标题栏
          Container(
            padding: const EdgeInsets.fromLTRB(20, 12, 16, 16),
            child: Row(
              children: [
                const Expanded(
                  child: Text(
                    '请选择领奖角色',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: const Icon(
                    Icons.close,
                    size: 24,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          
          // 提示文字
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: const Text(
              '仅支持选择当前登录账号及绑定账号的角色',
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFFFF6B6B),
              ),
              textAlign: TextAlign.center,
            ),
          ),
          
          const SizedBox(height: 20),
          
          // 角色列表
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: widget.characters.asMap().entries.map((entry) {
                  int index = entry.key;
                  GitClaimInfo character = entry.value;
                  return _buildCharacterItem(character, index);
                }).toList(),
              ),
            ),
          ),
          
          // 底部提示和按钮
          Container(
            padding: const EdgeInsets.fromLTRB(20, 16, 20, 20),
            child: Column(
              children: [
                // 绑定提示
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      '绑定游戏微信、QQ等账号添加角色，',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        // 前往绑定页面
                      },
                      child: const Text(
                        '前往绑定>',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF1E88E5),
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // 确认按钮
                SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      widget.onConfirm?.call();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFFF6B6B),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(24),
                      ),
                      elevation: 0,
                    ),
                    child: const Text(
                      '确认',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(height: 12),
                
                // 切换账号按钮
                SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: OutlinedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      widget.onSwitchAccount?.call();
                    },
                    style: OutlinedButton.styleFrom(
                      foregroundColor: const Color(0xFFFF6B6B),
                      side: const BorderSide(color: Color(0xFFFF6B6B)),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(24),
                      ),
                    ),
                    child: const Text(
                      '切换账号领奖',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCharacterItem(GitClaimInfo character, int index) {
    bool isSelected = selectedIndex == index;
    
    return GestureDetector(
      onTap: () {
        if (character.canReceive) {
          setState(() {
            selectedIndex = index;
          });
        }
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFFF8F8F8),
          borderRadius: BorderRadius.circular(12),
          border: isSelected ? Border.all(color: const Color(0xFFFF6B6B), width: 2) : null,
        ),
        child: Row(
          children: [
            // 头像
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: const Color(0xFFFFD700),
                borderRadius: BorderRadius.circular(24),
              ),
              child: const Icon(
                Icons.person,
                color: Colors.white,
                size: 24,
              ),
            ),
            
            const SizedBox(width: 12),
            
            // 角色信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    character.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    character.serverInfo,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
            
            // 右侧状态
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                if (character.canReceive)
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: isSelected ? const Color(0xFFFF6B6B) : Colors.transparent,
                      border: Border.all(
                        color: isSelected ? const Color(0xFFFF6B6B) : Colors.grey[400]!,
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: isSelected
                        ? const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 16,
                          )
                        : null,
                  )
                else
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        character.statusText,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFFFF6B6B),
                        ),
                      ),
                      if (character.statusDetail.isNotEmpty)
                        Text(
                          character.statusDetail,
                          style: const TextStyle(
                            fontSize: 10,
                            color: Colors.grey,
                          ),
                        ),
                    ],
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
