import 'package:flutter/material.dart';

class GiftDetailDialog extends StatelessWidget {
  final String title;
  final String contents;
  final String eligibility;
  final String claimTime;
  final List<String> rules;
  final bool canClaim;
  final String buttonText;
  final VoidCallback? onClaim;

  const GiftDetailDialog({
    super.key,
    required this.title,
    required this.contents,
    required this.eligibility,
    required this.claimTime,
    required this.rules,
    required this.canClaim,
    required this.buttonText,
    this.onClaim,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 拖拽指示器
          Container(
            margin: const EdgeInsets.only(top: 8, bottom: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // 标题栏
          Container(
            padding: const EdgeInsets.fromLTRB(20, 12, 16, 16),
            child: Row(
              children: [
                const Expanded(
                  child: Text(
                    '礼包',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: const Icon(
                    Icons.close,
                    size: 24,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          
          // 内容区域
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 礼包内容
                  _buildSection(
                    title: '礼包内容：',
                    content: contents,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 礼包说明
                  _buildSection(
                    title: '礼包说明：',
                    content: eligibility,
                    hasLink: true,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 领取时间
                  _buildSection(
                    title: '领取时间：',
                    content: claimTime,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 领取规则
                  _buildRulesSection(),
                  
                  const SizedBox(height: 24),
                  
                  // 按钮
                  _buildActionButton(context),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required String content,
    bool hasLink = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        if (hasLink)
          RichText(
            text: TextSpan(
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black54,
                height: 1.4,
              ),
              children: [
                TextSpan(text: content.split('（')[0]),
                if (content.contains('（'))
                  TextSpan(
                    text: '（${content.split('（')[1]}',
                    style: const TextStyle(
                      color: Color(0xFF1E88E5),
                    ),
                  ),
              ],
            ),
          )
        else
          Text(
            content,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black54,
              height: 1.4,
            ),
          ),
      ],
    );
  }

  Widget _buildRulesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '领取规则：',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        ...rules.asMap().entries.map((entry) {
          int index = entry.key;
          String rule = entry.value;
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Text(
              '${index + 1}.$rule',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black54,
                height: 1.4,
              ),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildActionButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: ElevatedButton(
        onPressed: canClaim ? () {
          Navigator.of(context).pop();
          onClaim?.call();
        } : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: canClaim ? const Color(0xFFF97727) : Colors.grey[300],
          foregroundColor: canClaim ? Colors.white : Colors.grey[600],
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          elevation: 0,
          disabledBackgroundColor: Colors.grey[300],
          disabledForegroundColor: Colors.grey[600],
        ),
        child: Text(
          buttonText,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  // 静态方法用于显示底部弹窗
  static Future<void> show(
    BuildContext context, {
    required String title,
    required String contents,
    required String eligibility,
    required String claimTime,
    required List<String> rules,
    required bool canClaim,
    required String buttonText,
    VoidCallback? onClaim,
  }) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        builder: (context, scrollController) => GiftDetailDialog(
          title: title,
          contents: contents,
          eligibility: eligibility,
          claimTime: claimTime,
          rules: rules,
          canClaim: canClaim,
          buttonText: buttonText,
          onClaim: onClaim,
        ),
      ),
    );
  }
}
