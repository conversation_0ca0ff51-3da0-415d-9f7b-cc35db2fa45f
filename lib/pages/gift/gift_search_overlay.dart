import 'package:flutter/material.dart';

import 'gift_center_page.dart';

// 搜索覆盖层组件
class GiftSearchOverlay extends StatefulWidget {
  final bool isVisible;
  final VoidCallback onClose;
  final Function(List<GameGiftItem>?)? onSearchResultsChanged; // 修改：返回搜索结果List

  const GiftSearchOverlay({
    super.key,
    required this.isVisible,
    required this.onClose,
    this.onSearchResultsChanged,
  });

  @override
  State<GiftSearchOverlay> createState() => _GiftSearchOverlayState();
}

class _GiftSearchOverlayState extends State<GiftSearchOverlay> with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200), // 减少动画时间，提升响应速度
      vsync: this,
    );
    _slideAnimation = Tween<double>(
      begin: -1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic, // 使用更流畅的动画曲线
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(GiftSearchOverlay oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isVisible && !oldWidget.isVisible) {
      // 显示搜索覆盖层
      _animationController.forward();
      // 减少延迟时间，提升响应速度
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          _searchFocusNode.requestFocus();
        }
      });
    } else if (!widget.isVisible && oldWidget.isVisible) {
      // 隐藏搜索覆盖层
      _animationController.reverse();
      _searchFocusNode.unfocus();
      _searchController.clear();
      // 不清除搜索结果，保持搜索后的状态
    }
  }

  // 顶部搜索栏
  Widget _buildTopSearchBar() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: AnimatedBuilder(
        animation: _slideAnimation,
        builder: (context, child) {
          return Transform.translate(
            offset: Offset(0, _slideAnimation.value * 50), // 减少移动距离
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Colors.white,
                border: Border(
                  bottom: BorderSide(
                    color: Color(0xFFF5F5F5),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  // 搜索框
                  Expanded(
                    child: Container(
                      height: 36,
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(18),
                      ),
                      child: TextField(
                        controller: _searchController,
                        focusNode: _searchFocusNode,
                        decoration: const InputDecoration(
                          hintText: '斗罗大陆：猎魂世界',
                          hintStyle: TextStyle(
                            fontSize: 16,
                            color: Colors.grey,
                          ),
                          prefixIcon: Icon(
                            Icons.search,
                            size: 18,
                            color: Colors.grey,
                          ),
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                          prefixIconConstraints: BoxConstraints(
                            minWidth: 40,
                            minHeight: 36,
                          ),
                        ),
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // 搜索按钮
                  GestureDetector(
                    onTap: () {
                      _performSearchAndClose(_searchController.text);
                    },
                    child: const Text(
                      '搜索',
                      style: TextStyle(
                        color: Color(0xFFF97727),
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }



  // 执行搜索并关闭搜索框
  void _performSearchAndClose(String query) {
    // 获取所有游戏数据进行搜索
    final allGames = _getAllGames();
    final searchResults = query.isEmpty
        ? allGames // 如果搜索内容为空，返回全部内容
        : allGames.where((game) => game.name.toLowerCase().contains(query.toLowerCase())).toList();

    // 关闭搜索框
    _animationController.reverse().then((_) {
      widget.onClose();
    });
    _searchFocusNode.unfocus();

    // 通知主页面搜索结果
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.onSearchResultsChanged != null) {
        widget.onSearchResultsChanged!(searchResults);
      }
    });
  }

  // 获取所有游戏数据
  List<GameGiftItem> _getAllGames() {
    return [
      GameGiftItem(
        name: '斗罗大陆:猎魂世界',
        iconUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        giftCount: 2,
        badges: ['MMO', '37手游'],
      ),
      GameGiftItem(
        name: '斗罗大陆:魂师对决',
        iconUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        giftCount: 3,
        badges: ['RPG'],
      ),
      GameGiftItem(
        name: '凡人修仙传:人界篇',
        iconUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        giftCount: 1,
        badges: ['凡人修仙传'],
      ),
      GameGiftItem(
        name: '三国群英传:鸿鹄霸业',
        iconUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        giftCount: 4,
        badges: ['SLG'],
      ),
      GameGiftItem(
        name: '离火之境',
        iconUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        giftCount: 2,
        badges: ['MMO'],
      ),
      GameGiftItem(
        name: '小小蚁国',
        iconUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        giftCount: 1,
        badges: ['休闲'],
      ),
      GameGiftItem(
        name: '诸神黄昏:征服',
        iconUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        giftCount: 3,
        badges: ['RPG'],
      ),
      GameGiftItem(
        name: '龙骑士学园',
        iconUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        giftCount: 2,
        badges: ['卡牌'],
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible) {
      return const SizedBox.shrink();
    }

    return RepaintBoundary( // 添加重绘边界，提升性能
      child: Stack(
        children: [
          // 半透明遮罩
          Container(
            color: Colors.black.withValues(alpha: 0.3),
            child: GestureDetector(
              onTap: () {
                _animationController.reverse().then((_) {
                  widget.onClose();
                });
                _searchFocusNode.unfocus();
              },
            ),
          ),

          // 顶部搜索栏
          _buildTopSearchBar(),
        ],
      ),
    );
  }


}
