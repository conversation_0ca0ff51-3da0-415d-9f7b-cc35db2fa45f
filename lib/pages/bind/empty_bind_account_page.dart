import 'package:flutter/material.dart';

class EmptyBindAccountPage extends StatefulWidget {
  final String phoneNumber;

  const EmptyBindAccountPage({
    super.key,
    required this.phoneNumber,
  });

  @override
  State<EmptyBindAccountPage> createState() => _EmptyBindAccountPageState();
}

class _EmptyBindAccountPageState extends State<EmptyBindAccountPage> {
  bool _showAlternativeMethods = false;

  // 模拟已绑定角色数据
  final List<BoundCharacter> _boundCharacters = [
    BoundCharacter(
      name: '角色昵称',
      server: '区服区服',
      level: '99级',
      avatar: 'https://imgcs.s98s2.com/common/**********火舞头像.png',
      isBound: true,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          '绑定角色',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const SizedBox(height: 30),
            
            // 空状态图标
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.description_outlined,
                size: 50,
                color: Colors.grey[400],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // 提示文字
            Text(
              '手机账号${widget.phoneNumber}未检测到其他可关联的角色',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 32),
            
            // 已绑定角色提示
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                '你已绑定该账号的以下角色：',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 已绑定角色列表
            ...List.generate(_boundCharacters.length, (index) {
              return _buildBoundCharacterItem(_boundCharacters[index]);
            }),
            
            const SizedBox(height: 32),
            
            // 找不到角色提示
            Center(
              child: GestureDetector(
                onTap: _toggleAlternativeMethods,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '未找到角色？',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[500],
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '试试别的办法',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.blue[600],
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(
                      _showAlternativeMethods ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                      size: 16,
                      color: Colors.blue[600],
                    ),
                  ],
                ),
              ),
            ),
            
            // 展开的替代方案
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              height: _showAlternativeMethods ? null : 0,
              child: AnimatedOpacity(
                duration: const Duration(milliseconds: 300),
                opacity: _showAlternativeMethods ? 1.0 : 0.0,
                child: _showAlternativeMethods ? Column(
                  children: [
                    const SizedBox(height: 20),
                    
                    // 换个账号按钮
                    Container(
                      width: double.infinity,
                      margin: const EdgeInsets.symmetric(horizontal: 0),
                      child: OutlinedButton(
                        onPressed: _changeAccount,
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          side: BorderSide(color: Colors.grey[300]!),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.person_outline, color: Colors.grey[600], size: 20),
                            const SizedBox(width: 8),
                            Text(
                              '换个账号',
                              style: TextStyle(
                                color: Colors.grey[800],
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 12),
                    
                    // 拉起游戏授权绑定按钮
                    Stack(
                      clipBehavior: Clip.none,
                      children: [
                        Container(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: _launchGameAuthorization,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue[600],
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              elevation: 0,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(Icons.image_outlined, size: 20),
                                const SizedBox(width: 8),
                                const Text(
                                  '拉起游戏授权绑定',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        // 叠在右上角的标签
                        Positioned(
                          top: -8,
                          right: 8,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Text(
                              '非37官方账号推荐使用',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // 底部提示文字
                    Text(
                      '选择授权游戏包后游戏内点击【确定】',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ) : const SizedBox.shrink(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBoundCharacterItem(BoundCharacter character) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.05),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // 角色头像
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.grey[200],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  character.avatar,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.grey[200],
                      child: Icon(
                        Icons.person,
                        color: Colors.grey[400],
                        size: 24,
                      ),
                    );
                  },
                ),
              ),
            ),
            
            const SizedBox(width: 12),
            
            // 角色信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    character.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${character.server}  ${character.level}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            
            // 绑定状态
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                '已绑定',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleAlternativeMethods() {
    setState(() {
      _showAlternativeMethods = !_showAlternativeMethods;
    });
  }

  void _changeAccount() {
    Navigator.pop(context);
  }

  void _launchGameAuthorization() {
    // TODO: 实现拉起游戏授权绑定功能
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('拉起游戏授权绑定'),
          content: const Text('正在启动游戏进行授权绑定...'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }
}

class BoundCharacter {
  final String name;
  final String server;
  final String level;
  final String avatar;
  final bool isBound;

  BoundCharacter({
    required this.name,
    required this.server,
    required this.level,
    required this.avatar,
    required this.isBound,
  });
}