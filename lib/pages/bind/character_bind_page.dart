import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../config/app_config.dart';
import '../../manager/channel_manager.dart';
import '../../model/game_binding_info.dart';
import '../../model/game_role.dart';
import '../../providers/user_provider.dart';
import 'bind_success_page.dart';
import 'dart:io';

class CharacterBindPage extends StatefulWidget {
  final String phoneNumber;
  final List<GameRole> allRoles;
  final List<GameRoleV2> boundRoles;

  const CharacterBindPage({
    super.key,
    required this.phoneNumber,
    required this.allRoles,
    required this.boundRoles,
  });

  @override
  State<CharacterBindPage> createState() => _CharacterBindPageState();
}

class _CharacterBindPageState extends State<CharacterBindPage> {
  int? _selectedCharacterIndex; // 改为可空，允许没有选中状态
  bool _showAlternativeMethods = false;
  
  // 已绑定角色ID集合
  late Set<String> _boundRoleIds;
  
  @override
  void initState() {
    super.initState();
    // 初始化已绑定角色ID集合
    _boundRoleIds = widget.boundRoles.map((role) => role.drid.toString()).toSet();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          '绑定角色',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 手机号信息
                  Text(
                    '账号${widget.phoneNumber}检测到以下角色：',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                      height: 1.5,
                    ),
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // 角色列表
                  ...List.generate(widget.allRoles.length, (index) {
                    return _buildCharacterItem(index);
                  }),
                  
                  const SizedBox(height: 30),
                  
                  // 找不到角色提示
                  Center(
                    child: GestureDetector(
                      onTap: _toggleAlternativeMethods,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '未找到角色？',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[500],
                            ),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '试试别的办法',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.blue[600],
                            ),
                          ),
                          const SizedBox(width: 4),
                          Icon(
                            _showAlternativeMethods ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                            size: 16,
                            color: Colors.blue[600],
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  // 展开的替代方案
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    height: _showAlternativeMethods ? null : 0,
                    child: AnimatedOpacity(
                      duration: const Duration(milliseconds: 300),
                      opacity: _showAlternativeMethods ? 1.0 : 0.0,
                      child: _showAlternativeMethods ? Column(
                        children: [
                          const SizedBox(height: 20),
                          // 换个账号按钮
                          Container(
                            width: double.infinity,
                            margin: const EdgeInsets.symmetric(horizontal: 0),
                            child: OutlinedButton(
                              onPressed: _changeAccount,
                              style: OutlinedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                side: BorderSide(color: Colors.grey[300]!),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.person_outline, color: Colors.grey[600], size: 20),
                                  const SizedBox(width: 8),
                                  Text(
                                    '换个账号',
                                    style: TextStyle(
                                      color: Colors.grey[800],
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 12),
                          // 拉起游戏授权绑定按钮
                          Container(
                            width: double.infinity,
                            margin: const EdgeInsets.symmetric(horizontal: 0),
                            child: Stack(
                              clipBehavior: Clip.none, // 允许子元素超出边界显示
                              children: [
                                OutlinedButton(
                                  onPressed: _launchGameAuthorization,
                                  style: OutlinedButton.styleFrom(
                                    backgroundColor: Colors.white,
                                    foregroundColor: Colors.black87,
                                    padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                                    side: BorderSide(color: Colors.grey[300]!, width: 1),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.image_outlined,
                                        color: Colors.grey[600],
                                        size: 20,
                                      ),
                                      const SizedBox(width: 8),
                                      const Text(
                                        '拉起游戏授权绑定',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                          color: Colors.black87,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                // 叠在右上角的标签
                                Positioned(
                                  top: -8, // 负值让标签向上偏移，实现一半超出按钮边框的效果
                                  right: 8,
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: Colors.red,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: const Text(
                                      '非37官方账号推荐使用',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 10,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 8),
                          // 底部提示文字
                          Text(
                            '选择授权游戏后游戏内点击【确定】',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ) : const SizedBox.shrink(),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // 底部绑定按钮
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 1,
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: SafeArea(
              // 仅保留底部安全区域，避免底部按钮被 Home 指示条遮挡，
              // 同时不增加不必要的顶部间距。
              top: false,
              child: SizedBox(
                height: 48,
                child: ElevatedButton(
                  onPressed: _selectedCharacterIndex != null ? _bindCharacter : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _selectedCharacterIndex != null 
                        ? Colors.blue[600] 
                        : Colors.blue[600]!.withOpacity(0.5),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    elevation: 0,
                  ),
                  child: const Text(
                    '绑定角色',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCharacterItem(int index) {
    final role = widget.allRoles[index];
    final isSelected = _selectedCharacterIndex == index;
    final isBound = _boundRoleIds.contains(role.drid);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: GestureDetector(
        onTap: isBound ? null : () {
          setState(() {
            // 如果点击已选中的角色，则取消选择；否则选择该角色
            if (_selectedCharacterIndex == index) {
              _selectedCharacterIndex = null;
            } else {
              _selectedCharacterIndex = index;
            }
          });
        },
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isBound ? Colors.grey[100] : Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isBound 
                  ? Colors.grey[300]!
                  : (isSelected ? Colors.blue[600]! : Colors.grey[200]!),
              width: isSelected ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.05),
                spreadRadius: 1,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // 角色头像
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey[200],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: ColorFiltered(
                    colorFilter: isBound 
                        ? const ColorFilter.mode(Colors.grey, BlendMode.saturation)
                        : const ColorFilter.mode(Colors.transparent, BlendMode.multiply),
                    child: Icon(
                      Icons.person,
                      color: isBound ? Colors.grey[400] : Colors.grey[600],
                      size: 24,
                    ),
                  ),
                ),
              ),
              
              const SizedBox(width: 12),
              
              // 角色信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            role.drname,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: isBound ? Colors.grey[500] : Colors.black87,
                            ),
                          ),
                        ),
                        if (isBound)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.grey[400],
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: const Text(
                              '已绑定',
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${role.dsname}  ${role.drlevel}级',
                      style: TextStyle(
                        fontSize: 14,
                        color: isBound ? Colors.grey[400] : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              
              // 选择状态
              if (!isBound)
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected ? Colors.blue[600]! : Colors.grey[300]!,
                      width: 2,
                    ),
                    color: isSelected ? Colors.blue[600] : Colors.transparent,
                  ),
                  child: isSelected
                      ? const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 16,
                        )
                      : null,
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _toggleAlternativeMethods() {
    setState(() {
      _showAlternativeMethods = !_showAlternativeMethods;
    });
  }

  void _changeAccount() {
    // TODO: 实现换账号功能
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('换个账号'),
          content: const Text('换账号功能开发中...'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  void _launchGameAuthorization() {
    // TODO: 实现拉起游戏授权绑定功能
    try {
      var ticket = context.read<UserProvider>().currentTicket;
      if (ticket?.isNotEmpty == true) {
        print("userTicket $ticket");
        var gameBindingInfo = GameBindingInfo(
          appTicket: ticket ?? '',
          appId: AppConfig.appId,
          appPid: AppConfig.pid,
          appGid: AppConfig.gid,
          traceId: GameBindingInfo.generateTraceId()
        );
        print("params ${jsonEncode(gameBindingInfo.toJson())}");
        // 如果当前是iOS环境，使用包名com.37ios.test
        if (Platform.isIOS) {
          ChannelManager().bindingGame(
              packageName: "com.37ios.test",
              gameBindingInfo: gameBindingInfo,
              gameExtParams: { 'dialogContent': '请确认授权当前登录信息给《斗罗宇宙》'}
          );
        }
        else{
          ChannelManager().bindingGame(
            packageName: "com.sy.yxjun",
            gameBindingInfo: gameBindingInfo,
            gameExtParams: { 'dialogContent': '请确认授权当前登录信息给《斗罗宇宙》'}
          );
        }
      } else {
        print("用户未登录或票据为空，无法启动游戏");
      }
    } catch (e) {
      print("启动游戏失败: $e");
    }
  }

  void _bindCharacter() {
    if (_selectedCharacterIndex == null) return;
    
    final selectedRole = widget.allRoles[_selectedCharacterIndex!];
    
    // 显示绑定确认对话框
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('确认绑定'),
          content: Text('确定要绑定角色"${selectedRole.drname}"吗？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _performBind(selectedRole);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue[600],
                foregroundColor: Colors.white,
              ),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  void _performBind(GameRole role) {
    // 显示加载对话框
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在绑定角色...'),
            ],
          ),
        );
      },
    );

    // 模拟绑定过程
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        Navigator.pop(context); // 关闭加载对话框
        
        // 跳转到绑定成功页面
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => BindSuccessPage(
              characterName: role.drname,
            ),
          ),
        );
      }
    });
  }
}