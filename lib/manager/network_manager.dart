import 'dart:async';
import 'package:dlyz_flutter/manager/channel_manager.dart';
import 'package:dlyz_flutter/model/connectivity_result.dart';

/// 网络管理器，封装网络连接状态监听功能
class NetworkManager {
  static final NetworkManager _instance = NetworkManager._internal();
  factory NetworkManager() => _instance;
  NetworkManager._internal();

  final ChannelManager _channelManager = ChannelManager();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  List<ConnectivityResult>? _lastConnectivityResult;

  /// 初始化网络监听
  void addListener({Function(List<ConnectivityResult>)? onConnectivityChanged}) {
    _connectivitySubscription = _channelManager.onConnectivityChanged.listen((List<ConnectivityResult> result) {
      if (!_listsEqual(_lastConnectivityResult, result)) {
        _lastConnectivityResult = List.from(result);
        if (onConnectivityChanged != null) {
          onConnectivityChanged(result);
        }
      }
    });
  }

  /// 销毁网络监听
  void dispose() {
    _connectivitySubscription?.cancel();
    _connectivitySubscription = null;
  }

  /// 获取当前网络状态（异步）
  Future<List<ConnectivityResult>> checkNetworkConnectivity() async {
    _lastConnectivityResult = await _channelManager.checkNetworkConnectivity();
    return _lastConnectivityResult!;
  }

  /// 获取最近一次的网络状态（同步）
  /// 注意：如果还没有初始化或者没有监听到网络状态，可能返回 null
  List<ConnectivityResult>? getCurrentConnectivity() {
    return _lastConnectivityResult;
  }

  /// 比较两个列表是否相等
  bool _listsEqual(List<ConnectivityResult>? a, List<ConnectivityResult>? b) {
    if (a == null || b == null) return a == b;
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }
}