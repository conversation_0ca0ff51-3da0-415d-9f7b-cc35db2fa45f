
import 'package:dlyz_flutter/manager/channel_manager.dart';
import 'package:dlyz_flutter/model/game_post_list.dart';
import 'package:dlyz_flutter/model/mini_program_info.dart';
import 'package:dlyz_flutter/net/api/game_list_service.dart';
import 'package:dlyz_flutter/services/download/internal/ALDownloaderStringExtension.dart';
import 'package:flutter/cupertino.dart';
import 'package:installed_apps/app_info.dart';
import 'package:installed_apps/installed_apps.dart';

import '../model/download_info.dart';
import '../model/game_card_info.dart';
import '../model/game_package_info.dart';
import '../utils/permission_utils.dart';

class GameDataManager {
  static final GameDataManager _instance = GameDataManager._internal();

  factory GameDataManager() {
    return _instance;
  }

  GameDataManager._internal();

  // 存储已安装应用的列表
  List<AppInfo> _installedPackageNames = [];

  // 存储已安装游戏的列表
  Map<String, List<GamePackageInfo>> _installedGamePackages = {};
  
  // 获取已安装应用列表的缓存
  List<AppInfo> get installedPackageNames => List.unmodifiable(_installedPackageNames);

  // 获取已安装游戏列表的缓存
  Map<String, List<GamePackageInfo>> get installedGamePackages => Map.unmodifiable(_installedGamePackages);

  final List<GameCardInfo> gameItems = [
    GameCardInfo(
        title: '港台云上城之歌',
        type: '冒险·动作·MMORPG',
        subtitle: '这一次，重新定义斗罗世界！',
        iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png',
        imageUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        videoUrl: 'https://imgcs.s98s2.com/aicc/video/1752075938385.mp4',
        packageName: 'com.tg.ysczg.tw',
        tag: '必得体验金',
        officialWeb: 'https://lhsj.37.com.cn/',
        miniProgram: '230',
        channelPackages: [
          GamePackageInfo(id: 1, tag: '', iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png', packageName: 'com.tt.lhgzs.asdv', miniProgram: '', isOfficial: true),
          GamePackageInfo(id: 2, tag: '', iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png', packageName: 'com.tg.ysczg.tw', miniProgram: '', isOfficial: false),
          GamePackageInfo(id: 4, tag: '', iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png', packageName: 'com.tg.ysczg.tw', miniProgram: '', isOfficial: false),
        ],
        downloadInfo: DownloadInfo(
            name: '港台云上城之歌',
            fileName: '',
            directoryPath: '',
            url: 'https://developer-mt.nofeba.com/media/DiskExtension/android_oversea_pack_output/2025_07_30/1000100_com_tg_ysczg_tw_seasdk_37000000_20250730_095649_1753840621.apk'
        )
    ),
    GameCardInfo(
        title: '斗罗大陆：猎魂世界',
        type: '卡牌·回合制·养成·多人联机',
        subtitle: '真正能打的斗罗大陆',
        iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png',
        imageUrl: 'https://imgcs.s98s2.com/image/webSite/article/1752072193000/MMO%E4%BB%A3%E8%A8%80%E4%BA%BA%E6%88%90%E9%BE%99.jpg',
        videoUrl: 'https://imgcs.s98s2.com/aicc/video/1752075938385.mp4',
        packageName: 'com.tt.lhgzs.dddllhsj',
        tag: '必得体验金',
        officialWeb: 'https://lhsj.37.com.cn/',
        miniProgram: '230',
        channelPackages: [
          GamePackageInfo(id: 1, tag: '', iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png', packageName: 'com.tt.lhgzs.dddllhsj', miniProgram: '', isOfficial: true),
          GamePackageInfo(id: 2, tag: '', iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png', packageName: 'com.tg.ysczg.tw', miniProgram: '', isOfficial: false),
          GamePackageInfo(id: 3, tag: '', iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png', packageName: 'com.tg.ysczg.tw', miniProgram: '', isOfficial: false),
          GamePackageInfo(id: 4, tag: '', iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png', packageName: 'com.tg.ysczg.tw', miniProgram: '', isOfficial: false),
        ],
        downloadInfo: DownloadInfo(
            name: '斗罗大陆：猎魂世界',
            fileName: '',
            directoryPath: '',
            url: 'https://dlcs.37tgy.com/upload/1_1023086_19077/douluodaluliehunshijie-pinzhuanguanggaosibu37zigengxin2_1001.apk'
        )
    ),
    GameCardInfo(
        title: '指尖像素城',
        type: '冒险·动作·MMORPG',
        subtitle: '1000万预约达成!',
        iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png',
        imageUrl: 'https://imgcs.s98s2.com/image/webSite/article/1752195478000/%E6%88%90%E5%9B%BE.jpg',
        videoUrl: '',
        packageName: 'com.gzqh.zjxsc.huawei',
        tag: '',
        officialWeb: '',
        miniProgram: '',
        channelPackages: [],
        downloadInfo: DownloadInfo(
            name: '指尖像素城',
            fileName: '',
            directoryPath: '',
            url: 'https://developer-mt.nofeba.com/media/android_mainland_pack_output/2025_06_23/huawei_20250618-dcb8ab4f-8196-4948-84f8-8f964f875134_20250623_1750652824.apk'
        )
    ),
    GameCardInfo(
        title: '斗罗大陆：猎魂世界',
        type: '冒险·动作·MMORPG',
        subtitle: '1000万预约达成!',
        iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png',
        imageUrl: 'https://imgcs.s98s2.com/image/webSite/article/1752195478000/%E6%88%90%E5%9B%BE.jpg',
        videoUrl: '',
        packageName: 'com.gzqh.zjxsc.huawei',
        tag: 'ios',
        officialWeb: '',
        miniProgram: '',
        channelPackages: [],
        downloadInfo: DownloadInfo(
            name: '斗罗大陆：猎魂世界',
            fileName: '',
            directoryPath: '',
            url: 'https://apps.apple.com/hk/app/%E6%96%97%E7%BE%85%E5%A4%A7%E9%99%B8-%E7%8D%B5%E9%AD%82%E4%B8%96%E7%95%8C/id6738236172'
        )
    ),
  ];

  List<GameListDetail> gameList = [];

  Future<void> requestGameList() async {
    debugPrint("开始请求游戏中心列表");
    try {
      final gameService = GameListService();
      final response = await gameService.getGameList();
      if (response.isSuccess && response.data != null) {
        var record = response.data?.record;
        if (record != null) {
          gameList = record;
        }
        debugPrint('游戏中心列表加载成功: 游戏列表=$gameList');
      } else {
        debugPrint('游戏中心列表加载失败: code=${response.code}, msg=${response.message}');
      }
    } catch (e) {
      debugPrint('游戏中心列表加载异常$e');
    }
  }

  /// 根据flagId获取小程序跳转配置并跳转
  Future<void> requestJumpMiniProgram(int flagId, {String extra = ""}) async {
    try {
      final gameService = GameListService();
      final response = await gameService.getJumpMiniProgramConfig(flagId: flagId);
      if (response.isSuccess && response.data != null) {
        var skipType = response.data?.skipType;
        if (skipType != null && skipType != 0) {
          final miniProgramPath = response.data?.miniProgramPath ?? '';
          final schemeUrl = response.data?.schemeUrl ?? '';
          ChannelManager().jumpToMiniProgram(info: MiniProgramInfo(
              skipType: skipType,
              appId: response.data?.appId ?? '',
              miniProgramId: response.data?.miniProgramId ?? '',
              miniProgramPath: miniProgramPath + extra,
              schemeUrl: schemeUrl + extra
          ));
        }
      } else {
        debugPrint('跳转小程序失败: code=${response.code}, msg=${response.message}');
      }
    } catch (e) {
      debugPrint('跳转小程序异常$e');
    }
  }

  /// 获取已安装应用列表并存储包名
  Future<void> loadInstalledApps() async {
    try {
      _installedPackageNames = await InstalledApps.getInstalledApps(true, true);
    } catch (e) {
      debugPrint('获取已安装应用列表失败: $e');
      _installedPackageNames = [];
    }
  }

  /// 重载已安装游戏列表
  Future<void> reloadInstalledGames() async {
    try {
      final apps = _installedPackageNames;
      Map<String, List<GamePackageInfo>> gameList = {};
      for (final game in gameItems) {
        final packagesLength = game.channelPackages.length;
        gameList.putIfAbsent(game.packageName, () => [GamePackageInfo(id: 1, tag: "", iconUrl: game.iconUrl, packageName: game.packageName, miniProgram: "", isOfficial: true)]);
        for (int index = 0; index < packagesLength; index++) {
          if (apps.isEmpty) {
            gameList[game.packageName]?.add(GamePackageInfo(id: index+2, tag: "", iconUrl: game.channelPackages[index].iconUrl, packageName: game.channelPackages[index].packageName, miniProgram: "", isOfficial: false));
          } else {
            AppInfo? exitPackage = findLocalPackage(game.channelPackages[index].packageName);
            if (exitPackage == null) {
              continue;
            }
            var icon = exitPackage.icon ?? game.channelPackages[index].iconUrl;
            gameList[game.packageName]?.add(GamePackageInfo(id: index+2, tag: "", iconUrl: icon, packageName: exitPackage.packageName, miniProgram: "", isOfficial: false));
          }
        }
        final listLength = gameList[game.packageName]?.length;
        if (listLength != null && game.miniProgram.isNotEmpty) {
          gameList[game.packageName]?.add(GamePackageInfo(id: listLength + 1, tag: "微信小游戏", iconUrl: game.iconUrl, packageName: "", miniProgram: game.miniProgram, isOfficial: false));
        }
      }
      _installedGamePackages = gameList;

      debugPrint("重载已安装游戏列表成功");
    } catch (e) {
      debugPrint('重载已安装游戏列表失败: $e');
    }
  }

  AppInfo? findLocalPackage(String packageName) {
    try {
      final apps = _installedPackageNames;
      AppInfo result = apps.firstWhere((item) => item.packageName.isEqualTo(packageName));
      return result;
    } catch (e) {
      return null;
    }
  }
}